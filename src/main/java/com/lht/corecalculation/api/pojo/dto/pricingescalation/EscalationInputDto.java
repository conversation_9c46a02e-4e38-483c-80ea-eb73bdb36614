package com.lht.corecalculation.api.pojo.dto.pricingescalation;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class EscalationInputDto {

    private Integer id;
    private String year;
    private Double eparPrices;
    private Double hcMaterialPrices;
    private Double hcSubcontractPrices;
    private Double labourPrices;
    private Double rfpLabour;
    private Double fpNtePrices;
}
