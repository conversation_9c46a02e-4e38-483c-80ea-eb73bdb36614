package com.lht.corecalculation.api.pojo.dto.handlingcharges;

import java.util.List;
import lombok.Data;

@Data
public class Cluster {

    private Double z1;
    private Double z2;
    private Double pma;
    private Double csm;
    private Integer oneItemCap;
    private Integer lineItemCap;
    private Long id;
    private String name;
    private Integer order;
    private String partsType;
    private List<Part> parts;
}
