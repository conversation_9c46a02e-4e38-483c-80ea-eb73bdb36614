package com.lht.corecalculation.api.pojo.dto.pricingescalation;

import com.lht.corecalculation.api.pojo.dto.common.ProgressDto;
import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class EscalationDataDto {

    private List<EscalationsPricingDto> escalationsPricing;
    private Boolean isRfpContractSelected;
    private Boolean canCurrentUserEdit;
    private ProgressDto progress;
}
