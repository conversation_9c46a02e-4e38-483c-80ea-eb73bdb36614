package com.lht.corecalculation.api.pojo.dto.quotation;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.lht.corecalculation.api.enums.BeginQuotationType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class BeginQuotationProgressDto {

    @JsonProperty
    Boolean routineFixedPrices;

    @JsonProperty
    Boolean workscopeNte;

    @JsonProperty
    Boolean workscopeFixedPrice;

    @JsonProperty
    Boolean modularNte;

    @JsonProperty
    Boolean modularFixedPrice;

    @JsonProperty
    Long sourceQuotationId;

    @JsonProperty
    BeginQuotationType beginQuotationType = BeginQuotationType.DEFAULT;

    public BeginQuotationProgressDto withRoutineFixedPrice(Boolean routineFixedPrices) {
        this.routineFixedPrices = routineFixedPrices;
        return this;
    }

    public BeginQuotationProgressDto withWorkscopeFixedPrice(Boolean workscopeFixedPrice) {
        this.workscopeFixedPrice = workscopeFixedPrice;
        if (Boolean.TRUE.equals(workscopeFixedPrice)) {
            this.routineFixedPrices = true;
        }
        return this;
    }

    public BeginQuotationProgressDto withWorkscopeNTE(Boolean workscopeNte) {
        this.workscopeNte = workscopeNte;
        if (Boolean.TRUE.equals(workscopeNte)) {
            this.routineFixedPrices = true;
        }
        return this;
    }

    public BeginQuotationProgressDto withModularNte(Boolean modularNte) {
        this.modularNte = modularNte;
        if (Boolean.TRUE.equals(modularNte)) {
            this.routineFixedPrices = true;
        }
        return this;
    }

    public BeginQuotationProgressDto withModularFixedPrice(Boolean modularFixedPrice) {
        this.modularFixedPrice = modularFixedPrice;
        if (Boolean.TRUE.equals(modularFixedPrice)) {
            this.routineFixedPrices = true;
        }
        return this;
    }

    public BeginQuotationProgressDto withSourceQuotationId(Long sourceQuotationId) {
        this.sourceQuotationId = sourceQuotationId;
        return this;
    }

    public BeginQuotationProgressDto withBeginQuotationType(BeginQuotationType beginQuotationType) {
        this.beginQuotationType = beginQuotationType != null ? beginQuotationType : BeginQuotationType.DEFAULT;
        return this;
    }
}