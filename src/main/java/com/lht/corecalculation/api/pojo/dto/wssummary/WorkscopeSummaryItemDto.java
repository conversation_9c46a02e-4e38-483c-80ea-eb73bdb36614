package com.lht.corecalculation.api.pojo.dto.wssummary;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class WorkscopeSummaryItemDto {

    private String year;
    private Double revenue;
    private Double revenueCap;
    private Double productionCost;
    private Double discount;
    private Double surchargesCost;
    private Double productionCostAfterDiscountAndSurcharges;
    private Double db2;
    private Double db2Percentage;
    private Double ebit;
    private Double ebitPercentage;
    private Double eatPercentage;
    private Double netMargin;
}
