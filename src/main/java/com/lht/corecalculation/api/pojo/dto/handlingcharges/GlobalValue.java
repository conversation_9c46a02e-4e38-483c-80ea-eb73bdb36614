package com.lht.corecalculation.api.pojo.dto.handlingcharges;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class GlobalValue {

    private Double baseCase;
    private Double z1;
    private Double z2;
    private Double pma;
    private Double csm;
    private Integer oneItemCap;
    private Integer lineItemCap;
    private Integer globalCap;
}
