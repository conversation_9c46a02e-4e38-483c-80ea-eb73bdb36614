package com.lht.corecalculation.api.pojo.dto.inner;

import lombok.Data;

@Data
public class User {

    private Long id;
    private String name;
    private String username;
    private String email;

    public User withId(Long id) {
        this.id = id;
        return this;
    }

    public User withName(String name) {
        this.name = name;
        return this;
    }

    public User withUsername(String username) {
        this.username = username;
        return this;
    }

    public User withEmail(String email) {
        this.email = email;
        return this;
    }
}
