package com.lht.corecalculation.api.pojo.dto.inner;

import java.sql.Timestamp;
import java.util.List;
import lombok.Data;

@Data
public class QuotationCover {

    private Integer id;
    private Integer version;
    private Integer position;
    private Integer scenario;
    private String offerNumber;
    private Long contractStart;
    private Long contractEnd;
    private LhtCustomer customer;
    private Engine engine;
    private List<Workscope> workscopes;
    private Timestamp lastUpdate;
    private User owner;
    //progress
    private String status;
    private Double usdExchangeRate;
    private Boolean timeAndMaterial;
    private Boolean routineFixedPrices;
    private Boolean canCurrentUserEdit;
}
