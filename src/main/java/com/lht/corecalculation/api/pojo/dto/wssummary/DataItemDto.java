package com.lht.corecalculation.api.pojo.dto.wssummary;

import java.util.ArrayList;
import java.util.List;
import lombok.Data;

@Data
public class DataItemDto {

    private Integer id;
    private String name;
    private List<WorkscopeSummaryItemDto> includedItems = new ArrayList<>();
    private List<WorkscopeSummaryItemDto> excludedItems = new ArrayList<>();
    private List<WorkscopeSummaryItemDto> totalItems = new ArrayList<>();
}
