package com.lht.corecalculation.api.request.modularpricing;

import com.lht.corecalculation.api.constants.CoCaEndpoint;
import com.zebrunner.carina.api.AbstractApiMethodV2;
import com.zebrunner.carina.api.annotation.Endpoint;
import com.zebrunner.carina.api.http.HttpMethodType;


import static com.lht.corecalculation.api.constants.GlobalConstants.AUTHORIZATION_HEADER;
import static com.lht.corecalculation.api.constants.GlobalConstants.QUOTATION_ID_PLACEHOLDER;

@Endpoint(url = CoCaEndpoint.MODULAR_PRICING, methodType = HttpMethodType.PUT)
public class PutModularPricingRequest extends AbstractApiMethodV2 {

    public PutModularPricingRequest(String id, String body) {
        this.replaceUrlPlaceholder(QUOTATION_ID_PLACEHOLDER, id);
        this.setBodyContent(body);
    }

    public PutModularPricingRequest withBearerToken(String token) {
        this.setHeaders(AUTHORIZATION_HEADER + token);
        return this;
    }
}
