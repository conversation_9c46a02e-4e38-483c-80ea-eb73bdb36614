package com.lht.corecalculation.api.request.user;

import com.zebrunner.carina.api.AbstractApiMethodV2;
import com.zebrunner.carina.api.annotation.Endpoint;
import com.zebrunner.carina.api.http.HttpMethodType;


import static com.lht.corecalculation.api.constants.CoCaEndpoint.USER_DETAILS;
import static com.lht.corecalculation.api.constants.GlobalConstants.AUTHORIZATION_HEADER;

@Endpoint(url = USER_DETAILS, methodType = HttpMethodType.PUT)
public class UpdateUserDetailsRequest extends AbstractApiMethodV2 {

    public UpdateUserDetailsRequest(String... headers) {
        this.setHeaders(headers);
    }

    public UpdateUserDetailsRequest withBearerToken(String token) {
        this.setHeaders(AUTHORIZATION_HEADER + token);
        return this;
    }
}
