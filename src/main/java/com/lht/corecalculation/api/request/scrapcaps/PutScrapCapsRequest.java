package com.lht.corecalculation.api.request.scrapcaps;

import com.zebrunner.carina.api.AbstractApiMethodV2;
import com.zebrunner.carina.api.annotation.Endpoint;
import com.zebrunner.carina.api.http.HttpMethodType;


import static com.lht.corecalculation.api.constants.CoCaEndpoint.SCRAP_CAPS_SAVE;
import static com.lht.corecalculation.api.constants.GlobalConstants.AUTHORIZATION_HEADER;
import static com.lht.corecalculation.api.constants.GlobalConstants.QUOTATION_ID_PLACEHOLDER;

@Endpoint(url = SCRAP_CAPS_SAVE, methodType = HttpMethodType.PUT)
public class PutScrapCapsRequest extends AbstractApiMethodV2 {

    public PutScrapCapsRequest(String quotationId, String body) {
        this.replaceUrlPlaceholder(QUOTATION_ID_PLACEHOLDER, quotationId);
        this.setBodyContent(body);
    }

    public PutScrapCapsRequest withBearerToken(String token) {
        this.setHeaders(AUTHORIZATION_HEADER + token);
        return this;
    }
}