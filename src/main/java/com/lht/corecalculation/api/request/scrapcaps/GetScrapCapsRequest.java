package com.lht.corecalculation.api.request.scrapcaps;

import com.lht.corecalculation.api.constants.CoCaEndpoint;
import com.zebrunner.carina.api.AbstractApiMethodV2;
import com.zebrunner.carina.api.annotation.Endpoint;
import com.zebrunner.carina.api.http.HttpMethodType;


import static com.lht.corecalculation.api.constants.GlobalConstants.AUTHORIZATION_HEADER;
import static com.lht.corecalculation.api.constants.GlobalConstants.QUOTATION_ID_PLACEHOLDER;

@Endpoint(url = CoCaEndpoint.SCRAP_CAPS, methodType = HttpMethodType.GET)
public class GetScrapCapsRequest extends AbstractApiMethodV2 {

    public GetScrapCapsRequest(String id) {
        this.replaceUrlPlaceholder(QUOTATION_ID_PLACEHOLDER, id);
    }

    public GetScrapCapsRequest withBearerToken(String token) {
        this.setHeaders(AUTHORIZATION_HEADER + token);
        return this;
    }
}
