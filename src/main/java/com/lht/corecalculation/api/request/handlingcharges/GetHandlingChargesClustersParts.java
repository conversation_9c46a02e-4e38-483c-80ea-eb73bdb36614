package com.lht.corecalculation.api.request.handlingcharges;

import com.lht.corecalculation.api.pojo.dto.handlingcharges.Cluster;
import com.lht.corecalculation.api.pojo.dto.handlingcharges.HandlingChargesData;
import com.lht.corecalculation.api.pojo.dto.handlingcharges.Part;
import java.util.ArrayList;
import java.util.List;
import lombok.Getter;

public class GetHandlingChargesClustersParts {

    private static String z1;
    private static String z2;
    private static String pma;
    private static String csm;
    @Getter
    private static String oneItemCap;
    @Getter
    private static String lineItemCap;

    public static List<Part> getUpdateClustersAndParts(HandlingChargesData handlingChargesData, String clusterName) {
        List<Part> updatedParts = new ArrayList<>();
        for (Cluster cluster : handlingChargesData.getClusters()) {
            if (clusterName.equals(cluster.getName())) {
                updatedParts.addAll(cluster.getParts());
                return updatedParts;
            }
        }
        return updatedParts;
    }

    public static String getZ1FromParts(HandlingChargesData handlingChargesData, String clusterName, String partName) {
        updateClustersAndParts(handlingChargesData, clusterName, partName);
        return z1;
    }

    public static String getZ2FromParts(HandlingChargesData handlingChargesData, String clusterName, String partName) {
        updateClustersAndParts(handlingChargesData, clusterName, partName);
        return z2;
    }

    public static String getPmaFromParts(HandlingChargesData handlingChargesData, String clusterName, String partName) {
        updateClustersAndParts(handlingChargesData, clusterName, partName);
        return pma;
    }

    public static String getCsmFromParts(HandlingChargesData handlingChargesData, String clusterName, String partName) {
        updateClustersAndParts(handlingChargesData, clusterName, partName);
        return csm;
    }

    public static String getOneItemCapFromParts(HandlingChargesData handlingChargesData, String clusterName, String partName) {
        updateClustersAndParts(handlingChargesData, clusterName, partName);
        return oneItemCap;
    }

    public static String getLineItemCapFromParts(HandlingChargesData handlingChargesData, String clusterName, String partName) {
        updateClustersAndParts(handlingChargesData, clusterName, partName);
        return lineItemCap;
    }

    private static void updateClustersAndParts(HandlingChargesData handlingChargesData, String clusterName, String partName) {
        for (Cluster cluster : handlingChargesData.getClusters()) {
            if (clusterName.equals(cluster.getName())) {
                for (Part part : cluster.getParts()) {
                    if (partName.equals(part.getName())) {
                        z1 = String.valueOf(part.getZ1());
                        z2 = String.valueOf(part.getZ2());
                        pma = String.valueOf(part.getPma());
                        csm = String.valueOf(part.getCsm());
                        oneItemCap = String.valueOf(part.getOneItemCap());
                        lineItemCap = String.valueOf(part.getLineItemCap());
                    }
                }
            }
        }
    }
}

