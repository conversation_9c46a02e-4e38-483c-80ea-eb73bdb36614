package com.lht.corecalculation.api.request.quotation;

import com.lht.corecalculation.api.constants.CoCaEndpoint;
import com.lht.corecalculation.api.enums.EngineType;
import com.lht.corecalculation.api.enums.QuotationSortOption;
import com.lht.corecalculation.api.enums.QuotationStatus;
import com.zebrunner.carina.api.AbstractApiMethodV2;
import com.zebrunner.carina.api.annotation.Endpoint;
import com.zebrunner.carina.api.http.HttpMethodType;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Endpoint(url = CoCaEndpoint.QUOTATIONS, methodType = HttpMethodType.GET)
public class GetQuotationsRequest extends AbstractApiMethodV2 {

    public GetQuotationsRequest withAccessToken(String accessToken) {
        this.setHeaders("Authorization=" + accessToken);
        return this;
    }

    public GetQuotationsRequest withPageSize(Integer pageSizeQueryParam) {
        this.addParameter("pageSize", String.valueOf(pageSizeQueryParam));
        return this;
    }

    public GetQuotationsRequest withPageIndex(Integer pageIndexQueryParam) {
        this.addParameter("pageIndex", String.valueOf(pageIndexQueryParam));
        return this;
    }

    public GetQuotationsRequest withOwnerId(Integer ownerIdQueryParam) {
        this.addParameter("ownerId", String.valueOf(ownerIdQueryParam));
        return this;
    }

    public GetQuotationsRequest withOfferNumber(String offerNumberQueryParam) {
        this.addParameter("offerNumber", offerNumberQueryParam);
        return this;
    }

    public GetQuotationsRequest withVersion(Integer versionQueryParam) {
        this.addParameter("version", String.valueOf(versionQueryParam));
        return this;
    }

    public GetQuotationsRequest withPosition(Integer positionQueryParam) {
        this.addParameter("position", String.valueOf(positionQueryParam));
        return this;
    }

    public GetQuotationsRequest withScenario(Integer scenarioQueryParam) {
        this.addParameter("scenario", String.valueOf(scenarioQueryParam));
        return this;
    }

    public GetQuotationsRequest withStatus(QuotationStatus quotationStatusQueryParam) {
        this.addParameter("quotationStatus", String.valueOf(quotationStatusQueryParam));
        return this;
    }

    public GetQuotationsRequest withEngineType(EngineType engineTypeQueryParam) {
        this.addParameter("engineType", engineTypeQueryParam.asString());
        return this;
    }

    public GetQuotationsRequest withCustomer(String customerQueryParam) {
        this.addParameter("customer", customerQueryParam);
        return this;
    }

    public GetQuotationsRequest sortedBy(QuotationSortOption sortBy) {
        this.addParameter("sortBy", sortBy.toString());
        return this;
    }
}
