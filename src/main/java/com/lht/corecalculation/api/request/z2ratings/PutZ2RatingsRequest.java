package com.lht.corecalculation.api.request.z2ratings;

import com.zebrunner.carina.api.AbstractApiMethodV2;
import com.zebrunner.carina.api.annotation.Endpoint;
import com.zebrunner.carina.api.http.HttpMethodType;


import static com.lht.corecalculation.api.constants.CoCaEndpoint.Z2_RATINGS_SAVE;
import static com.lht.corecalculation.api.constants.GlobalConstants.AUTHORIZATION_HEADER;
import static com.lht.corecalculation.api.constants.GlobalConstants.QUOTATION_ID_PLACEHOLDER;

@Endpoint(url = Z2_RATINGS_SAVE, methodType = HttpMethodType.PUT)
public class PutZ2RatingsRequest extends AbstractApiMethodV2 {

    public PutZ2RatingsRequest(String quotationId, String body) {
        this.replaceUrlPlaceholder(QUOTATION_ID_PLACEHOLDER, quotationId);
        this.setBodyContent(body);
    }

    public PutZ2RatingsRequest withBearerToken(String token) {
        this.setHeaders(AUTHORIZATION_HEADER + token);
        return this;
    }
}
