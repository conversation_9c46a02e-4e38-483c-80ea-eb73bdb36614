package com.lht.corecalculation.api.request.pricingescalation;

import com.lht.corecalculation.api.constants.CoCaEndpoint;
import com.zebrunner.carina.api.AbstractApiMethodV2;
import com.zebrunner.carina.api.annotation.Endpoint;
import com.zebrunner.carina.api.http.HttpMethodType;


import static com.lht.corecalculation.api.constants.GlobalConstants.AUTHORIZATION_HEADER;
import static com.lht.corecalculation.api.constants.GlobalConstants.QUOTATION_ID_PLACEHOLDER;

@Endpoint(url = CoCaEndpoint.ESCALATION_PRICING, methodType = HttpMethodType.PUT)
public class PutPricingEscalationRequest extends AbstractApiMethodV2 {

    public PutPricingEscalationRequest(String id, String body) {
        this.replaceUrlPlaceholder(QUOTATION_ID_PLACEHOLDER, id);
        this.setBodyContent(body);
    }

    public PutPricingEscalationRequest withBearerToken(String token) {
        this.setHeaders(AUTHORIZATION_HEADER + token);
        return this;
    }
}
