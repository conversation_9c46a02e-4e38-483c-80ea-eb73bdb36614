package com.lht.corecalculation.api.request.quotation;

import com.zebrunner.carina.api.AbstractApiMethodV2;
import com.zebrunner.carina.api.annotation.Endpoint;
import com.zebrunner.carina.api.http.HttpMethodType;


import static com.lht.corecalculation.api.constants.CoCaEndpoint.QUOTATION_BY_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.AUTHORIZATION_HEADER;
import static com.lht.corecalculation.api.constants.GlobalConstants.QUOTATION_ID_PLACEHOLDER;

@Endpoint(url = QUOTATION_BY_ID, methodType = HttpMethodType.PUT)
public class PutQuotationByIdRequest extends AbstractApiMethodV2 {

    public PutQuotationByIdRequest(String quotationId, String body) {
        this.replaceUrlPlaceholder(QUOTATION_ID_PLACEHOLDER, quotationId);
        this.setBodyContent(body);
    }

    public PutQuotationByIdRequest withBearerToken(String token) {
        this.setHeaders(AUTHORIZATION_HEADER + token);
        return this;
    }
}


