package com.lht.corecalculation.api.request.quotation;

import com.lht.corecalculation.api.constants.CoCaEndpoint;
import com.zebrunner.carina.api.AbstractApiMethodV2;
import com.zebrunner.carina.api.annotation.Endpoint;
import com.zebrunner.carina.api.http.HttpMethodType;


import static com.lht.corecalculation.api.constants.GlobalConstants.AUTHORIZATION_HEADER;
import static com.lht.corecalculation.api.constants.GlobalConstants.QUOTATION_ID_PLACEHOLDER;

@Endpoint(url = CoCaEndpoint.QUOTATION_BY_ID, methodType = HttpMethodType.GET)
public class GetQuotationByIdRequest extends AbstractApiMethodV2 {

    public GetQuotationByIdRequest(String id) {
        this.replaceUrlPlaceholder(QUOTATION_ID_PLACEHOLDER, id);
    }

    public GetQuotationByIdRequest withBearerToken(String token) {
        this.setHeaders(AUTHORIZATION_HEADER + token);
        return this;
    }
}
