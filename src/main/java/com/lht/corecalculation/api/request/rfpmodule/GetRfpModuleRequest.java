package com.lht.corecalculation.api.request.rfpmodule;

import com.lht.corecalculation.api.constants.CoCaEndpoint;
import com.zebrunner.carina.api.AbstractApiMethodV2;
import com.zebrunner.carina.api.annotation.Endpoint;
import com.zebrunner.carina.api.http.HttpMethodType;


import static com.lht.corecalculation.api.constants.GlobalConstants.AUTHORIZATION_HEADER;
import static com.lht.corecalculation.api.constants.GlobalConstants.QUOTATION_ID_PLACEHOLDER;

@Endpoint(url = CoCaEndpoint.RFP_MODULE, methodType = HttpMethodType.GET)
public class GetRfpModuleRequest extends AbstractApiMethodV2 {

    public GetRfpModuleRequest(String id) {
        this.replaceUrlPlaceholder(QUOTATION_ID_PLACEHOLDER, id);
    }

    public GetRfpModuleRequest withBearerToken(String token) {
        this.setHeaders(AUTHORIZATION_HEADER + token);
        return this;
    }
}