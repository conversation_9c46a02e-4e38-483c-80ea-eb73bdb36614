package com.lht.corecalculation.api.request.user;

import com.zebrunner.carina.api.AbstractApiMethodV2;
import com.zebrunner.carina.api.annotation.Endpoint;
import com.zebrunner.carina.api.http.HttpMethodType;


import static com.lht.corecalculation.api.constants.CoCaEndpoint.USERS;
import static com.lht.corecalculation.api.constants.GlobalConstants.AUTHORIZATION_HEADER;

@Endpoint(url = USERS, methodType = HttpMethodType.GET)
public class GetAllUsersRequest extends AbstractApiMethodV2 {

    public GetAllUsersRequest(String... headers) {
        this.setHeaders(headers);
    }

    public GetAllUsersRequest withBearerToken(String token) {
        this.setHeaders(AUTHORIZATION_HEADER + token);
        return this;
    }
}
