package com.lht.corecalculation.api.request.handlingcharges;

import com.zebrunner.carina.api.AbstractApiMethodV2;
import com.zebrunner.carina.api.annotation.Endpoint;
import com.zebrunner.carina.api.http.HttpMethodType;


import static com.lht.corecalculation.api.constants.CoCaEndpoint.HANDLING_CHARGES_SAVE;
import static com.lht.corecalculation.api.constants.GlobalConstants.AUTHORIZATION_HEADER;
import static com.lht.corecalculation.api.constants.GlobalConstants.QUOTATION_ID_PLACEHOLDER;

@Endpoint(url = HANDLING_CHARGES_SAVE, methodType = HttpMethodType.PUT)
public class PutHandlingChargesRequest extends AbstractApiMethodV2 {

    public PutHandlingChargesRequest(String quotationId, String body) {
        this.replaceUrlPlaceholder(QUOTATION_ID_PLACEHOLDER, quotationId);
        this.setBodyContent(body);
    }

    public PutHandlingChargesRequest withBearerToken(String token) {
        this.setHeaders(AUTHORIZATION_HEADER + token);
        return this;
    }
}
