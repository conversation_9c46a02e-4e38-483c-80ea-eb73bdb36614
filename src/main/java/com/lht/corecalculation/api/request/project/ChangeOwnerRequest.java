package com.lht.corecalculation.api.request.project;

import com.zebrunner.carina.api.AbstractApiMethodV2;
import com.zebrunner.carina.api.annotation.Endpoint;
import com.zebrunner.carina.api.http.HttpMethodType;


import static com.lht.corecalculation.api.constants.CoCaEndpoint.CHANGE_QUOTATION_OWNER_URL;
import static com.lht.corecalculation.api.constants.GlobalConstants.AUTHORIZATION_HEADER;

@Endpoint(url = CHANGE_QUOTATION_OWNER_URL, methodType = HttpMethodType.PATCH)
public class ChangeOwnerRequest extends AbstractApiMethodV2 {

    public ChangeOwnerRequest(String originalQuotationId, String body, String... headers) {
        this.replaceUrlPlaceholder("originalQuotationId", originalQuotationId);
        this.setBodyContent(body);
        this.setHeaders(headers);
    }

    public ChangeOwnerRequest withBearerToken(String token) {
        this.setHeaders(AUTHORIZATION_HEADER + token);
        return this;
    }
}
