package com.lht.corecalculation.api.request.z2ratings;

import com.lht.corecalculation.api.constants.CoCaEndpoint;
import com.zebrunner.carina.api.AbstractApiMethodV2;
import com.zebrunner.carina.api.annotation.Endpoint;
import com.zebrunner.carina.api.http.HttpMethodType;


import static com.lht.corecalculation.api.constants.GlobalConstants.AUTHORIZATION_HEADER;
import static com.lht.corecalculation.api.constants.GlobalConstants.QUOTATION_ID_PLACEHOLDER;

@Endpoint(url = CoCaEndpoint.Z2_RATINGS, methodType = HttpMethodType.GET)
public class GetZ2RatingsRequest extends AbstractApiMethodV2 {

    public GetZ2RatingsRequest(String id) {
        this.replaceUrlPlaceholder(QUOTATION_ID_PLACEHOLDER, id);
    }

    public GetZ2RatingsRequest withBearerToken(String token) {
        this.setHeaders(AUTHORIZATION_HEADER + token);
        return this;
    }
}
