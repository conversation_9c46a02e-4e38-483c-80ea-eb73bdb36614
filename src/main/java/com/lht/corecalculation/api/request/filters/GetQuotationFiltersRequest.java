package com.lht.corecalculation.api.request.filters;

import com.lht.corecalculation.api.constants.CoCaEndpoint;
import com.zebrunner.carina.api.AbstractApiMethodV2;
import com.zebrunner.carina.api.annotation.Endpoint;
import com.zebrunner.carina.api.http.HttpMethodType;
import lombok.NoArgsConstructor;


import static com.lht.corecalculation.api.constants.GlobalConstants.AUTHORIZATION_HEADER;

@NoArgsConstructor
@Endpoint(url = CoCaEndpoint.QUOTATION_FILTERS, methodType = HttpMethodType.GET)
public class GetQuotationFiltersRequest extends AbstractApiMethodV2 {

    public GetQuotationFiltersRequest withBearerToken(String token) {
        this.setHeaders(AUTHORIZATION_HEADER + token);
        return this;
    }
}
