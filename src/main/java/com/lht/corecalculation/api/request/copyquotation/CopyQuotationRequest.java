package com.lht.corecalculation.api.request.copyquotation;

import com.zebrunner.carina.api.AbstractApiMethodV2;
import com.zebrunner.carina.api.annotation.Endpoint;
import com.zebrunner.carina.api.http.HttpMethodType;


import static com.lht.corecalculation.api.constants.CoCaEndpoint.COPY_QUOTATION;
import static com.lht.corecalculation.api.constants.GlobalConstants.AUTHORIZATION_HEADER;

@Endpoint(url = COPY_QUOTATION, methodType = HttpMethodType.PUT)
public class CopyQuotationRequest extends AbstractApiMethodV2 {

    public CopyQuotationRequest(String originalQuotationId, String... headers) {
        this.replaceUrlPlaceholder("originalQuotationId", originalQuotationId);
        this.setHeaders(headers);
    }

    public CopyQuotationRequest withBearerToken(String token) {
        this.setHeaders(AUTHORIZATION_HEADER + token);
        return this;
    }
}
