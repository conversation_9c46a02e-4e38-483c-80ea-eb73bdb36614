package com.lht.corecalculation.api.request.pmarating;

import com.zebrunner.carina.api.AbstractApiMethodV2;
import com.zebrunner.carina.api.annotation.Endpoint;
import com.zebrunner.carina.api.http.HttpMethodType;


import static com.lht.corecalculation.api.constants.CoCaEndpoint.PMA_RATINGS;
import static com.lht.corecalculation.api.constants.GlobalConstants.AUTHORIZATION_HEADER;
import static com.lht.corecalculation.api.constants.GlobalConstants.QUOTATION_ID_PLACEHOLDER;

@Endpoint(url = PMA_RATINGS, methodType = HttpMethodType.PUT)
public class PutPmaRatingsRequest extends AbstractApiMethodV2 {

    public PutPmaRatingsRequest(String quotationId, String body) {
        this.replaceUrlPlaceholder(QUOTATION_ID_PLACEHOLDER, quotationId);
        this.setBodyContent(body);
    }

    public PutPmaRatingsRequest withBearerToken(String token) {
        this.setHeaders(AUTHORIZATION_HEADER + token);
        return this;
    }
}
