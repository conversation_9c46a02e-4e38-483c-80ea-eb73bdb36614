package com.lht.corecalculation.api.request.modularpricing;

import com.lht.corecalculation.api.constants.CoCaEndpoint;
import com.zebrunner.carina.api.AbstractApiMethodV2;
import com.zebrunner.carina.api.annotation.Endpoint;
import com.zebrunner.carina.api.http.HttpMethodType;


import static com.lht.corecalculation.api.constants.GlobalConstants.AUTHORIZATION_HEADER;
import static com.lht.corecalculation.api.constants.GlobalConstants.QUOTATION_ID_PLACEHOLDER;

@Endpoint(url = CoCaEndpoint.MODULAR_PRICING, methodType = HttpMethodType.GET)
public class GetModularPricingRequest extends AbstractApiMethodV2 {

    public GetModularPricingRequest(String id) {
        this.replaceUrlPlaceholder(QUOTATION_ID_PLACEHOLDER, id);
    }

    public GetModularPricingRequest withBearerToken(String token) {
        this.setHeaders(AUTHORIZATION_HEADER + token);
        return this;
    }
}
