package com.lht.corecalculation.api.constants;

import com.zebrunner.carina.utils.R;
import java.time.format.DateTimeFormatter;

public class GlobalConstants {

    //region SSO
    public static final String NON_PROD_SSO_URL = String.format(
            "%s/auth/realms/%s/protocol/openid-connect/token",
            R.TESTDATA.get("sso_server"),
            R.TESTDATA.get("sso_realm"));
    //endregion

    //region Error Messages
    public static final String RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE = "Response status code mismatch";
    public static final String GENERIC_ERROR_DESCRIPTION_MISMATCH_AS_MESSAGE = "Error description mismatch";
    public static final String GENERIC_ERROR_MESSAGE_MISMATCH_AS_MESSAGE = "Error message mismatch";
    public static final String ERROR_STATUS_MISMATCH_AS_MESSAGE = "Error status code mismatch";
    public static final String ERROR_TYPE_MISMATCH_AS_MESSAGE = "Error type mismatch";
    public static final String ERROR_DETAILS_MISMATCH_AS_MESSAGE = "Error details mismatch";
    public static final String RESPONSE_QUOTATIONS_ORDERING_MISMATCH_AS_MESSAGE = "Response quotations ordering mismatch";
    public static final String RESPONSE_QUOTATIONS_LIST_SIZE_MISMATCH_AS_MESSAGE = "Response quotation list size mismatch";
    public static final String RESPONSE_TOTAL_PAGES_MISMATCH_AS_MESSAGE = "Response total pages mismatch";
    public static final String RESPONSE_TOTAL_QUOTATIONS_COUNT_MISMATCH_AS_MESSAGE = "Response total quotations count mismatch";
    public static final String RESPONSE_USERS_COUNT_MISMATCH_AS_MESSAGE = "Response users count mismatch";
    public static final String RESPONSE_USER_IDS_NOT_UNIQUE_AS_MESSAGE = "Response user ids not unique";
    public static final String RESPONSE_USER_ID_NOT_FOUND_IN_DB_AS_MESSAGE_FORMAT = "Response user id %d not found in DB";
    public static final String UPDATE_QUERY_ERROR_MESSAGE = "Something went wrong with the update query";
    public static final String PROPERTY_DOES_NOT_MATCH_AS_MESSAGE_FORMAT = "%s does not match";
    public static final String RESPONSE_DATA_NULL_AS_MESSAGE = "Data is null in the response.";
    public static final String CLUSTER_ORDER_MISMATCH_AS_MESSAGE = "Cluster order mismatch!";
    public static final String CLUSTER_PART_ORDER_MISMATCH_AS_MESSAGE = "Cluster Part order mismatch!";
    public static final String PERMISSIONS_NOT_MATCHING_AS_MESSAGE_FORMAT = "Permissions for username %s do not match those expected for resource %s";
    public static final String PERMISSIONS_FOR_RESOURCE_NOT_PRESENT_AS_MESSAGE_FORMAT = "Permissions for resource %s not present";
    public static final String DIFFERENCE_BETWEEN_ACTUAL_AND_EXPECTED_PRICE = "Difference between actual and expected price";
    public static final String WORKSCOPE_NOT_FOUND = "Target workscope with the specified name not found.";
    public static final String DBII_IN_TARGET_WORKSCOPE_NOT_FOUND = "DBII value mismatch for target workscope.";
    public static final String INVALID_REQUEST_CONTENT = "Invalid request content.";
    public static final String UNIQUE_CAPS_DO_NOT_MATCH_NULL = "Unique caps do not match expected value of null.";
    public static final String USER_DOESNT_HAVE_REQUIRED_PERMISSIONS = "User doesn't have required permissions";
    public static final String LABOUR_PRICES_VALUE_MISMATCH = "LabourPrices values mismatch";
    public static final String EPAR_PRICES_VALUE_MISMATCH = "EparPrice values mismatch";
    public static final String RFP_LABOUR_VALUE_MISMATCH = "RfpLabour values mismatch";
    public static final String WORKSCOPE_PRICES_VALUE_MISMATCH = "Workscope Prices value mismatch";
    public static final String HC_MATERIAL_PRICES_VALUE_MISMATCH = "HcMaterialPrices values mismatch";
    public static final String HC_SUBCONTRACT_PRICES_VALUE_MISMATCH = "HcSubcontractPrices values mismatch";
    public static final String USER_DOES_NOT_MATCH_PROJECT_OWNER = "Save Escalation Pricing not allowed. Current user does not match the project owner";
    public static final String INVALID_CAP_CONFIGURATION_PART_NAME = "Invalid cap configuration for Part with Name: %s, Quantity: %d, SingleItemCap: %s, LineItemCap: %s";
    public static final String ATTRIBUTE_DO_NOT_MATCH_FOR_WORKSCOPE = "%s do not match for workscope: %s";
    public static final String FAILED_TO_FETCH_WORKSCOPE_SUMMARY = "Failed to fetch workscope summary";
    public static final String FAILED_TO_CHECK_CALCULATION_STATE = "Failed to check calculation state.";
    public static final String FAILED_TO_SUBMIT_WORKSCOPE_UPDATE = "Failed to submit workscope update.";
    public static final String WORKSCOPE_CALCULATION_NOT_COMPLETE = "Workscope calculation did not complete within expected timeframe.";
    public static final String ERROR_MESSAGE_LIST_SIZE_MISMATCH = "Lists do not have the same size.";
    public static final String ERROR_MESSAGE_VALUE_MISMATCH_WITH_TOLERANCE = "Values at index %d do not match within tolerance: expected %f but was %f. %s";
    public static final String ERROR_MESSAGE_CLP_VALUES_MISMATCH = "The sizes of clpValues, scrapCapInputs and partIds lists must match";
    public static final String MESSAGE_EXPECTED_SCRAP_CAP_INPUT_TO_BE_ZERO = "Expected scrapCapInput to be 0.0 when clp > clpThreshold for part ID: ";
    public static final String MESSAGE_EXPECTED_SCRAP_CAP_INPUT_TO_MATCH_CUSTOM = "Expected scrapCapInput to match the customScrapCapValue for part ID: ";
    public static final String MESSAGE_NOT_ALL_IS_EXCLUDED_VALUES_ARE_FALSE = "Not all isExcluded values are false";
    public static final String MESSAGE_NOT_ALL_IS_EXCLUDED_VALUES_ARE_TRUE = "Not all isExcluded values are true";
    public static final String ERROR_MESSAGE_NOT_ALL_PARTS_HAS_EXPECTED_COMMITMENT_LETTER = "Not all parts has expected commitment letter";
    public static final String ERROR_MESSAGE_FOR_QUATATION_COPY_LIMIT = "The maximum number of allowed copies [10] has been exceeded";
    public static final String USER_CANNOT_EDIT_QUOTATION_WITH_ID = "User [%s] cannot edit quotation with ID: [%s]";
    public static final String USER_WITH_ID_NOT_FOUND = "User with id: [%s] is not found";
    public static final String READ_ONLY_USER_NOT_ALLOWED_TO_CHANGE_OWNERSHIP = "Read-only users are not allowed to change ownership";
    public static final String VIEW_ONLY_USER_CANNOT_MODIFY_DATA = "View-only user cannot modify data";
    public static final String QUOTATION_OWNERSHIP_CHANGED = "The quotation ownership is changed. Your changes are discarded.";
    // Expected error bodies
    public static final String COPY_LIMIT_EXCEEDED_BODY = "The maximum number of allowed copies";
    //endregion

    //region OTHER
    public static final String JSON_FILES_PATH = "src/test/resources/logs/";
    public static final DateTimeFormatter DTF = DateTimeFormatter.ofPattern("yy-MM-dd HH:mm:ss");
    public static final String OUTPUT_FILE_NOT_CREATED_EX_MESSAGE = "Mapper output file could not be created";
    //endregion

    //region Users contextKey as String
    public static final String ADMIN_TOKEN = "adminToken";
    public static final String VIEW_ONLY_TOKEN = "viewOnlyToken";
    public static final String USER_1_TOKEN = "calcOneToken";
    public static final String USER_2_TOKEN = "calcTwoToken";
    public static final String USER_3_TOKEN = "calcThreeToken";
    public static final String NO_ROLE_TOKEN = "noRoleToken";
    public static final String ACCESS_TOKEN_1 = "accessToken1";
    public static final String ADMIN_USER_USERNAME = "admin_user_username";
    public static final String ADMIN_USER_PASSWORD = "admin_user_password";
    //endregion

    //region Quotations ids
    public static final String ENGINE_V2500_QUOTATION_ID = "2";
    public static final String ENGINE_CFM56_5B_QUOTATION_ID = "4";
    public static final String ENGINE_CFM56_7B_QUOTATION_ID = "5";
    public static final String ENGINE_LEAP_1B_QUOTATION_ID = "3";
    public static final String ENGINE_LEAP_1A_QUOTATION_ID = "1";
    public static final String ENGINE_CFM56_5B_PMA_QUOTATION_ID = "6";
    //endregion

    //region Response status codes
    public static final String STATUS_CODE = " HTTP Status Code: ";
    public static final String BAD_REQUEST_ERROR_MESSAGE = "Bad Request";
    public static final String FORBIDDEN = "Forbidden";
    public static final String UNAUTHORIZED = "Unauthorized";
    public static final int RESPONSE_STATUS_CODE_200 = 200;
    public static final int RESPONSE_STATUS_CODE_400 = 400;
    public static final int RESPONSE_STATUS_CODE_401 = 401;
    public static final int RESPONSE_STATUS_CODE_403 = 403;
    public static final int RESPONSE_STATUS_CODE_404 = 404;
    public static final int RESPONSE_STATUS_CODE_500 = 500;
    //endregion

    //region PLACEHOLDER
    public static final String QUOTATION_ID_PLACEHOLDER = "quotationId";
    public static final String AUTHORIZATION_HEADER = "Authorization=";
    public static final String ERROR_DETAIL = "error.detail";
    public static final String ERROR_TYPE = "error.type";
    public static final String ERROR_NOT_FOUND = "Not Found";
    public static final String ERROR_QUOTATION_NOT_FOUND = "quotation with id: [%d] is not found";
    public static final String ERROR_QUOTATION_NOT_FOUND_UPPERCASE = "Quotation with id: [%d] is not found";
    public static final String COPY_QUOTATION_NOT_ALLOWED = "Copy quotation not allowed.";
    //endregion

    //region Labour Rates
    public static final String ROUTINE_LABOUR_RATES = "data.labourRate.routineLabourRate";
    public static final String NON_ROUTINE_LABOUR_RATES = "data.labourRate.nonRoutineLabourRate";
    public static final String EPAR_DISCOUNT = "data.labourRate.eparDiscount";
    public static final String DATA_OPERATION_PROGRESS = "data.operationProgress";
    public static final double PERCENTAGE_DIVISOR = 100.0;
    public static final double TOLERANCE = 0.01;
    public static final double BIG_TOLERANCE = 1.0;
    public static final long TIMEOUT_MS = 240_000; // 240 seconds
    public static final long POLL_INTERVAL_MS = 5_000; // 5 seconds
    public static final String OFFER_NUMBER_V2500 = "5100253";
    public static final String ROUTINE_MATERIAL = "Routine Material";
    public static final String REPAIR_MATERIAL = "Repair Material";
    public static final String PARTS_PACKAGES = "Parts Packages";
    public static final String PARTS_PACKAGE = "Parts Package";
    public static final String COMPONENTS = "Components";
    public static final String KITS = "Kits";
    public static final String OTHER = "Other";
    public static final String PART_TYPE_NON_ROUTINE_MATERIAL = "NON_ROUTINE_MATERIAL";
    public static final String PART_TYPE_ROUTINE_MATERIAL = "ROUTINE_MATERIAL";
    public static final String PART_TYPE_PARTS_PACKAGE = "PARTS_PACKAGE";
    public static final String PART_TYPE_COMPONENT = "COMPONENT";
    public static final String PART_TYPE_KIT = "KIT";
    public static final String LPT_SHROUD_SEGMENTS = "LPT Shroud Segments";
    public static final String FAN_BLADE = "Fan Blade";
    public static final String HPC_VANES = "HPC Vanes";
    public static final String HPC_VANE = "HPC Vane";
    public static final String LPT_BLADES = "LPT Blades";
    public static final String MAIN_BEARINGS = "Main Bearings";
    public static final String LPT_NOZZLES_STG_2_4 = "LPT Nozzles Stg. 2 - 4";
    //endregion

    //region Workscopes
    public static final String CPR = "CPR";
    public static final String HPC = "HPC";
    public static final String HPT_S1B = "HPT_S1B";
    public static final String HPT_LLP = "HPT_LLP";
    public static final String NSV = "NSV";

    public static final String WS03_1 = "WS03.1";
    public static final String WS01 = "WS01";
    public static final String WS04 = "WS04";
    public static final String WS06 = "WS06";
    public static final String WS05 = "WS05";
    public static final String WS03 = "WS03";
    public static final String WS07 = "WS07";
    public static final String WS08 = "WS08";
    public static final String WS09 = "WS09";
    public static final String WS02 = "WS02";
    public static final String WS10 = "WS10";

    public static final String WS1 = "WS1";
    public static final String WS2 = "WS2";

    public static final String C_N_B = "C-N-B";
    public static final String C_N_A = "C-N-A";
    public static final String C_N_B_K = "C-N-B_K";
    public static final String C_N_A_K = "C-N-A_K";

    public static final String LEVEL_00 = "LEVEL 00";
    public static final String LEVEL_01 = "LEVEL 01";
    public static final String LEVEL_02 = "LEVEL 02";
    public static final String LEVEL_03 = "LEVEL 03";

    public static final String L3 = "L3";
    //endregion

    //region Scrap Caps
    public static final Float VALID_SCRAP_CAP_VALUE = 10F;
    public static final Integer VALID_CLP_THRESHOLD_VALUE = 50000;
    //endregion

    //region Workscope Summary
    public static final String PRODUCTION_COST = "Production Cost";
    public static final String DISCOUNT = "Discount";
    public static final String REVENUE = "Revenue";
    public static final String SURCHARGES_COST = "Surcharges Cost";
    public static final String PRODUCTION_COST_AFTER_DISCOUNTS_AND_SURCHARGES = "Production Cost after Discounts and Surcharges";
    public static final String DB2 = "DB2";
    public static final String DB2_PERCENTAGE = "DB2 Percentage";
    public static final String EBIT = "EBIT";
    public static final String EBIT_PERCENTAGE = "EBIT Percentage";
    public static final String EAT_PERCENTAGE = "EAT Percentage";
    public static final String NET_MARGIN = "Net Margin";
    //endregion
}
