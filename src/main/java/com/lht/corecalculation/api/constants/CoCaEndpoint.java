package com.lht.corecalculation.api.constants;

public class CoCaEndpoint {

    public static final String SEARCH_ENGINE_PAGE = "/pricing-tool";
    private static final String BASE_URL = "${config.env.api_url_base}";

    //Modular Pricing
    public static final String MODULAR_PRICING = BASE_URL + "/quotations/${quotationId}/modular-pricing";

    //PMA Rating
    public static final String PMA_RATINGS = BASE_URL + "/quotations/${quotationId}/pma-ratings";

    //Commitment Letter
    public static final String COMMITMENT_LETTER = BASE_URL + "/quotations/${quotationId}/commitment-letter";

    //Workscope Summary
    public static final String WORKSCOPE_CALCULATION_STATE = BASE_URL + "/quotations/${quotationId}/calculation-state";
    public static final String WORKSCOPE_SUMMARY = BASE_URL + "/quotations/${quotationId}/summary";

    //Escalation Pricing
    public static final String ESCALATION_PRICING = BASE_URL + "/quotations/${quotationId}/escalations";

    //Subcontract Pricing
    public static final String SUBCONTRACT_PRICING = BASE_URL + "/quotations/${quotationId}/subcontracts";

    //RFP Module
    public static final String RFP_MODULE = BASE_URL + "/quotations/${quotationId}/rlfp-module";

    //RFP Engine
    public static final String RFP_ENGINE = BASE_URL + "/quotations/${quotationId}/rlfp-engine";

    //Labour Rates
    public static final String LABOUR_RATES = BASE_URL + "/quotations/${quotationId}/labour-rates";
    public static final String LABOUR_RATES_SAVE = BASE_URL + "/quotations/${quotationId}/labour-rates";

    //Z2 Rating
    public static final String Z2_RATINGS = BASE_URL + "/quotations/${quotationId}/usm-ratings";
    public static final String Z2_RATINGS_SAVE = BASE_URL + "/quotations/${quotationId}/usm-ratings";

    //Scrap Caps
    public static final String SCRAP_CAPS = BASE_URL + "/quotations/${quotationId}/scrap-caps";
    public static final String SCRAP_CAPS_SAVE = BASE_URL + "/quotations/${quotationId}/scrap-caps";

    //Repair Exclusions
    public static final String REPAIR_EXCLUSIONS = BASE_URL + "/quotations/${quotationId}/repair-exclusions";

    //Handling Charges
    public static final String HANDLING_CHARGES = BASE_URL + "/quotations/${quotationId}/handling-charges";
    public static final String HANDLING_CHARGES_SAVE = BASE_URL + "/quotations/${quotationId}/handling-charges";

    //Quotation
    public static final String QUOTATIONS = BASE_URL + "/quotations";
    public static final String CHANGE_QUOTATION_OWNER_URL = BASE_URL + "/quotations/${originalQuotationId}/owner";
    public static final String QUOTATION_BY_ID = BASE_URL + "/quotations/${quotationId}";
    public static final String COPY_QUOTATION = BASE_URL + "/quotations/${originalQuotationId}/copy";

    //Filters
    public static final String QUOTATION_FILTERS = BASE_URL + "/filters/quotations";

    //User
    public static final String USERS = BASE_URL + "/users";
    public static final String USER_DETAILS = BASE_URL + "/users/details";
}
