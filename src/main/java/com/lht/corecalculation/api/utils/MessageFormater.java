package com.lht.corecalculation.api.utils;

import com.lht.corecalculation.api.enums.TestUser;
import static com.lht.corecalculation.api.constants.GlobalConstants.USER_CANNOT_EDIT_QUOTATION_WITH_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.VIEW_ONLY_USER_CANNOT_MODIFY_DATA;

public class MessageFormater {

    public static String cannotEditQuotationMessage(TestUser user, String quotationId) {
        return String.format(
                USER_CANNOT_EDIT_QUOTATION_WITH_ID,
                user.getUNumber().toLowerCase(),
                quotationId
        );
    }

    public static String viewOnlyUserCannotModifyData(TestUser user, String quotationId) {
        return String.format(
                VIEW_ONLY_USER_CANNOT_MODIFY_DATA,
                user.getUNumber().toLowerCase(),
                quotationId
        );
    }
}