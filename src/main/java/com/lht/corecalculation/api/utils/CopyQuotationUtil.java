package com.lht.corecalculation.api.utils;

import com.lht.corecalculation.api.enums.QuotationStatus;
import com.lht.corecalculation.api.pojo.dto.inner.QuotationCover;
import com.lht.corecalculation.base.cp.utils.Utils;
import org.testng.asserts.SoftAssert;

public class CopyQuotationUtil {

    public static Utils utils = new Utils();

    public static void compareOriginalAndCopyQuotation(QuotationCover originalQuotation, QuotationCover copyQuotation) {
        var soft = new SoftAssert();
        soft.assertEquals(copyQuotation.getEngine().getName(), originalQuotation.getEngine().getName());
        soft.assertEquals(copyQuotation.getOfferNumber(), originalQuotation.getOfferNumber());
        soft.assertEquals(copyQuotation.getOwner().getName(), originalQuotation.getOwner().getName());
        soft.assertEquals(copyQuotation.getPosition(), originalQuotation.getPosition());
        soft.assertEquals(copyQuotation.getVersion(), originalQuotation.getVersion());
        soft.assertEquals(copyQuotation.getScenario(), originalQuotation.getScenario());
        soft.assertEquals(copyQuotation.getCustomer(), originalQuotation.getCustomer());
        soft.assertEquals(copyQuotation.getStatus(), QuotationStatus.ANKA_VALIDATED.name());
        soft.assertAll();
    }
}