package com.lht.corecalculation.api.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.lht.corecalculation.api.enums.PartType;
import com.lht.corecalculation.api.pojo.dto.commitmentletter.CommitmentLetterPartDto;
import com.lht.corecalculation.api.pojo.dto.commitmentletter.CommitmentLetterRequestDto;
import com.lht.corecalculation.api.pojo.dto.commitmentletter.CommitmentLetterResponseDto;
import com.lht.corecalculation.api.request.commitmentletter.GetCommitmentLetterRequest;
import com.lht.corecalculation.api.request.commitmentletter.PutCommitmentLetterRequest;
import com.lht.corecalculation.base.BaseCocaApiTest;
import com.lht.corecalculation.base.cp.utils.Utils;
import io.restassured.response.Response;
import java.util.List;


import static java.util.stream.Collectors.toList;

public class CommitmentLetterUtil extends BaseCocaApiTest {

    public static Utils utils = new Utils();

    public static Response getCommitmentLetterResponse(String quotationId, String token) {
        GetCommitmentLetterRequest getCommitmentLetterRequest =
                new GetCommitmentLetterRequest(quotationId)
                        .withBearerToken(token);

        return getCommitmentLetterRequest.callAPI();
    }

    public static Response putCommitmentLetterRequest(
            String quotationId,
            String token,
            CommitmentLetterResponseDto commitmentLetterResponseDto
    )
            throws JsonProcessingException {
        PutCommitmentLetterRequest putCommitmentLetterRequest = new PutCommitmentLetterRequest(quotationId,
                utils.convert.dtoToJsonString(mapResponseToRequest(commitmentLetterResponseDto)))
                .withBearerToken(token);
        return putCommitmentLetterRequest.callAPI();
    }

    public static List<CommitmentLetterPartDto> getPartsByType(CommitmentLetterResponseDto response, PartType partType) {
        return response.getData().getParts().stream()
                .filter(part -> partType.equals(part.getType()))
                .collect(toList());
    }

    public static CommitmentLetterResponseDto updatePartsToHasCommitmentLetter(
            CommitmentLetterResponseDto response,
            PartType partType
    ) {
        List<CommitmentLetterPartDto> updatedParts = response.getData().getParts().stream()
                .map(part -> {
                    if (partType.name().equals(part.getType())) {
                        part.setHasCommitmentLetter(true);
                    }
                    return part;
                })
                .collect(toList());

        response.getData().setParts(updatedParts);
        return response;
    }

    // Convert CommitmentLetterResponseDto to CommitmentLetterRequestDto
    public static CommitmentLetterRequestDto mapResponseToRequest(CommitmentLetterResponseDto response) {
        List<CommitmentLetterPartDto> partDtos = response.getData().getParts().stream()
                .map(part -> CommitmentLetterPartDto.builder()
                        .id(part.getId())
                        .name(part.getName())
                        .type(part.getType())
                        .hasCommitmentLetter(part.getHasCommitmentLetter())
                        .build())
                .collect(toList());

        return CommitmentLetterRequestDto.builder()
                .parts(partDtos)
                .build();
    }

    public static CommitmentLetterResponseDto resetAllPartsHasCommitmentLetter(CommitmentLetterResponseDto response) {
        List<CommitmentLetterPartDto> updatedParts = response.getData().getParts().stream()
                .map(part -> {
                    part.setHasCommitmentLetter(false);
                    return part;
                })
                .collect(toList());

        response.getData().setParts(updatedParts);
        return response;
    }

    public static boolean validatePartsHasCommitmentLetter(CommitmentLetterResponseDto response, PartType partType) {
        return response.getData().getParts().stream()
                .filter(part -> partType.name().equals(part.getType()))
                .allMatch(CommitmentLetterPartDto::getHasCommitmentLetter);
    }

    public static boolean validateAllPartsHasCommitmentLetter(CommitmentLetterResponseDto response, boolean expectedValue) {
        return response.getData().getParts().stream()
                .allMatch(part -> part.getHasCommitmentLetter() == expectedValue);
    }
}
