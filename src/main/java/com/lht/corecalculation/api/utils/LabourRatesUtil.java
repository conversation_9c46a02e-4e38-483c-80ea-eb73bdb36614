package com.lht.corecalculation.api.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.lht.corecalculation.api.pojo.dto.labourrates.LabourRateDto;
import com.lht.corecalculation.api.request.labourrates.GetLabourRates;
import com.lht.corecalculation.api.request.labourrates.PutLabourRates;
import com.lht.corecalculation.base.BaseCocaApiTest;
import com.lht.corecalculation.base.cp.utils.Utils;
import io.restassured.response.Response;
import org.testng.Assert;


import static com.lht.corecalculation.api.constants.GlobalConstants.EPAR_DISCOUNT;
import static com.lht.corecalculation.api.constants.GlobalConstants.NON_ROUTINE_LABOUR_RATES;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_200;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.ROUTINE_LABOUR_RATES;

public class LabourRatesUtil extends BaseCocaApiTest {

    public static Utils utils = new Utils();

    public static String updateLabourRates(
            Integer routineLabourRateValue, Integer nonRoutineLabourRateValue, Double eparDiscountValue
    ) throws JsonProcessingException {
        LabourRateDto labourRateInput = new LabourRateDto(routineLabourRateValue, nonRoutineLabourRateValue, eparDiscountValue);
        return utils.convert.dtoToJsonString(labourRateInput);
    }

    public static void verifyLabourRatesValidInputs(
            Integer routineLabourRateValue, Integer nonRoutineLabourRateValue, Double eparDiscountValue,
            String quotationId, String accessToken
    ) throws JsonProcessingException {
        GetLabourRates getLabourRates = new GetLabourRates(quotationId).withBearerToken(accessToken);
        Response getResponse = getLabourRates.callAPI();
        Assert.assertEquals(getResponse.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        String labourRatesInputValues = updateLabourRates(routineLabourRateValue, nonRoutineLabourRateValue, eparDiscountValue);

        PutLabourRates putLabourRates = new PutLabourRates(quotationId, labourRatesInputValues)
                .withBearerToken(accessToken);
        Response putResponse = putLabourRates.callAPI();
        Assert.assertEquals(putResponse.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        GetLabourRates getLabourRatesUpdated = new GetLabourRates(quotationId)
                .withBearerToken(accessToken);
        Response getUpdatedResponse = getLabourRatesUpdated.callAPI();
        Assert.assertEquals(getUpdatedResponse.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        Integer actualRoutineLabourRateValue = getUpdatedResponse.getBody().jsonPath().get(ROUTINE_LABOUR_RATES);
        Integer actualNonRoutineLabourRateValue = getUpdatedResponse.getBody().jsonPath().get(NON_ROUTINE_LABOUR_RATES);
        String actualEparDiscountValue = getUpdatedResponse.getBody().jsonPath().get(EPAR_DISCOUNT).toString();

        Assert.assertEquals(actualRoutineLabourRateValue, routineLabourRateValue);
        Assert.assertEquals(actualNonRoutineLabourRateValue, nonRoutineLabourRateValue);
        Assert.assertEquals(actualEparDiscountValue, String.valueOf(eparDiscountValue));
    }
}
