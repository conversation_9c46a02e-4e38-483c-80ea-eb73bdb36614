package com.lht.corecalculation.api.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.lht.corecalculation.api.enums.CalculationProgress;
import com.lht.corecalculation.api.enums.PartType;
import com.lht.corecalculation.api.enums.WorkscopeItemType;
import com.lht.corecalculation.api.pojo.dto.commitmentletter.CommitmentLetterResponseDto;
import com.lht.corecalculation.api.pojo.dto.handlingcharges.HandlingChargesData;
import com.lht.corecalculation.api.pojo.dto.handlingcharges.ResultDto;
import com.lht.corecalculation.api.pojo.dto.modularpricing.ModularPricingInputDto;
import com.lht.corecalculation.api.pojo.dto.modularpricing.ModularPricingRequestDto;
import com.lht.corecalculation.api.pojo.dto.modularpricing.ModularPricingResponseDto;
import com.lht.corecalculation.api.pojo.dto.pmarating.PmaRatingInputDto;
import com.lht.corecalculation.api.pojo.dto.pmarating.PmaResponseDto;
import com.lht.corecalculation.api.pojo.dto.pricingescalation.EscalationInputRequestDto;
import com.lht.corecalculation.api.pojo.dto.pricingescalation.EscalationResponseDto;
import com.lht.corecalculation.api.pojo.dto.repairexclusions.RepairExclusionWorkscopeDataDto;
import com.lht.corecalculation.api.pojo.dto.repairexclusions.RepairExclusionsRequestDto;
import com.lht.corecalculation.api.pojo.dto.rfpengine.RfpEngineResponseDto;
import com.lht.corecalculation.api.pojo.dto.rfpengine.RfpItemInputDto;
import com.lht.corecalculation.api.pojo.dto.scrapcaps.ScrapCapInputDto;
import com.lht.corecalculation.api.pojo.dto.scrapcaps.ScrapCapResponseDto;
import com.lht.corecalculation.api.pojo.dto.scrapcaps.ScrapCapsRequestDto;
import com.lht.corecalculation.api.pojo.dto.wssummary.WorkscopeSummaryItemDto;
import com.lht.corecalculation.api.pojo.dto.wssummary.WsSummaryResponseDto;
import com.lht.corecalculation.api.pojo.dto.z2rating.ResponseDto;
import com.lht.corecalculation.api.pojo.dto.z2rating.Z2RatingInputDto;
import com.lht.corecalculation.api.request.handlingcharges.PutHandlingChargesRequest;
import com.lht.corecalculation.api.request.modularpricing.GetModularPricingRequest;
import com.lht.corecalculation.api.request.modularpricing.PutModularPricingRequest;
import com.lht.corecalculation.api.request.wssummary.CalculationStateRequest;
import com.lht.corecalculation.api.request.wssummary.GetWorkscopeSummaryRequest;
import com.lht.corecalculation.api.request.wssummary.PostWorkscopeSummaryRequest;
import com.lht.corecalculation.base.BaseCocaApiTest;
import com.lht.corecalculation.base.cp.utils.Utils;
import io.restassured.response.Response;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.testng.Assert;


import static com.lht.corecalculation.api.constants.GlobalConstants.ATTRIBUTE_DO_NOT_MATCH_FOR_WORKSCOPE;
import static com.lht.corecalculation.api.constants.GlobalConstants.BIG_TOLERANCE;
import static com.lht.corecalculation.api.constants.GlobalConstants.DATA_OPERATION_PROGRESS;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_CFM56_5B_PMA_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_CFM56_5B_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_CFM56_7B_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_LEAP_1A_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_LEAP_1B_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_V2500_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ERROR_DETAIL;
import static com.lht.corecalculation.api.constants.GlobalConstants.ERROR_MESSAGE_LIST_SIZE_MISMATCH;
import static com.lht.corecalculation.api.constants.GlobalConstants.ERROR_MESSAGE_VALUE_MISMATCH_WITH_TOLERANCE;
import static com.lht.corecalculation.api.constants.GlobalConstants.FAILED_TO_CHECK_CALCULATION_STATE;
import static com.lht.corecalculation.api.constants.GlobalConstants.FAILED_TO_FETCH_WORKSCOPE_SUMMARY;
import static com.lht.corecalculation.api.constants.GlobalConstants.FAILED_TO_SUBMIT_WORKSCOPE_UPDATE;
import static com.lht.corecalculation.api.constants.GlobalConstants.FAN_BLADE;
import static com.lht.corecalculation.api.constants.GlobalConstants.LPT_BLADES;
import static com.lht.corecalculation.api.constants.GlobalConstants.LPT_NOZZLES_STG_2_4;
import static com.lht.corecalculation.api.constants.GlobalConstants.MESSAGE_NOT_ALL_IS_EXCLUDED_VALUES_ARE_FALSE;
import static com.lht.corecalculation.api.constants.GlobalConstants.MESSAGE_NOT_ALL_IS_EXCLUDED_VALUES_ARE_TRUE;
import static com.lht.corecalculation.api.constants.GlobalConstants.POLL_INTERVAL_MS;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_200;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.STATUS_CODE;
import static com.lht.corecalculation.api.constants.GlobalConstants.TIMEOUT_MS;
import static com.lht.corecalculation.api.constants.GlobalConstants.WORKSCOPE_CALCULATION_NOT_COMPLETE;
import static com.lht.corecalculation.api.utils.CommitmentLetterUtil.getCommitmentLetterResponse;
import static com.lht.corecalculation.api.utils.CommitmentLetterUtil.putCommitmentLetterRequest;
import static com.lht.corecalculation.api.utils.CommitmentLetterUtil.resetAllPartsHasCommitmentLetter;
import static com.lht.corecalculation.api.utils.CommitmentLetterUtil.updatePartsToHasCommitmentLetter;
import static com.lht.corecalculation.api.utils.HandlingChargesUtil.getHandlingCharges;
import static com.lht.corecalculation.api.utils.HandlingChargesUtil.updateClustersAndParts;
import static com.lht.corecalculation.api.utils.LabourRatesUtil.verifyLabourRatesValidInputs;
import static com.lht.corecalculation.api.utils.PmaRatingsUtil.assertResponseStatusCode;
import static com.lht.corecalculation.api.utils.PmaRatingsUtil.buildPmaRatingInputs;
import static com.lht.corecalculation.api.utils.PmaRatingsUtil.createPmaRatingsUpdate;
import static com.lht.corecalculation.api.utils.PmaRatingsUtil.extractFromParts;
import static com.lht.corecalculation.api.utils.PmaRatingsUtil.fetchPmaRatingsResponseDto;
import static com.lht.corecalculation.api.utils.PmaRatingsUtil.updatePmaRatings;
import static com.lht.corecalculation.api.utils.PmaRatingsUtil.validateUpdatedRatings;
import static com.lht.corecalculation.api.utils.PricingEscalationUtil.assertExpectedAndActualPricingEscalationValues;
import static com.lht.corecalculation.api.utils.PricingEscalationUtil.extractIdsAndYears;
import static com.lht.corecalculation.api.utils.PricingEscalationUtil.getEscalationPricingResponseWithBearerToken;
import static com.lht.corecalculation.api.utils.PricingEscalationUtil.modifyValuesInEscalationInput;
import static com.lht.corecalculation.api.utils.RepairExclusionsUtil.createRepairExclusionsRequestDto;
import static com.lht.corecalculation.api.utils.RepairExclusionsUtil.getIsExcludedForClusterNameParts;
import static com.lht.corecalculation.api.utils.RepairExclusionsUtil.getRepairExclusionsResponse;
import static com.lht.corecalculation.api.utils.RepairExclusionsUtil.getRepairIdsForClusterName;
import static com.lht.corecalculation.api.utils.RepairExclusionsUtil.sendPutRepairExclusionsRequest;
import static com.lht.corecalculation.api.utils.RfpEngineUtil.callGetRfpEngineApi;
import static com.lht.corecalculation.api.utils.RfpEngineUtil.callPutRfpEngineApi;
import static com.lht.corecalculation.api.utils.RfpEngineUtil.transformRfpItemsWithModifiedDbii;
import static com.lht.corecalculation.api.utils.RfpModuleUtil.verifyGlobalRfpModuleChangeDbii;
import static com.lht.corecalculation.api.utils.ScrapCapsUtil.convertResponseToDto;
import static com.lht.corecalculation.api.utils.ScrapCapsUtil.createScrapCapsInputList;
import static com.lht.corecalculation.api.utils.ScrapCapsUtil.createScrapCapsRequestDto;
import static com.lht.corecalculation.api.utils.ScrapCapsUtil.getScrapCapsResponse;
import static com.lht.corecalculation.api.utils.ScrapCapsUtil.sendPutScrapCapsRequest;
import static com.lht.corecalculation.api.utils.SubcontractPricingUtil.verifySubcontractMarginAndCapForQuotation;
import static com.lht.corecalculation.api.enums.WorkscopeItemType.EXCLUDED_ITEMS;
import static com.lht.corecalculation.api.enums.WorkscopeItemType.INCLUDED_ITEMS;
import static com.lht.corecalculation.api.enums.WorkscopeItemType.TOTAL_ITEMS;
import static org.testng.Assert.assertEquals;
import static org.testng.Assert.assertTrue;

public class WorkscopeUtil extends BaseCocaApiTest {

    public static final Utils utils = new Utils();

    public static Response putScrapCapsValues(
            String accessToken, String quotationId, Float customScrapCapValue, Integer clpThresholdValue
    ) throws IOException {
        Response response = getScrapCapsResponse(accessToken, quotationId);
        ScrapCapResponseDto scrapCapResponseDto = convertResponseToDto(response);

        List<ScrapCapInputDto> scrapCapsInputList = createScrapCapsInputList(scrapCapResponseDto, customScrapCapValue);

        ScrapCapsRequestDto requestDto = createScrapCapsRequestDto(scrapCapsInputList, clpThresholdValue);

        return sendPutScrapCapsRequest(accessToken, quotationId, requestDto);
    }

    public static void putHandlingChargesValues(
            String engineTypeId, String accessToken, double z1Value, double z2Value,
            double pmaValue, double csmValue, Integer oneItemCapValue, Integer lineItemCapValue
    ) {
        Response response = getHandlingCharges(accessToken, engineTypeId);
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        HandlingChargesData handlingChargesData = response.as(ResultDto.class).getData();
        updateClustersAndParts(handlingChargesData, z1Value, z2Value, pmaValue, csmValue, oneItemCapValue, lineItemCapValue);

        Gson gson = new GsonBuilder().serializeNulls().create();
        String handlingChargesDataJson = gson.toJson(handlingChargesData);

        PutHandlingChargesRequest putHandlingChargesRequest = new PutHandlingChargesRequest(engineTypeId, handlingChargesDataJson)
                .withBearerToken(accessToken);
        Response responsePut = putHandlingChargesRequest.callAPI();
        Assert.assertEquals(responsePut.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
    }

    public static void putGlobalZ2RatingValues(String engineTypeId, String accessToken, Double z2RatingInputValue) throws IOException {
        ResponseDto initialRatingsResponse = Z2RatingsUtil.fetchZ2RatingsResponseDto(accessToken, engineTypeId);

        List<Z2RatingInputDto> z2RatingInputs = Z2RatingsUtil.buildZ2RatingInputs(Z2RatingsUtil.extractFromParts(initialRatingsResponse, Function.identity()), z2RatingInputValue);
        String updateRequestBody = utils.convert.dtoToJsonString(Z2RatingsUtil.createRatingsUpdate(z2RatingInputs));
        Z2RatingsUtil.assertResponseStatusCode(Z2RatingsUtil.updateZ2Ratings(accessToken, updateRequestBody, engineTypeId), RESPONSE_STATUS_CODE_200);

        Z2RatingsUtil.validateUpdatedRatings(Z2RatingsUtil.fetchZ2RatingsResponseDto(accessToken, engineTypeId), z2RatingInputValue);
    }

    public static void putGlobalPmaRatingValues(String engineTypeId, String accessToken, Double pmaRatingInputValue) throws IOException {
        PmaResponseDto initialRatingsResponse = fetchPmaRatingsResponseDto(accessToken, engineTypeId);

        List<PmaRatingInputDto> pmaRatingInputs = buildPmaRatingInputs(
                extractFromParts(initialRatingsResponse, Function.identity()), pmaRatingInputValue
        );
        String updateRequestBody = utils.convert.dtoToJsonString(createPmaRatingsUpdate(pmaRatingInputs));
        assertResponseStatusCode(updatePmaRatings(accessToken, updateRequestBody, engineTypeId), RESPONSE_STATUS_CODE_200);

        validateUpdatedRatings(fetchPmaRatingsResponseDto(accessToken, engineTypeId), pmaRatingInputValue);
    }

    public static void putCommitmentLetterToNo(String quotationId, String accessToken) throws JsonProcessingException {
        Response response = getCommitmentLetterResponse(quotationId, accessToken);

        CommitmentLetterResponseDto commitmentLetterResponseDto = utils.convert.jsonToDto(response, CommitmentLetterResponseDto.class);

        resetAllPartsHasCommitmentLetter(commitmentLetterResponseDto);

        Response responsePut = putCommitmentLetterRequest(quotationId, accessToken, commitmentLetterResponseDto);
        assertEquals(responsePut.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
    }

    public static void putCommitmentLetter(String quotationId, String accessToken, PartType partType) throws JsonProcessingException {
        Response response = getCommitmentLetterResponse(quotationId, accessToken);
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        CommitmentLetterResponseDto commitmentLetterResponseDto = utils.convert.jsonToDto(response, CommitmentLetterResponseDto.class);

        resetAllPartsHasCommitmentLetter(commitmentLetterResponseDto);

        updatePartsToHasCommitmentLetter(commitmentLetterResponseDto, partType);

        Response responsePut = putCommitmentLetterRequest(quotationId, accessToken, commitmentLetterResponseDto);

        assertEquals(responsePut.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
    }

    public static void putLabourRatesValues(
            String engineTypeId, String accessToken, Integer routineLabourRateValue,
            Integer nonRoutineLabourRateValue, Double eparDiscountValue
    ) throws JsonProcessingException {
        verifyLabourRatesValidInputs(routineLabourRateValue, nonRoutineLabourRateValue, eparDiscountValue, engineTypeId, accessToken);
    }

    public static void putRfpEngineValues(
            String engineTypeId, String accessToken, Boolean ciIncluded, Double dbiiValue
    ) throws JsonProcessingException {
        Response getRfpEngineResponse = callGetRfpEngineApi(accessToken, engineTypeId);
        RfpEngineResponseDto rfpEngineResponse = utils.convert.jsonToDto(getRfpEngineResponse, RfpEngineResponseDto.class);

        List<RfpItemInputDto> rfpItemInputs = transformRfpItemsWithModifiedDbii(rfpEngineResponse, dbiiValue);
        callPutRfpEngineApi(accessToken, ciIncluded, rfpItemInputs, engineTypeId);
    }

    public static void putRfpModuleValues(
            String engineTypeId, String accessToken, Boolean ciIncluded, Double dbiiValue
    ) throws JsonProcessingException {
        verifyGlobalRfpModuleChangeDbii(engineTypeId, ciIncluded, dbiiValue, accessToken);
    }

    public static void putSubcontractPricingValues(
            String engineTypeId, String accessToken, Double expectedMargin, Integer expectedCap
    ) throws JsonProcessingException {
        verifySubcontractMarginAndCapForQuotation(engineTypeId, expectedMargin, expectedCap, accessToken);
    }

    public static void putModularPricingDbiiAndPrice(
            String engineTypeId, String accessToken, Double dbiiValue , Double fpNtePrice
    ) throws JsonProcessingException {
        Response response = getModularPricingResponseWithBearerToken(engineTypeId, accessToken);
        assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        ModularPricingResponseDto modularPricingResponseDto = utils.convert.jsonToDto(response, ModularPricingResponseDto.class);

        List<ModularPricingInputDto> modularPricingInputs = createModularPricingInputsWithDbiiAndPrice(modularPricingResponseDto, dbiiValue , fpNtePrice);

        ModularPricingRequestDto requestDto = new ModularPricingRequestDto(modularPricingInputs);

         Response putResponse = new PutModularPricingRequest(
                engineTypeId,
                utils.convert.dtoToJsonString(requestDto))
                .withBearerToken(accessToken).callAPI();

         assertEquals(putResponse.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
    }

    public static Response getModularPricingResponseWithBearerToken(String quotationId, String accessToken) {
        GetModularPricingRequest getModularPricingRequest = new GetModularPricingRequest(quotationId).withBearerToken(accessToken);
        return getModularPricingRequest.callAPI();
    }

    public static List<ModularPricingInputDto> createModularPricingInputsWithDbiiAndPrice(
            ModularPricingResponseDto modularPricingResponseDto, Double dbiiValue, Double ftpNtePrice
    ) {
        List<ModularPricingInputDto> modularPricingInputs = new ArrayList<>();

        modularPricingResponseDto.getData().getWorkscopes().stream()
                .forEach(workscope -> workscope.getModules().stream()
                        .filter(module -> module.getId() != null) // Only add modules with valid IDs (not null)
                        .forEach(module -> modularPricingInputs.add(new ModularPricingInputDto(
                                module.getId(),
                                ftpNtePrice,
                                dbiiValue
                        )))
                );

        return modularPricingInputs;
    }

    public static void updateRepairExclusionsForCluster(
            String accessToken, String quotationId, String clusterName, boolean isExcluded
    ) throws IOException {
        RepairExclusionWorkscopeDataDto workscopeDataDto = getRepairExclusionsResponse(accessToken, quotationId);

        List<Integer> repairIds = getRepairIdsForClusterName(workscopeDataDto, clusterName);

        RepairExclusionsRequestDto requestDto = createRepairExclusionsRequestDto(repairIds, isExcluded);

        Response response = sendPutRepairExclusionsRequest(accessToken, quotationId, requestDto);
        assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        RepairExclusionWorkscopeDataDto repairExclusionAfterUpdate = getRepairExclusionsResponse(accessToken, quotationId);
        List<Boolean> getAllIsExcludedValues = getIsExcludedForClusterNameParts(repairExclusionAfterUpdate, clusterName);

        if (isExcluded) {
            assertTrue(getAllIsExcludedValues.stream().allMatch(Boolean::booleanValue), MESSAGE_NOT_ALL_IS_EXCLUDED_VALUES_ARE_TRUE);
        } else {
            assertTrue(getAllIsExcludedValues.stream().noneMatch(Boolean::booleanValue), MESSAGE_NOT_ALL_IS_EXCLUDED_VALUES_ARE_FALSE);
        }
    }

    public static void putPricingEscalationValues(
            String engineTypeId, String accessToken, Double manHourRate, Double eparPrices, Double rfpLabour,
            Double hcCapMaterial, Double hcCapSubcontract
    ) throws JsonProcessingException {
        putPricingEscalationValues(engineTypeId, accessToken, manHourRate, eparPrices, rfpLabour, hcCapMaterial, hcCapSubcontract, null);
    }

    public static void putPricingEscalationValues(
            String engineTypeId, String accessToken, Double manHourRate, Double eparPrices, Double rfpLabour,
            Double hcCapMaterial, Double hcCapSubcontract, Double fpNtePrices
    ) throws JsonProcessingException {
        // Get current pricing escalation response
        Response response = getEscalationPricingResponseWithBearerToken(engineTypeId, accessToken);
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        // Convert response to DTO
        EscalationResponseDto escalationResponseDto = utils.convert.jsonToDto(response, EscalationResponseDto.class);

        // Create and populate the escalation input DTO
        EscalationInputRequestDto escalationInputDto = new EscalationInputRequestDto();
        extractIdsAndYears(escalationResponseDto, escalationInputDto);

        // Modify the values in the escalation input DTO
        if (fpNtePrices == null) {
            modifyValuesInEscalationInput(
                    escalationInputDto, eparPrices, hcCapMaterial,
                    hcCapSubcontract, manHourRate, rfpLabour);
        } else {
            modifyValuesInEscalationInput(
                    escalationInputDto, eparPrices, hcCapMaterial, hcCapSubcontract,
                    manHourRate, rfpLabour, fpNtePrices);
        }

        // Assert the expected and actual values
        assertExpectedAndActualPricingEscalationValues(engineTypeId, accessToken, escalationInputDto,
                eparPrices, hcCapMaterial, hcCapSubcontract, manHourRate, rfpLabour, fpNtePrices);
    }

    public static void updateEngineValuesNteAndFixed(
            String engineTypeId,
            String accessToken,
            Float customScrapCapValue,
            Integer clpThreshold,
            double z1Value,
            double z2Value,
            double pmaValue,
            double csmValue,
            Integer oneItemCapValue,
            Integer lineItemCapValue,
            Double z2RatingInputValue,
            PartType partType,
            Integer routineLabourRateValue,
            Integer nonRoutineLabourRateValue,
            Double eparDiscountValue,
            Double dbiiRfpEngineValue,
            Boolean ciIncludedRfpEngine,
            Double dbiiRfpModuleValue,
            Boolean ciIncludedRfpModule,
            Double expectedMargin,
            Integer expectedCap,
            String clusterName,
            Boolean isExcluded,
            Double manHourRate,
            Double eparPrices,
            Double rfpLabour,
            Double hcCapMaterial,
            Double hcCapSubcontract,
            Double... fpNtePrices
    ) throws IOException {
        // Scrap Caps
        putScrapCapsValues(accessToken, engineTypeId, customScrapCapValue, clpThreshold);

        // Handling Charges
        putHandlingChargesValues(engineTypeId, accessToken, z1Value, z2Value, pmaValue, csmValue, oneItemCapValue, lineItemCapValue);

        // Global Z2 Rating
        putGlobalZ2RatingValues(engineTypeId, accessToken, z2RatingInputValue);

        // PMA Rating
        if (ENGINE_CFM56_5B_PMA_QUOTATION_ID.equals(engineTypeId)) {
            putGlobalPmaRatingValues(engineTypeId, accessToken, 80.0);
        }

        // Commitment Letter
        if (!ENGINE_V2500_QUOTATION_ID.equals(engineTypeId)) {
            putCommitmentLetterToNo(engineTypeId, accessToken);
            putCommitmentLetter(engineTypeId, accessToken, partType);
        }

        // Labour Rates
        putLabourRatesValues(engineTypeId, accessToken, routineLabourRateValue, nonRoutineLabourRateValue, eparDiscountValue);

        // RFP Engine
        putRfpEngineValues(engineTypeId, accessToken, ciIncludedRfpEngine, dbiiRfpEngineValue);

        // RFP Module
        putRfpModuleValues(engineTypeId, accessToken, ciIncludedRfpModule, dbiiRfpModuleValue);

        // Subcontract Pricing
        putSubcontractPricingValues(engineTypeId, accessToken, expectedMargin, expectedCap);

        // Repair Exclusions
        updateRepairExclusionsForCluster(accessToken, engineTypeId, clusterName, isExcluded);

        // Pricing Escalation
        if (fpNtePrices.length > 0) {
            putPricingEscalationValues(engineTypeId, accessToken, manHourRate, eparPrices, rfpLabour, hcCapMaterial, hcCapSubcontract, fpNtePrices[0]);
        } else {
            putPricingEscalationValues(engineTypeId, accessToken, manHourRate, eparPrices, rfpLabour, hcCapMaterial, hcCapSubcontract);
        }
    }

    public static void updateEngineValues(
            String engineTypeId,
            String accessToken,
            double z1Value,
            double z2Value,
            double pmaValue,
            double csmValue,
            Integer oneItemCapValue,
            Integer lineItemCapValue,
            Double z2RatingInputValue,
            Integer routineLabourRateValue,
            Integer nonRoutineLabourRateValue,
            Double eparDiscountValue,
            Double dbiiRfpEngineValue,
            Boolean ciIncludedRfpEngine,
            Double dbiiRfpModuleValue,
            Boolean ciIncludedRfpModule,
            Double expectedMargin,
            Integer expectedCap,
            Double manHourRate,
            Double eparPrices,
            Double rfpLabour,
            Double hcCapMaterial,
            Double hcCapSubcontract
    ) throws IOException {
        // Handling Charges
        putHandlingChargesValues(engineTypeId, accessToken, z1Value, z2Value, pmaValue, csmValue, oneItemCapValue, lineItemCapValue);

        // Global Z2 Rating
        putGlobalZ2RatingValues(engineTypeId, accessToken, z2RatingInputValue);

        //No Commitment Letter
        if (!ENGINE_V2500_QUOTATION_ID.equals(engineTypeId)) {
            putCommitmentLetterToNo(engineTypeId, accessToken);
        }

        // Labour Rates
        putLabourRatesValues(engineTypeId, accessToken, routineLabourRateValue, nonRoutineLabourRateValue, eparDiscountValue);

        // RFP Engine
        putRfpEngineValues(engineTypeId, accessToken, ciIncludedRfpEngine, dbiiRfpEngineValue);

        // RFP Module
        putRfpModuleValues(engineTypeId, accessToken, ciIncludedRfpModule, dbiiRfpModuleValue);

        // Subcontract Pricing
        putSubcontractPricingValues(engineTypeId, accessToken, expectedMargin, expectedCap);

        // Pricing Escalation
        putPricingEscalationValues(engineTypeId, accessToken, manHourRate, eparPrices, rfpLabour, hcCapMaterial, hcCapSubcontract);
    }

    public static void updateEngineValues(
            String engineTypeId,
            String accessToken,
            double z1Value,
            double z2Value,
            double pmaValue,
            double csmValue,
            Integer oneItemCapValue,
            Integer lineItemCapValue,
            Double z2RatingInputValue,
            Integer routineLabourRateValue,
            Integer nonRoutineLabourRateValue,
            Double eparDiscountValue,
            Double dbiiRfpEngineValue,
            Boolean ciIncludedRfpEngine,
            Double dbiiRfpModuleValue,
            Boolean ciIncludedRfpModule,
            Double expectedMargin,
            Integer expectedCap,
            Double manHourRate,
            Double eparPrices,
            Double rfpLabour,
            Double hcCapMaterial,
            Double hcCapSubcontract,
            Double fpNtePrices
    ) throws IOException {
        // Handling Charges
        putHandlingChargesValues(engineTypeId, accessToken, z1Value, z2Value, pmaValue, csmValue, oneItemCapValue, lineItemCapValue);

        // Global Z2 Rating
        putGlobalZ2RatingValues(engineTypeId, accessToken, z2RatingInputValue);

        // Labour Rates
        putLabourRatesValues(engineTypeId, accessToken, routineLabourRateValue, nonRoutineLabourRateValue, eparDiscountValue);

        // RFP Engine
        putRfpEngineValues(engineTypeId, accessToken, ciIncludedRfpEngine, dbiiRfpEngineValue);

        // RFP Module
        putRfpModuleValues(engineTypeId, accessToken, ciIncludedRfpModule, dbiiRfpModuleValue);

        // Subcontract Pricing
        putSubcontractPricingValues(engineTypeId, accessToken, expectedMargin, expectedCap);

        // Pricing Escalation
        putPricingEscalationValues(engineTypeId, accessToken, manHourRate, eparPrices, rfpLabour, hcCapMaterial, hcCapSubcontract, fpNtePrices);
    }

    public static List<String> extractYearFromResponse(Response response) throws JsonProcessingException {
        WsSummaryResponseDto wsSummaryResponseDto = utils.convert.jsonToDto(response, WsSummaryResponseDto.class);

        return wsSummaryResponseDto.getData().stream()
                .flatMap(dataItem -> dataItem.getIncludedItems().stream())
                .map(WorkscopeSummaryItemDto::getYear)
                .toList();
    }

    public static List<Double> extractValueFromResponse(
            Response response, Function<WorkscopeSummaryItemDto, Double> valueExtractor
    ) throws JsonProcessingException {
        WsSummaryResponseDto wsSummaryResponseDto = utils.convert.jsonToDto(response, WsSummaryResponseDto.class);

        return wsSummaryResponseDto.getData().stream()
                .flatMap(dataItem -> dataItem.getIncludedItems().stream())
                .map(valueExtractor)
                .toList();
    }

    public static List<Double> extractValueFromResponse(
            Response response, Function<WorkscopeSummaryItemDto, Double> valueExtractor,
            String workscopeName
    ) throws JsonProcessingException {
        WsSummaryResponseDto wsSummaryResponseDto = utils.convert.jsonToDto(response, WsSummaryResponseDto.class);

        return wsSummaryResponseDto.getData().stream()
                .filter(dataItem -> dataItem.getName().equals(workscopeName))
                .flatMap(dataItem -> dataItem.getIncludedItems().stream())
                .map(valueExtractor)
                .toList();
    }

    public static List<Double> extractValueFromResponse(
            Response response,
            Function<WorkscopeSummaryItemDto, Double> valueExtractor,
            String workscopeName,
            WorkscopeItemType itemType
    ) throws JsonProcessingException {
        WsSummaryResponseDto wsSummaryResponseDto = utils.convert.jsonToDto(response, WsSummaryResponseDto.class);

        return wsSummaryResponseDto.getData().stream()
                .filter(dataItem -> dataItem.getName().equals(workscopeName))
                .flatMap(dataItem -> switch (itemType) {
                    case INCLUDED_ITEMS -> dataItem.getIncludedItems().stream();
                    case EXCLUDED_ITEMS -> dataItem.getExcludedItems().stream();
                    case TOTAL_ITEMS -> dataItem.getTotalItems().stream();
                })
                .map(valueExtractor)
                .toList();
    }

    /**
     * Initializes or updates the workscope summary and waits until the calculation state is COMPLETED,
     * then fetches the workscope summary.
     *
     * @param engineTypeId Engine Type ID to update the workscope for.
     * @param accessToken  Bearer token for authorization.
     * @return The workscope summary response.
     * @throws InterruptedException If the thread is interrupted while waiting.
     * @throws ApiException         If there are issues with API calls.
     */
    public static Response initializeWorkscopeAndAwaitCompletionThenFetchSummary(
            String engineTypeId, String accessToken
    ) throws InterruptedException, ApiException {
        submitWorkscopeUpdate(engineTypeId, accessToken);
        ensureWorkscopeCalculationCompletes(engineTypeId, accessToken);
        return fetchWorkscopeSummary(engineTypeId, accessToken);
    }

    private static void submitWorkscopeUpdate(String engineTypeId, String accessToken) throws ApiException {
        Response response = new PostWorkscopeSummaryRequest(engineTypeId).withBearerToken(accessToken).callAPI();
        validateApiResponse(response, FAILED_TO_SUBMIT_WORKSCOPE_UPDATE);
    }

    private static void ensureWorkscopeCalculationCompletes(
            String engineTypeId, String accessToken
    ) throws InterruptedException, ApiException {
        boolean isCompleted = pollForCompletion(engineTypeId, accessToken);
        if (!isCompleted) {
            throw new ApiException(WORKSCOPE_CALCULATION_NOT_COMPLETE, engineTypeId);
        }
    }

    private static boolean pollForCompletion(String engineTypeId, String accessToken) throws InterruptedException, ApiException {
        long endTime = System.currentTimeMillis() + TIMEOUT_MS;
        while (System.currentTimeMillis() < endTime) {
            if (isCalculationCompleted(engineTypeId, accessToken)) {
                return true;
            }
            Thread.sleep(POLL_INTERVAL_MS);
        }
        return false;
    }

    private static boolean isCalculationCompleted(String engineTypeId, String accessToken) throws ApiException {
        Response response = new CalculationStateRequest(engineTypeId).withBearerToken(accessToken).callAPI();
        validateApiResponse(response, FAILED_TO_CHECK_CALCULATION_STATE);
        return CalculationProgress.COMPLETED.toString().equals(response.jsonPath().getString(DATA_OPERATION_PROGRESS));
    }

    private static Response fetchWorkscopeSummary(String engineTypeId, String accessToken) throws ApiException {
        Response response = new GetWorkscopeSummaryRequest(engineTypeId).withBearerToken(accessToken).callAPI();
        validateApiResponse(response, FAILED_TO_FETCH_WORKSCOPE_SUMMARY);
        return response;
    }

    private static void validateApiResponse(Response response, String errorMessage) throws ApiException {
        if (response.statusCode() != RESPONSE_STATUS_CODE_200) {
            throw new ApiException(errorMessage + STATUS_CODE + response.statusCode(), response.path(ERROR_DETAIL));
        }
    }

    public static void updateEngineValuesForLeap1a(String accessToken) throws IOException {
        updateEngineValues(ENGINE_LEAP_1A_QUOTATION_ID, accessToken,
                3, 3, 3, 3, 2500, 4000,
                75.0,
                60, 60, 0.0,
                10.0, false,
                10.0, false,
                3.0, 2500,
                3.0, 5.0, 0.0, 6.28, 6.28);
        //Fuel & Oil MATERIAL for the Testrun = 0.0
    }

    public static void updateEngineValuesForLeap1aFixedPrice(String accessToken) throws IOException {
        updateEngineValuesNteAndFixed(ENGINE_LEAP_1A_QUOTATION_ID, accessToken,
                5.0f, 50000,
                4, 4, 4, 4, 4000, 5000,
                80.0,
                PartType.CASE_AND_FRAME,
                60, 60, 5.0,
                10.0, true,
                10.0, false,
                4.0, 4000,
                FAN_BLADE, true,
                9.36, 5.0, 9.36, 6.88, 6.00, 7.13);
        //Fuel & Oil MATERIAL for the Testrun = 0.0
    }

    public static void updateEngineValuesForLeap1b(String accessToken) throws IOException {
        updateEngineValues(ENGINE_LEAP_1B_QUOTATION_ID, accessToken,
                3, 3, 3, 3, 2500, 4000,
                80.0,
                60, 55, 0.0,
                10.0, false,
                10.0, false,
                3.0, 2500,
                3.0, 5.0, 3.0, 6.42, 6.42);
        //Fuel & Oil MATERIAL for the Testrun = 0.0
    }

    public static void updateEngineValuesForLeap1bNte(String accessToken) throws IOException {
        updateEngineValuesNteAndFixed(ENGINE_LEAP_1B_QUOTATION_ID, accessToken,
                5.0f, 50000,
                4, 4, 4, 4, 4000, 5000,
                80.0,
                PartType.CASE_AND_FRAME,
                60, 60, 5.0,
                10.0, true,
                10.0, false,
                4.0, 4000,
                FAN_BLADE, true,
                9.36, 5.0, 9.36, 6.88, 6.00, 7.13);
        //Fuel & Oil MATERIAL for the Testrun = 0.0
    }

    public static void updateEngineValuesForV2500(String accessToken) throws IOException {
        updateEngineValues(ENGINE_V2500_QUOTATION_ID, accessToken,
                3, 3, 3, 3, 2500, 4000,
                75.0,
                55, 55, 0.0,
                10.0, false,
                10.0, false,
                3.0, 3000,
                3.0, 5.0, 3.0, 3.0, 3.0);
        //Fuel & Oil MATERIAL for the Testrun = 0.0
    }

    public static void updateEngineValuesForV2500FixedPrice(String accessToken) throws IOException {
        updateEngineValuesNteAndFixed(ENGINE_V2500_QUOTATION_ID, accessToken,
                5.0f, 50000,
                4, 4, 4, 4, 4000, 5000,
                80.0,
                PartType.CASE_AND_FRAME,
                60, 60, 5.0,
                10.0, true,
                10.0, false,
                4.0, 4000,
                FAN_BLADE, true,
                9.36, 5.0, 9.36, 6.88, 6.00, 7.13);
        //Fuel & Oil MATERIAL for the Testrun = 0.0
    }

    public static void updateEngineValuesForCfm565b(String accessToken) throws IOException {
        updateEngineValues(ENGINE_CFM56_5B_QUOTATION_ID, accessToken,
                4, 4, 4, 4, 3000, 5000,
                80.0,
                55, 65, 0.0,
                12.0, false,
                10.0, false,
                5.0, 5000,
                3.0, 5.0, 3.0, 6.3, 6.3);
        //Fuel & Oil MATERIAL for the Testrun = 0.0
    }

    public static void updateEngineValuesForCfm565BPmaFixedPrice(String accessToken) throws IOException {
        updateEngineValuesNteAndFixed(ENGINE_CFM56_5B_PMA_QUOTATION_ID, accessToken,
                5.0f, 50000,
                4, 4, 4, 4, 4000, 5000,
                80.0,
                PartType.CASE_AND_FRAME,
                60, 60, 5.0,
                10.0, true,
                10.0, false,
                4.0, 4000,
                LPT_BLADES, true,
                9.36, 5.0, 9.36, 6.88, 6.00, 7.13);
    }

    public static void updateEngineValuesForCfm567b(String accessToken) throws IOException {
        updateEngineValues(ENGINE_CFM56_7B_QUOTATION_ID, accessToken,
                4, 4, 4, 4, 3000, 5000,
                80.0,
                55, 65, 0.0,
                12.0, false,
                10.0, false,
                5.0, 5000,
                3.0, 5.0, 3.0, 6.28, 6.28);
        //Fuel & Oil MATERIAL for the Testrun = 0.0
    }

    public static void updateEngineValuesForCfm567bNte(String accessToken) throws IOException {
        updateEngineValuesNteAndFixed(ENGINE_CFM56_7B_QUOTATION_ID, accessToken,
                5.0f, 50000,
                4, 4, 4, 4, 4000, 5000,
                80.0,
                PartType.CASE_AND_FRAME,
                60, 60, 5.0,
                10.0, true,
                10.0, false,
                4.0, 4000,
                LPT_NOZZLES_STG_2_4, true,
                9.36, 5.0, 9.36, 6.88, 6.00, 7.13);
        //Fuel & Oil MATERIAL for the Testrun = 0.0
    }

    public static void updateEngineValuesModular(
            String engineTypeId,
            String accessToken,
            Float customScrapCapValue,
            Integer clpThreshold,
            double z1Value,
            double z2Value,
            double pmaValue,
            double csmValue,
            Integer oneItemCapValue,
            Integer lineItemCapValue,
            Double z2RatingInputValue,
            Integer routineLabourRateValue,
            Integer nonRoutineLabourRateValue,
            Double eparDiscountValue,
            Double dbiiRfpEngineValue,
            Boolean ciIncludedRfpEngine,
            Double dbiiRfpModuleValue,
            Boolean ciIncludedRfpModule,
            Double expectedMargin,
            Integer expectedCap,
            Double manHourRate,
            Double eparPrices,
            Double rfpLabour,
            Double hcCapMaterial,
            Double hcCapSubcontract,
            Double modularDbIIValues,
            String clusterName,
            Boolean isExcluded,
            Double fpNtePrice
    ) throws IOException {

        // Put Scrap Cap Values
        putScrapCapsValues(accessToken, engineTypeId, customScrapCapValue, clpThreshold);

        // Handling Charges
        putHandlingChargesValues(engineTypeId, accessToken, z1Value, z2Value, pmaValue, csmValue, oneItemCapValue, lineItemCapValue);

        // Global Z2 Rating
        putGlobalZ2RatingValues(engineTypeId, accessToken, z2RatingInputValue);

        // PMA Rating
        if (ENGINE_CFM56_5B_PMA_QUOTATION_ID.equals(engineTypeId)) {
            putGlobalPmaRatingValues(engineTypeId, accessToken, 80.0);
        }

        //No Commitment Letter
        if (!ENGINE_V2500_QUOTATION_ID.equals(engineTypeId)) {
            putCommitmentLetterToNo(engineTypeId, accessToken);
        }

        // Labour Rates
        putLabourRatesValues(engineTypeId, accessToken, routineLabourRateValue, nonRoutineLabourRateValue, eparDiscountValue);

        // RFP Engine
        putRfpEngineValues(engineTypeId, accessToken, ciIncludedRfpEngine, dbiiRfpEngineValue);

        // RFP Module
        putRfpModuleValues(engineTypeId, accessToken, ciIncludedRfpModule, dbiiRfpModuleValue);

        // Subcontract Pricing
        putSubcontractPricingValues(engineTypeId, accessToken, expectedMargin, expectedCap);

        // Get Repair Exclusion
        updateRepairExclusionsForCluster(accessToken, engineTypeId, clusterName, isExcluded);

        // Modular Pricing
        putModularPricingDbiiAndPrice(engineTypeId, accessToken, modularDbIIValues , fpNtePrice);

        // Pricing Escalation
        putPricingEscalationValues(engineTypeId, accessToken, manHourRate, eparPrices, rfpLabour, hcCapMaterial, hcCapSubcontract, 5.0);
    }

    public static void updateEngineValuesForModularPrice(String accessToken, String engineTypeId) throws IOException {
        updateEngineValuesModular(engineTypeId, accessToken,
                5.0f, 4000,
                3, 3, 3, 3, 2500, 4000,
                75.0,
                55, 55, 0.0,
                10.0, false,
                10.0, false,
                3.0, 3000,
                3.0, 5.0, 3.0, 3.0,10.0, 10.0 , FAN_BLADE , true , 1000.0);
    }

    public static void assertListEqualsWithTolerance(List<Double> actual, List<Double> expected, double tolerance, String errorMessage) {
        if (actual.size() != expected.size()) {
            Assert.fail(ERROR_MESSAGE_LIST_SIZE_MISMATCH + errorMessage);
        }

        for (int i = 0; i < actual.size(); i++) {
            if (Math.abs(actual.get(i) - expected.get(i)) > tolerance) {
                Assert.fail(String.format(ERROR_MESSAGE_VALUE_MISMATCH_WITH_TOLERANCE,
                        i, expected.get(i), actual.get(i), errorMessage));
            }
        }
    }

    /**
     * Helper method for printing the formatted results for the workscope summary for NTE/FP contracts that need to be copied to the @DataProvider.
     * Example usage:
     * printFormattedResults(workscope, actualIncludedEbit, actualExcludedEbit, actualTotalEbit);
     */
    public static void printFormattedResults(String workscope, List<Double> included, List<Double> excluded, List<Double> total) {
        StringBuilder result = new StringBuilder();
        result.append("    {\n");
        result.append("        ").append(workscope).append(",\n");
        result.append("        Arrays.asList(").append(listToString(included)).append("),\n");
        result.append("        Arrays.asList(").append(listToString(excluded)).append("),\n");
        result.append("        Arrays.asList(").append(listToString(total)).append(")\n");
        result.append("    },");
        System.out.println(result);
    }

    private static String listToString(List<Double> list) {
        return list.stream()
                .map(Object::toString)
                .collect(Collectors.joining(", "));
    }

    /**
     * Custom exception class for API errors.
     */
    public static class ApiException extends Exception {

        private final String engineTypeId;

        public ApiException(String message, String engineTypeId) {
            super(message + engineTypeId);
            this.engineTypeId = engineTypeId;
        }
    }
}