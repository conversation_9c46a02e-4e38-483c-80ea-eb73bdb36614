package com.lht.corecalculation.api.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.lht.corecalculation.api.pojo.dto.quotation.QuotationWrapperDto;
import com.lht.corecalculation.api.pojo.dto.rfpengine.RfpEngineItemDto;
import com.lht.corecalculation.api.pojo.dto.rfpengine.RfpEngineResponseDto;
import com.lht.corecalculation.api.pojo.dto.rfpengine.RfpItemInputDto;
import com.lht.corecalculation.api.pojo.dto.rfpengine.RfpRequestDto;
import com.lht.corecalculation.api.request.quotation.GetQuotationsRequest;
import com.lht.corecalculation.api.request.rfpengine.GetRfpEngineRequest;
import com.lht.corecalculation.api.request.rfpengine.PutRfpEngineRequest;
import com.lht.corecalculation.base.BaseCocaApiTest;
import com.lht.corecalculation.base.cp.utils.Utils;
import io.restassured.response.Response;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import org.testng.Assert;


import static com.lht.corecalculation.api.constants.GlobalConstants.DIFFERENCE_BETWEEN_ACTUAL_AND_EXPECTED_PRICE;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_200;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE;
import static java.util.stream.Collectors.toList;

public class RfpEngineUtil extends BaseCocaApiTest {

    public static Utils utils = new Utils();

    public static void checkForListMismatch(List<String> namesFromJson, List<String> namesFromClusterOrder) {
        IntStream.range(0, namesFromJson.size())
                .filter(i -> !namesFromJson.get(i).equals(namesFromClusterOrder.get(i)))
                .findFirst()
                .ifPresent(i ->
                        Assert.fail("Mismatch found at index " + i + ": Expected: " +
                                namesFromClusterOrder.get(i) + ", But was: " + namesFromJson.get(i)));
    }

    public static Response getRfpEngineResponseWithBearerToken(String quotationId, String accessToken) {
        GetRfpEngineRequest getRfpEngineRequest = new GetRfpEngineRequest(quotationId).withBearerToken(accessToken);
        return getRfpEngineRequest.callAPI();
    }

    public static List<String> extractRfpClusterNamesFromResponse(Response response) throws JsonProcessingException {
        RfpEngineResponseDto responseDtoRfpEngine = utils.convert.jsonToDto(response, RfpEngineResponseDto.class);
        return responseDtoRfpEngine.getData().getRfpEngineData().getRfpEngineItems()
                .stream()
                .map(RfpEngineItemDto::getName)
                .collect(toList());
    }

    public static Map<String, Double> extractRfpClusterNameAndCostFromResponse(Response response) throws JsonProcessingException {
        RfpEngineResponseDto responseDtoRfpEngine = utils.convert.jsonToDto(response, RfpEngineResponseDto.class);
        return responseDtoRfpEngine.getData().getRfpEngineData().getRfpEngineItems()
                .stream()
                .collect(Collectors.toMap(RfpEngineItemDto::getName, RfpEngineItemDto::getCost));
    }

    public static Integer getCurrentMaxQuotations(String accessToken) throws JsonProcessingException {
        GetQuotationsRequest getQuotationsRequest = new GetQuotationsRequest().withAccessToken(accessToken);
        Response response = getQuotationsRequest.callAPI();
        QuotationWrapperDto quotationWrapperDto = utils.convert.jsonToDto(response, QuotationWrapperDto.class);
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        return quotationWrapperDto.getQuotationResult().getTotalItems();
    }

    /*
       61238 / (1 - (13.5 / 100)) = 61238 / (1 - 0.135) = 61238 / 0.865 = 70795.3757
    */
    public static List<Double> calculateSumPriceCostAndCiHours(RfpEngineResponseDto rfpEngineResponse) {
        List<Double> cost = extractCostValuesFromResponse(rfpEngineResponse);
        List<Double> ciHoursCost = extractCiHoursCostValuesFromResponse(rfpEngineResponse);
        List<Double> dbiiValues = extractDbiiValuesFromResponse(rfpEngineResponse);

        List<Double> sumCostAndCiHours = new ArrayList<>();
        int minLength = Math.min(cost.size(), Math.min(ciHoursCost.size(), dbiiValues.size()));
        for (int i = 0; i < minLength; i++) {
            double dbii = dbiiValues.get(i);
            sumCostAndCiHours.add((cost.get(i) + ciHoursCost.get(i)) / (1 - (dbii / 100.00)));
        }
        return sumCostAndCiHours;
    }

    public static List<Double> calculateSumCostAndCiHours(RfpEngineResponseDto rfpEngineResponse) {
        List<Double> cost = extractCostValuesFromResponse(rfpEngineResponse);
        List<Double> ciHoursCost = extractCiHoursCostValuesFromResponse(rfpEngineResponse);

        List<Double> sumCostAndCiHours = new ArrayList<>();
        int minLength = Math.min(cost.size(), ciHoursCost.size());
        for (int i = 0; i < minLength; i++) {
            sumCostAndCiHours.add(cost.get(i) + ciHoursCost.get(i));
        }
        return sumCostAndCiHours;
    }

    public static List<Double> roundDoublesToOneDecimal(List<Double> values) {
        return values.stream()
                .map(num -> Math.round(num * 1.0) / 10.0)
                .collect(Collectors.toList());
    }

    public static List<Double> extractValuesFromResponse(
            RfpEngineResponseDto rfpEngineResponseDto, Function<RfpEngineItemDto, Double> mapper
    ) {
        return rfpEngineResponseDto.getData().getRfpEngineData().getRfpEngineItems().stream()
                .map(mapper)
                .collect(Collectors.toList());
    }

    public static List<Double> extractUniqueDbiiValuesFromResponse(RfpEngineResponseDto rfpEngineResponseDto) {
        return extractValuesFromResponse(rfpEngineResponseDto, RfpEngineItemDto::getDbii).stream()
                .distinct()
                .collect(Collectors.toList());
    }

    public static List<Double> extractCostValuesFromResponse(RfpEngineResponseDto rfpEngineResponseDto) {
        return extractValuesFromResponse(rfpEngineResponseDto, RfpEngineItemDto::getCost);
    }

    public static List<Double> extractCiHoursCostValuesFromResponse(RfpEngineResponseDto rfpEngineResponseDto) {
        return extractValuesFromResponse(rfpEngineResponseDto, RfpEngineItemDto::getCiHoursCost);
    }

    public static List<Double> extractDbiiValuesFromResponse(RfpEngineResponseDto rfpEngineResponseDto) {
        return extractValuesFromResponse(rfpEngineResponseDto, RfpEngineItemDto::getDbii);
    }

    public static List<Double> extractCiHoursPriceValuesFromResponse(RfpEngineResponseDto rfpEngineResponseDto) {
        return extractValuesFromResponse(rfpEngineResponseDto, RfpEngineItemDto::getPrice);
    }

    public static List<RfpItemInputDto> transformRfpItemsWithModifiedDbii(RfpEngineResponseDto rfpEngineResponseDto, Double dbiiValue) {
        List<RfpItemInputDto> rfpItemInputs = new ArrayList<>();
        for (RfpEngineItemDto rfpEngineItem : rfpEngineResponseDto.getData().getRfpEngineData().getRfpEngineItems()) {
            rfpItemInputs.add(new RfpItemInputDto(
                    rfpEngineItem.getId(),
                    rfpEngineItem.getPrice(),
                    rfpEngineItem.getIsTestrunMaterial(),
                    dbiiValue
            ));
        }
        return rfpItemInputs;
    }

    public static Response callPutRfpEngineApi(
            String accessToken, Boolean ciIncluded, List<RfpItemInputDto> rfpItemInputs,
            String quotationId
    ) throws JsonProcessingException {
        RfpRequestDto rfpRequestDto = new RfpRequestDto(ciIncluded, rfpItemInputs);
        PutRfpEngineRequest putRfpEngineRequest =
                new PutRfpEngineRequest(quotationId, utils.convert.dtoToJsonString(rfpRequestDto))
                        .withBearerToken(accessToken);
        return putRfpEngineRequest.callAPI();
    }

    public static Response callGetRfpEngineApi(String accessToken, String quotationId) {
        GetRfpEngineRequest getRfpEngineRequest = new GetRfpEngineRequest(quotationId).withBearerToken(accessToken);
        Response response = getRfpEngineRequest.callAPI();
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
        return response;
    }

    public static void verifyDbiiModification(
            String accessToken,
            String quotationId,
            Double dbiiValue,
            Boolean ciIncluded
    ) throws JsonProcessingException {
        Response getRfpEngineResponse = callGetRfpEngineApi(accessToken, quotationId);
        RfpEngineResponseDto rfpEngineResponseDto = utils.convert.jsonToDto(getRfpEngineResponse, RfpEngineResponseDto.class);

        List<RfpItemInputDto> rfpItemInputs = transformRfpItemsWithModifiedDbii(rfpEngineResponseDto, dbiiValue);
        callPutRfpEngineApi(accessToken, ciIncluded, rfpItemInputs, quotationId);

        Response updatedGetRfpEngineResponse = callGetRfpEngineApi(accessToken, quotationId);
        RfpEngineResponseDto updatedRfpEngineResponseDto = utils.convert.jsonToDto(updatedGetRfpEngineResponse, RfpEngineResponseDto.class);
        List<Double> actualDbiiValue = extractUniqueDbiiValuesFromResponse(updatedRfpEngineResponseDto);

        Assert.assertEquals(actualDbiiValue.size(), 1, DIFFERENCE_BETWEEN_ACTUAL_AND_EXPECTED_PRICE);
        Assert.assertEquals(actualDbiiValue.get(0), dbiiValue, DIFFERENCE_BETWEEN_ACTUAL_AND_EXPECTED_PRICE);
    }

    public static void verifyRfpEngineIncluceCiHours(
            String accessToken,
            String quotationId,
            Double dbiiValue,
            Boolean ciIncluded
    ) throws JsonProcessingException {
        Response getRfpEngineResponse = callGetRfpEngineApi(accessToken, quotationId);
        RfpEngineResponseDto rfpEngineResponse = utils.convert.jsonToDto(getRfpEngineResponse, RfpEngineResponseDto.class);

        List<Double> sumCostAndCiHours = calculateSumCostAndCiHours(rfpEngineResponse);

        List<RfpItemInputDto> rfpItemInputs = transformRfpItemsWithModifiedDbii(rfpEngineResponse, dbiiValue);
        callPutRfpEngineApi(accessToken, ciIncluded, rfpItemInputs, quotationId);

        Response updatedGetRfpEngineResponse = callGetRfpEngineApi(accessToken, quotationId);
        RfpEngineResponseDto updatedRfpEngineResponseDto = utils.convert.jsonToDto(updatedGetRfpEngineResponse, RfpEngineResponseDto.class);
        List<Double> actualPrice = extractCiHoursPriceValuesFromResponse(updatedRfpEngineResponseDto);

        List<Double> actualPriceRounded = roundDoublesToOneDecimal(actualPrice);
        List<Double> sumCostAndCiHoursRounded = roundDoublesToOneDecimal(sumCostAndCiHours);

        Assert.assertEquals(actualPriceRounded, sumCostAndCiHoursRounded, DIFFERENCE_BETWEEN_ACTUAL_AND_EXPECTED_PRICE);
    }
}
