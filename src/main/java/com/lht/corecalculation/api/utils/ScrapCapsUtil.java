package com.lht.corecalculation.api.utils;

import com.lht.corecalculation.api.pojo.dto.scrapcaps.ScrapCapInputDto;
import com.lht.corecalculation.api.pojo.dto.scrapcaps.ScrapCapResponseDto;
import com.lht.corecalculation.api.pojo.dto.scrapcaps.ScrapCapsRequestDto;
import com.lht.corecalculation.api.request.scrapcaps.GetScrapCapsRequest;
import com.lht.corecalculation.api.request.scrapcaps.PutScrapCapsRequest;
import com.lht.corecalculation.base.BaseCocaApiTest;
import com.lht.corecalculation.base.cp.utils.Utils;
import io.restassured.response.Response;
import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;


import static com.lht.corecalculation.api.constants.GlobalConstants.ERROR_MESSAGE_CLP_VALUES_MISMATCH;
import static com.lht.corecalculation.api.constants.GlobalConstants.MESSAGE_EXPECTED_SCRAP_CAP_INPUT_TO_BE_ZERO;
import static com.lht.corecalculation.api.constants.GlobalConstants.MESSAGE_EXPECTED_SCRAP_CAP_INPUT_TO_MATCH_CUSTOM;
import static com.lht.corecalculation.api.constants.GlobalConstants.PART_TYPE_COMPONENT;
import static com.lht.corecalculation.api.constants.GlobalConstants.PART_TYPE_KIT;
import static com.lht.corecalculation.api.constants.GlobalConstants.PART_TYPE_NON_ROUTINE_MATERIAL;
import static com.lht.corecalculation.api.constants.GlobalConstants.PART_TYPE_PARTS_PACKAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.PART_TYPE_ROUTINE_MATERIAL;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_200;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE;
import static org.testng.Assert.assertEquals;

public class ScrapCapsUtil extends BaseCocaApiTest {

    public static Utils utils = new Utils();

    public static Response getScrapCaps(String accessToken, String quotationId) {
        GetScrapCapsRequest getScrapCapsRequest = new GetScrapCapsRequest(quotationId).withBearerToken(accessToken);
        return getScrapCapsRequest.callAPI();
    }

    public static List<String> getUniqueClusterNames(ScrapCapResponseDto scrapCapResponseDto) {
        return scrapCapResponseDto.getData().getWorkscopes().stream()
                .flatMap(workscope -> workscope.getClusters().stream())
                .map(cluster -> cluster.getName())
                .distinct()
                .collect(Collectors.toList());
    }

    public static List<String> getUniquePartWithoutKitComponentAndPartsPackage(ScrapCapResponseDto scrapCapResponseDto) {
        return scrapCapResponseDto.getData().getWorkscopes().stream()
                .flatMap(workscope -> workscope.getClusters().stream())
                .filter(part -> !isExcludedPartType(part.getPartsType())) // Exclude parts based on type
                .flatMap(cluster -> cluster.getParts().stream())
                .map(part -> part.getName())
                .distinct()
                .collect(Collectors.toList());
    }

    private static boolean isExcludedPartType(String partType) {
        return PART_TYPE_KIT.equals(partType) ||
                PART_TYPE_COMPONENT.equals(partType) ||
                PART_TYPE_PARTS_PACKAGE.equals(partType) ||
                PART_TYPE_ROUTINE_MATERIAL.equals(partType) ||
                PART_TYPE_NON_ROUTINE_MATERIAL.equals(partType);
    }

    public static List<Double> getAllScrapCapInputValues(ScrapCapResponseDto scrapCapResponseDto) {
        return scrapCapResponseDto.getData().getWorkscopes().stream()
                .flatMap(workscope -> workscope.getClusters().stream())
                .flatMap(cluster -> cluster.getParts().stream())
                .map(part -> part.getScrapCapInput())
                .collect(Collectors.toList());
    }

    public static List<Double> getAllClpValues(ScrapCapResponseDto scrapCapResponseDto) {
        return scrapCapResponseDto.getData().getWorkscopes().stream()
                .flatMap(workscope -> workscope.getClusters().stream())
                .flatMap(cluster -> cluster.getParts().stream())
                .map(part -> part.getClp())
                .collect(Collectors.toList());
    }

    public static List<Integer> getAllPartIdValues(ScrapCapResponseDto scrapCapResponseDto) {
        return scrapCapResponseDto.getData().getWorkscopes().stream()
                .flatMap(workscope -> workscope.getClusters().stream())
                .flatMap(cluster -> cluster.getParts().stream())
                .map(part -> part.getId())
                .collect(Collectors.toList());
    }

    public static Response executePutScrapCapsTest(
            String accessToken, String quotationId, Float customScrapCapValue, Integer clpThresholdValue
    ) throws IOException {
        Response response = getScrapCapsResponse(accessToken, quotationId);
        ScrapCapResponseDto scrapCapResponseDto = convertResponseToDto(response);

        List<ScrapCapInputDto> scrapCapsInputList = createScrapCapsInputList(scrapCapResponseDto, customScrapCapValue);

        ScrapCapsRequestDto requestDto = createScrapCapsRequestDto(scrapCapsInputList, clpThresholdValue);

        return sendPutScrapCapsRequest(accessToken, quotationId, requestDto);
    }

    public static Response getScrapCapsResponse(String accessToken, String quotationId) {
        Response response = getScrapCaps(accessToken, quotationId);
        assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
        return response;
    }

    public static ScrapCapResponseDto convertResponseToDto(Response response) throws IOException {
        return utils.convert.jsonToDto(response, ScrapCapResponseDto.class);
    }

    public static List<ScrapCapInputDto> createScrapCapsInputList(ScrapCapResponseDto scrapCapResponseDto, Float customScrapCapValue) {
        List<Integer> allPartIds = ScrapCapsUtil.getAllPartIdValues(scrapCapResponseDto);

        return allPartIds.stream()
                .filter(id -> id != null)
                .map(id -> new ScrapCapInputDto(id, customScrapCapValue))
                .collect(Collectors.toList());
    }

    public static ScrapCapsRequestDto createScrapCapsRequestDto(List<ScrapCapInputDto> scrapCapsInputList, Integer clpThresholdValue) {
        ScrapCapsRequestDto requestDto = new ScrapCapsRequestDto();
        if (clpThresholdValue != null) {
            requestDto.setClpThreshold(clpThresholdValue);
        }
        requestDto.setScrapCapsInput(scrapCapsInputList);
        return requestDto;
    }

    public static Response sendPutScrapCapsRequest(
            String accessToken, String quotationId, ScrapCapsRequestDto requestDto
    ) throws IOException {
        String requestBody = utils.convert.dtoToJsonString(requestDto);

        PutScrapCapsRequest putScrapCapsRequest = new PutScrapCapsRequest(quotationId, requestBody)
                .withBearerToken(accessToken);
        return putScrapCapsRequest.callAPI();
    }

    public static void validatePutScrapCapsResponse(
            Response putResponse, Float customScrapCapValue, Integer clpThreshold
    ) throws IOException {
        ScrapCapResponseDto responseDto = ScrapCapsUtil.utils.convert.jsonToDto(putResponse, ScrapCapResponseDto.class);

        List<Double> clpValues = ScrapCapsUtil.getAllClpValues(responseDto);
        List<Double> scrapCapInputs = ScrapCapsUtil.getAllScrapCapInputValues(responseDto);
        List<Integer> partIds = ScrapCapsUtil.getAllPartIdValues(responseDto);

        if (clpValues.size() != scrapCapInputs.size() || scrapCapInputs.size() != partIds.size()) {
            throw new IllegalStateException(ERROR_MESSAGE_CLP_VALUES_MISMATCH);
        }

        partIds.stream()
                .filter(id -> id != null)
                .forEach(id -> {
                    int index = partIds.indexOf(id);
                    Double clpValue = clpValues.get(index);
                    Float scrapCapInput = scrapCapInputs.get(index) != null ? scrapCapInputs.get(index).floatValue() : null;

                    if (clpThreshold != null && clpValue != null && clpValue > clpThreshold) {
                        assertEqualsScrapCaps(scrapCapInput, Float.valueOf(0.0F), MESSAGE_EXPECTED_SCRAP_CAP_INPUT_TO_BE_ZERO + id);
                    } else {
                        assertEqualsScrapCaps(scrapCapInput, customScrapCapValue, MESSAGE_EXPECTED_SCRAP_CAP_INPUT_TO_MATCH_CUSTOM + id);
                    }
                });
    }

    private static void assertEqualsScrapCaps(Float actual, Float expected, String message) {
        if ((actual == null && expected != null) || (actual != null && !actual.equals(expected))) {
            throw new AssertionError(message + expected + actual);
        }
    }
}
