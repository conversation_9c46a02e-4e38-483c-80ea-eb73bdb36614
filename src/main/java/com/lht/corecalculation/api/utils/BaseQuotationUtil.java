package com.lht.corecalculation.api.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.lht.corecalculation.api.pojo.dto.inner.QuotationCover;
import com.lht.corecalculation.api.pojo.dto.quotation.QuotationDetailsDto;
import com.lht.corecalculation.api.pojo.dto.quotation.QuotationWrapperDto;
import com.lht.corecalculation.api.request.quotation.GetQuotationsRequest;
import com.lht.corecalculation.base.cp.utils.Utils;
import com.lht.corecalculation.exceptions.CopyQuotationLimitException;
import com.zebrunner.carina.api.AbstractApiMethodV2;
import io.restassured.response.Response;
import org.testng.Assert;


import static com.lht.corecalculation.api.constants.GlobalConstants.COPY_LIMIT_EXCEEDED_BODY;
import static com.lht.corecalculation.api.constants.GlobalConstants.ERROR_MESSAGE_FOR_QUATATION_COPY_LIMIT;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_200;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE;

public class BaseQuotationUtil {

    public static Utils utils = new Utils();

    public static QuotationCover getAndAssertQuotation(AbstractApiMethodV2 request) throws JsonProcessingException, CopyQuotationLimitException {
        var response = executeAndAssertRequest(request);
        var quotationDetailsDto = utils.convert.jsonToDto(response, QuotationDetailsDto.class);
        verifyQuotationCopyLimitIsNotReached(response);
        Assert.assertNull(quotationDetailsDto.getError());
        return quotationDetailsDto.getQuotation();
    }

    public static Integer getCurrentMaxQuotationsCount(String accessToken) throws JsonProcessingException, CopyQuotationLimitException {
        var currentMaxQuotations = 1;
        var getQuotationsRequest = new GetQuotationsRequest().withAccessToken(accessToken);
        var getQuotationsResponse = executeAndAssertRequest(getQuotationsRequest);
        var quotationWrapperDto = utils.convert.jsonToDto(getQuotationsResponse, QuotationWrapperDto.class);
        currentMaxQuotations = quotationWrapperDto.getQuotationResult().getTotalItems();

        return currentMaxQuotations;
    }

    public static Response executeAndAssertRequest(AbstractApiMethodV2 request) throws CopyQuotationLimitException {
        var response = request.callAPI();
        verifyQuotationCopyLimitIsNotReached(response);
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
        return response;
    }

    private static void verifyQuotationCopyLimitIsNotReached(Response rsp) throws CopyQuotationLimitException {
        if (rsp.getBody().asString().contains(COPY_LIMIT_EXCEEDED_BODY)) {
            throw new CopyQuotationLimitException(ERROR_MESSAGE_FOR_QUATATION_COPY_LIMIT);
        }

    }
}