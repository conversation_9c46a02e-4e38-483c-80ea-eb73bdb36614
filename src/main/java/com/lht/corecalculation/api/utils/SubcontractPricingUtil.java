package com.lht.corecalculation.api.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.lht.corecalculation.api.pojo.dto.subcontractpricing.SubcontractInputsDto;
import com.lht.corecalculation.api.pojo.dto.subcontractpricing.SubcontractItemDto;
import com.lht.corecalculation.api.pojo.dto.subcontractpricing.SubcontractItemInputDto;
import com.lht.corecalculation.api.pojo.dto.subcontractpricing.SubcontractResponseDto;
import com.lht.corecalculation.api.request.subcontractpricing.GetSubcontractPricingRequest;
import com.lht.corecalculation.api.request.subcontractpricing.PutSubcontractPricingRequest;
import com.lht.corecalculation.base.BaseCocaApiTest;
import com.lht.corecalculation.base.cp.utils.Utils;
import io.restassured.response.Response;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import org.testng.Assert;


import static com.lht.corecalculation.api.constants.GlobalConstants.DIFFERENCE_BETWEEN_ACTUAL_AND_EXPECTED_PRICE;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_200;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.UNIQUE_CAPS_DO_NOT_MATCH_NULL;
import static java.util.stream.Collectors.toList;

public class SubcontractPricingUtil extends BaseCocaApiTest {

    public static Utils utils = new Utils();

    public static List<String> extractSubcontractPriceClusterNamesFromResponse(Response response) throws JsonProcessingException {
        SubcontractResponseDto subcontractResponseDto = utils.convert.jsonToDto(response, SubcontractResponseDto.class);
        return subcontractResponseDto.getData().getSubcontracts()
                .stream()
                .map(SubcontractItemDto::getName)
                .collect(toList());
    }

    public static Response getSubcontractPriceResponseWithBearerToken(String quotationId, String accessToken) {
        GetSubcontractPricingRequest getSubcontractPricingRequest = new GetSubcontractPricingRequest(quotationId).withBearerToken(accessToken);
        return getSubcontractPricingRequest.callAPI();
    }

    public static void checkForListMismatch(List<String> namesFromJson, List<String> namesFromClusterOrder) {
        IntStream.range(0, namesFromJson.size())
                .filter(i -> !namesFromJson.get(i).equals(namesFromClusterOrder.get(i)))
                .findFirst()
                .ifPresent(i -> Assert.fail("Mismatch found at index " + i + ": Expected: " + namesFromClusterOrder.get(i) + ", But was: " + namesFromJson.get(i)));
    }

    public static void verifySubcontractMarginAndCapForQuotation(
            String quotationId, Double expectedMargin, Integer expectedCap,
            String accessToken
    ) throws JsonProcessingException {
        Response response = getAndAssertSubcontractPriceResponse(quotationId, accessToken, RESPONSE_STATUS_CODE_200);

        SubcontractResponseDto subcontractResponseDto = utils.convert.jsonToDto(response, SubcontractResponseDto.class);
        updateSubcontractPricing(quotationId, subcontractResponseDto, accessToken, expectedMargin, expectedCap);

        SubcontractResponseDto updatedSubcontractResponse = getAndAssertUpdatedSubcontractResponse(quotationId, accessToken, RESPONSE_STATUS_CODE_200);
        assertMarginAndCap(updatedSubcontractResponse, expectedMargin, expectedCap);
    }

    public static void updateSubcontractPricing(
            String quotationId, SubcontractResponseDto subcontractResponseDto,
            String adminToken, Double margin, Integer cap
    ) throws JsonProcessingException {
        PutSubcontractPricingRequest putRequest = new PutSubcontractPricingRequest(
                quotationId,
                utils.convert.dtoToJsonString(createSubcontractInputsDto(subcontractResponseDto, margin, cap)))
                .withBearerToken(adminToken);
        Response responsePutSubcontract = putRequest.callAPI();
        Assert.assertEquals(responsePutSubcontract.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
    }

    public static Response updateSubcontractPricing(
            String quotationId,
            SubcontractResponseDto subcontractResponseDto,
            String adminToken,
            Double margin,
            Integer cap,
            int expectedStatusCode
    ) throws JsonProcessingException {
        PutSubcontractPricingRequest putRequest = new PutSubcontractPricingRequest(
                quotationId,
                utils.convert.dtoToJsonString(createSubcontractInputsDto(subcontractResponseDto, margin, cap)))
                .withBearerToken(adminToken);
        Response responsePutSubcontract = putRequest.callAPI();
        Assert.assertEquals(responsePutSubcontract.statusCode(), expectedStatusCode, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
        return responsePutSubcontract;
    }

    public static SubcontractResponseDto getAndAssertUpdatedSubcontractResponse(
            String quotationId, String adminToken, int expectedStatusCode
    ) throws JsonProcessingException {
        Response response = getSubcontractPriceResponseWithBearerToken(quotationId, adminToken);
        Assert.assertEquals(response.statusCode(), expectedStatusCode, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
        return utils.convert.jsonToDto(response, SubcontractResponseDto.class);
    }

    public static void assertMarginAndCap(SubcontractResponseDto subcontractResponseDto, Double expectedMargin, Integer expectedCap) {
        List<Double> allMargins = extractSubcontractMarginValuesFromResponse(subcontractResponseDto);
        List<Integer> allCaps = extractSubcontractCapValuesFromResponse(subcontractResponseDto);

        Set<Double> uniqueMargins = allMargins != null ? new HashSet<>(allMargins) : Collections.emptySet();
        Set<Integer> uniqueCaps = allCaps != null ? new HashSet<>(allCaps) : Collections.emptySet();

        if (expectedMargin == null) {
            Assert.assertTrue(uniqueMargins.isEmpty() || uniqueMargins.contains(null), UNIQUE_CAPS_DO_NOT_MATCH_NULL);
        } else {
            Assert.assertEquals(uniqueMargins, Set.of(expectedMargin), DIFFERENCE_BETWEEN_ACTUAL_AND_EXPECTED_PRICE);
        }

        if (expectedCap == null) {
            Assert.assertTrue(uniqueCaps.isEmpty() || uniqueCaps.contains(null), UNIQUE_CAPS_DO_NOT_MATCH_NULL);
        } else {
            Assert.assertEquals(uniqueCaps, Set.of(expectedCap), DIFFERENCE_BETWEEN_ACTUAL_AND_EXPECTED_PRICE);
        }
    }

    public static Response getAndAssertSubcontractPriceResponse(String quotationId, String accessToken, int expectedStatusCode) {
        Response response = getSubcontractPriceResponseWithBearerToken(quotationId, accessToken);
        Assert.assertEquals(response.statusCode(), expectedStatusCode, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
        return response;
    }

    public static List<Integer> extractSubcontractCapValuesFromResponse(SubcontractResponseDto subcontractResponseDto) {
        return extractSubcontractValuesFromResponse(subcontractResponseDto, SubcontractItemDto::getCap);
    }

    public static List<Double> extractSubcontractMarginValuesFromResponse(SubcontractResponseDto subcontractResponseDto) {
        return extractSubcontractValuesFromResponse(subcontractResponseDto, SubcontractItemDto::getMargin);
    }

    public static <T> List<T> extractSubcontractValuesFromResponse(
            SubcontractResponseDto subcontractResponseDto, Function<SubcontractItemDto, T> mapper
    ) {
        if (subcontractResponseDto != null && subcontractResponseDto.getData() != null && subcontractResponseDto.getData().getSubcontracts() != null) {
            return subcontractResponseDto.getData().getSubcontracts().stream()
                    .map(mapper)
                    .collect(Collectors.toList());
        }
        return List.of();
    }

    public static List<SubcontractItemInputDto> transformSubcontractItems(
            SubcontractResponseDto subcontractResponseDto, Double margin, Integer cap
    ) {
        if (subcontractResponseDto != null && subcontractResponseDto.getData() != null && subcontractResponseDto.getData().getSubcontracts() != null) {
            return subcontractResponseDto.getData().getSubcontracts().stream()
                    .map(subcontractItem -> new SubcontractItemInputDto(
                            subcontractItem.getId(),
                            margin,
                            cap
                    ))
                    .collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    public static SubcontractInputsDto createSubcontractInputsDto(
            SubcontractResponseDto subcontractResponseDto, Double margin, Integer cap
    ) {
        return new SubcontractInputsDto(transformSubcontractItems(subcontractResponseDto, margin, cap));
    }
}
