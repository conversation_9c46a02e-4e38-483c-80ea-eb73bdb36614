package com.lht.corecalculation.api.utils;

import com.lht.corecalculation.api.pojo.dto.pmarating.ClusterDto;
import com.lht.corecalculation.api.pojo.dto.pmarating.PartDto;
import com.lht.corecalculation.api.pojo.dto.pmarating.PmaRatingInputDto;
import com.lht.corecalculation.api.pojo.dto.pmarating.PmaRatingRequestDto;
import com.lht.corecalculation.api.pojo.dto.pmarating.PmaResponseDto;
import com.lht.corecalculation.api.pojo.dto.pmarating.YearDto;
import com.lht.corecalculation.api.request.pmarating.GetPmaRatingsRequest;
import com.lht.corecalculation.api.request.pmarating.PutPmaRatingsRequest;
import com.lht.corecalculation.base.BaseCocaApiTest;
import com.lht.corecalculation.base.cp.utils.Utils;
import io.restassured.response.Response;
import java.io.IOException;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.function.Function;


import static com.lht.corecalculation.api.constants.GlobalConstants.ERROR_DETAILS_MISMATCH_AS_MESSAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_200;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE;
import static org.testng.Assert.assertEquals;

public class PmaRatingsUtil extends BaseCocaApiTest {

    public static Utils utils = new Utils();

    public static PmaResponseDto fetchPmaRatingsResponseDto(String accessToken, String quotationId) throws IOException {
        GetPmaRatingsRequest getPmaRatingsRequest = new GetPmaRatingsRequest(quotationId).withBearerToken(accessToken);
        Response response = getPmaRatingsRequest.callAPI();
        assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        return utils.convert.jsonToDto(response, PmaResponseDto.class);
    }

    public static List<PmaRatingInputDto> buildPmaRatingInputs(List<PartDto> parts, Double pmaRatingInputValue) {
        return parts.stream()
                .map(part -> {
                    PmaRatingInputDto dto = new PmaRatingInputDto();
                    dto.setId(part.getId());
                    dto.setPmaRatingInput(pmaRatingInputValue);
                    return dto;
                })
                .toList();
    }

    public static PmaRatingRequestDto createPmaRatingsUpdate(List<PmaRatingInputDto> pmaRatingInputs) {
        PmaRatingRequestDto pmaRatingsUpdate = new PmaRatingRequestDto();
        pmaRatingsUpdate.setPmaRatingsInput(pmaRatingInputs);
        return pmaRatingsUpdate;
    }

    public static Response updatePmaRatings(String token, String body, String quotationId) {
        PutPmaRatingsRequest request = new PutPmaRatingsRequest(quotationId, body).withBearerToken(token);
        return request.callAPI();
    }

    public static void assertResponseStatusCode(Response response, int expectedStatusCode) {
        assertEquals(response.statusCode(), expectedStatusCode, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
    }

    public static <T> List<T> extractFromClusters(PmaResponseDto responseDto, Function<ClusterDto, T> mapper) {
        return responseDto.getData().getYears().stream()
                .flatMap(year -> year.getClusters().stream())
                .map(mapper)
                .distinct()
                .toList();
    }

    public static <T> List<T> extractFromParts(PmaResponseDto responseDto, Function<PartDto, T> mapper) {
        return responseDto.getData().getYears().stream()
                .flatMap(year -> year.getClusters().stream())
                .flatMap(cluster -> cluster.getParts().stream())
                .map(mapper)
                .distinct()
                .toList();
    }

    public static <T> List<T> extractFromYears(PmaResponseDto responseDto, Function<YearDto, T> mapper) {
        return responseDto.getData().getYears().stream()
                .map(mapper)
                .distinct()
                .toList();
    }

    public static void validateUpdatedRatings(PmaResponseDto responseDtoAfterUpdate, Double expectedValue) {
        List<String> extractedUniquePmaRatingsValues =
                Collections.singletonList(extractFromParts(responseDtoAfterUpdate, PartDto::getPmaRatingInput).toString());
        assertEquals(extractedUniquePmaRatingsValues, Collections.singletonList("[" + expectedValue + "]"), ERROR_DETAILS_MISMATCH_AS_MESSAGE);
    }

    public static <T> List<T> getAttributeForSpecificYear(PmaResponseDto responseDto, String targetYear, Function<PartDto, T> mapper) {
        List<YearDto> extractedYears = extractFromYears(responseDto, Function.identity());

        return extractedYears.stream()
                .filter(yearDto -> yearDto.getYear().equals(targetYear))
                .flatMap(yearDto -> yearDto.getClusters().stream())
                .flatMap(clusterDto -> clusterDto.getParts().stream())
                .map(mapper)
                .toList();
    }

    public static List<PmaRatingInputDto> buildPmaRatingInputsSpecificYearAndValue(
            List<PartDto> parts, Double pmaRatingInputValue, PmaResponseDto responseDto, String targetYear, Double value
    ) {
        return parts.stream()
                .map(part -> {
                    PmaRatingInputDto dto = new PmaRatingInputDto();
                    dto.setId(part.getId());

                    Optional<YearDto> yearDtoForPart = responseDto.getData().getYears().stream()
                            .filter(year -> year.getClusters().stream().anyMatch(cluster -> cluster.getParts().contains(part)))
                            .findFirst();

                    if (yearDtoForPart.isPresent() && yearDtoForPart.get().getYear().equals(targetYear)) {
                        dto.setPmaRatingInput(value);
                    } else {
                        dto.setPmaRatingInput(pmaRatingInputValue);
                    }
                    return dto;
                })
                .toList();
    }
}
