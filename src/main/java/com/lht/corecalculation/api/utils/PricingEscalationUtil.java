package com.lht.corecalculation.api.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.lht.corecalculation.api.pojo.dto.pricingescalation.EscalationInputDto;
import com.lht.corecalculation.api.pojo.dto.pricingescalation.EscalationInputRequestDto;
import com.lht.corecalculation.api.pojo.dto.pricingescalation.EscalationResponseDto;
import com.lht.corecalculation.api.pojo.dto.pricingescalation.EscalationsPricingDto;
import com.lht.corecalculation.api.pojo.dto.z2rating.ResponseDto;
import com.lht.corecalculation.api.pojo.dto.z2rating.YearDto;
import com.lht.corecalculation.api.request.pricingescalation.GetPricingEscalationRequest;
import com.lht.corecalculation.api.request.pricingescalation.PutPricingEscalationRequest;
import com.lht.corecalculation.api.request.z2ratings.GetZ2RatingsRequest;
import com.lht.corecalculation.base.BaseCocaApiTest;
import com.lht.corecalculation.base.cp.utils.Utils;
import io.restassured.response.Response;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.testng.Assert;


import static com.lht.corecalculation.api.constants.GlobalConstants.EPAR_PRICES_VALUE_MISMATCH;
import static com.lht.corecalculation.api.constants.GlobalConstants.HC_MATERIAL_PRICES_VALUE_MISMATCH;
import static com.lht.corecalculation.api.constants.GlobalConstants.HC_SUBCONTRACT_PRICES_VALUE_MISMATCH;
import static com.lht.corecalculation.api.constants.GlobalConstants.LABOUR_PRICES_VALUE_MISMATCH;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_200;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.RFP_LABOUR_VALUE_MISMATCH;
import static com.lht.corecalculation.api.constants.GlobalConstants.WORKSCOPE_PRICES_VALUE_MISMATCH;
import static java.util.stream.Collectors.toList;

public class PricingEscalationUtil extends BaseCocaApiTest {

    public static Utils utils = new Utils();

    public static List<String> extractYearsFromResponse(Response response) throws JsonProcessingException {
        EscalationResponseDto escalationResponseDto = utils.convert.jsonToDto(response, EscalationResponseDto.class);
        return escalationResponseDto.getData().getEscalationsPricing()
                .stream()
                .map(EscalationsPricingDto::getYear)
                .collect(toList());
    }

    public static ResponseDto fetchZ2RatingsResponseDto(String accessToken, String quotationId) throws IOException {
        GetZ2RatingsRequest getZ2RatingsRequest = new GetZ2RatingsRequest(quotationId).withBearerToken(accessToken);
        Response response = getZ2RatingsRequest.callAPI();
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
        return utils.convert.jsonToDto(response, ResponseDto.class);
    }

    public static List<String> extractActualYearsWithoutBaseYear(String accessToken, String quotationId) throws IOException {
        ResponseDto responseDto = fetchZ2RatingsResponseDto(accessToken, quotationId);
        List<String> actualYearsInQuotation = Z2RatingsUtil.extractFromYears(responseDto, YearDto::getYear);
        if (!actualYearsInQuotation.isEmpty()) {
            actualYearsInQuotation.remove(0); // Removes the base year
        }
        return actualYearsInQuotation;
    }

    public static void extractIdsAndYears(EscalationResponseDto escalationResponseDto, EscalationInputRequestDto escalationInputDto) {
        List<EscalationsPricingDto> escalationsPricingDtoList = escalationResponseDto.getData().getEscalationsPricing();
        for (EscalationsPricingDto dto : escalationsPricingDtoList) {
            EscalationInputDto input = new EscalationInputDto();
            input.setId(dto.getId());
            input.setYear(dto.getYear());
            escalationInputDto.getEscalationInputs().add(input);
        }
    }

    public static void modifyValuesInEscalationInput(
            EscalationInputRequestDto escalationInputDto, Double eparPrices, Double hcMaterialPrices,
            Double hcSubcontractPrices, Double labourPrices, Double rfpLabour
    ) {
        modifyValuesInEscalationInput(
                escalationInputDto, eparPrices, hcMaterialPrices,
                hcSubcontractPrices, labourPrices, rfpLabour, null);
    }

    public static void modifyValuesInEscalationInput(
            EscalationInputRequestDto escalationInputDto, Double eparPrices, Double hcMaterialPrices, Double hcSubcontractPrices,
            Double labourPrices, Double rfpLabour, Double fpNtePrices
    ) {
        for (EscalationInputDto input : escalationInputDto.getEscalationInputs()) {
            input.setEparPrices(eparPrices);
            input.setHcMaterialPrices(hcMaterialPrices);
            input.setHcSubcontractPrices(hcSubcontractPrices);
            input.setLabourPrices(labourPrices);
            input.setRfpLabour(rfpLabour);

            if (fpNtePrices != null) {
                input.setFpNtePrices(fpNtePrices);
            }
        }
    }

    public static Response getEscalationPricingResponseWithBearerToken(String quotationId, String accessToken) {
        GetPricingEscalationRequest getEscalationPricingRequest = new GetPricingEscalationRequest(quotationId).withBearerToken(accessToken);
        return getEscalationPricingRequest.callAPI();
    }

    public static List<Double> extractValuesFromResponse(
            Response response, Function<EscalationsPricingDto, Double> valueExtractor
    ) throws JsonProcessingException {
        EscalationResponseDto escalationResponseDto = utils.convert.jsonToDto(response, EscalationResponseDto.class);
        return escalationResponseDto.getData().getEscalationsPricing().stream()
                .map(EscalationsPricingDto -> valueExtractor.apply(EscalationsPricingDto)).collect(Collectors.toList());
    }

    public static List<Double> extractDistinctValuesFromResponse(
            Response response, Function<EscalationsPricingDto, Double> valueExtractor
    ) throws JsonProcessingException {
        EscalationResponseDto escalationResponseDto = utils.convert.jsonToDto(response, EscalationResponseDto.class);
        return escalationResponseDto.getData().getEscalationsPricing().stream()
                .map(valueExtractor)
                .distinct()
                .collect(Collectors.toList());
    }

    public static Response putEscalationPricingResponseWithBearerToken(
            String quotationId, String accessToken, EscalationInputRequestDto escalationInputDto
    ) throws JsonProcessingException {
        PutPricingEscalationRequest putPricingEscalationRequest =
                new PutPricingEscalationRequest(quotationId, utils.convert.dtoToJsonString(escalationInputDto))
                        .withBearerToken(accessToken);
        return putPricingEscalationRequest.callAPI();
    }

    public static void assertExpectedAndActualPricingEscalationValues(
            String quotationId, String adminToken, EscalationInputRequestDto escalationInputDto, Double eparPrices, Double hcMaterialPrices,
            Double hcSubcontractPrices, Double labourPrices, Double rfpLabour
    ) throws JsonProcessingException {
        assertExpectedAndActualPricingEscalationValues(
                quotationId, adminToken, escalationInputDto,
                eparPrices, hcMaterialPrices, hcSubcontractPrices,
                labourPrices, rfpLabour, null);
    }

    public static void assertExpectedAndActualPricingEscalationValues(
            String quotationId, String adminToken, EscalationInputRequestDto escalationInputDto, Double eparPrices, Double hcMaterialPrices,
            Double hcSubcontractPrices, Double labourPrices, Double rfpLabour, Double fpNtePrices
    ) throws JsonProcessingException {
        // Send PUT request
        Response responsePutPricingEscalation = putEscalationPricingResponseWithBearerToken(quotationId, adminToken, escalationInputDto);

        // Extract actual values from the response
        List<Double> eparPricesActualValue =
                extractDistinctValuesFromResponse(responsePutPricingEscalation, dto -> dto.getValues().getEparPrices().getValue());
        List<Double> hcMaterialPricesActualValue =
                extractDistinctValuesFromResponse(responsePutPricingEscalation, dto -> dto.getValues().getHcMaterialPrices().getValue());
        List<Double> hcSubcontractPricesActualValue =
                extractDistinctValuesFromResponse(responsePutPricingEscalation, dto -> dto.getValues().getHcSubcontractPrices().getValue());
        List<Double> labourPricesActualValue =
                extractDistinctValuesFromResponse(responsePutPricingEscalation, dto -> dto.getValues().getLabourPrices().getValue());
        List<Double> rfpLabourActualValue =
                extractDistinctValuesFromResponse(responsePutPricingEscalation, dto -> dto.getValues().getRfpLabour().getValue());

        // Perform assertions
        assertValuesMatch(eparPricesActualValue, eparPrices, EPAR_PRICES_VALUE_MISMATCH);
        assertValuesMatch(hcMaterialPricesActualValue, hcMaterialPrices, HC_MATERIAL_PRICES_VALUE_MISMATCH);
        assertValuesMatch(hcSubcontractPricesActualValue, hcSubcontractPrices, HC_SUBCONTRACT_PRICES_VALUE_MISMATCH);
        assertValuesMatch(labourPricesActualValue, labourPrices, LABOUR_PRICES_VALUE_MISMATCH);
        assertValuesMatch(rfpLabourActualValue, rfpLabour, RFP_LABOUR_VALUE_MISMATCH);

        if (fpNtePrices != null) {
            List<Double> fpNtePricesActualValue = extractDistinctValuesFromResponse(
                    responsePutPricingEscalation, dto -> dto.getValues().getFpNtePrices().getValue());
            assertValuesMatch(fpNtePricesActualValue, fpNtePrices, WORKSCOPE_PRICES_VALUE_MISMATCH);
        }
    }

    private static void assertValuesMatch(List<Double> actualValues, Double expectedValue, String errorMessage) {
        Assert.assertEquals(actualValues, new ArrayList<>(Collections.singleton(expectedValue)), errorMessage);
    }
}
