package com.lht.corecalculation.api.utils;

import com.lht.corecalculation.api.pojo.dto.quotation.QuotationDetailsDto;
import com.lht.corecalculation.api.pojo.dto.z2rating.ClusterDto;
import com.lht.corecalculation.api.pojo.dto.z2rating.PartDto;
import com.lht.corecalculation.api.pojo.dto.z2rating.ResponseDto;
import com.lht.corecalculation.api.pojo.dto.z2rating.YearDto;
import com.lht.corecalculation.api.pojo.dto.z2rating.Z2RatingInputDto;
import com.lht.corecalculation.api.pojo.dto.z2rating.Z2RatingsRequestDto;
import com.lht.corecalculation.api.request.quotation.GetQuotationByIdRequest;
import com.lht.corecalculation.api.request.z2ratings.GetZ2RatingsRequest;
import com.lht.corecalculation.api.request.z2ratings.PutZ2RatingsRequest;
import com.lht.corecalculation.base.BaseCocaApiTest;
import com.lht.corecalculation.base.cp.utils.Utils;
import io.restassured.response.Response;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.testng.Assert;


import static com.lht.corecalculation.api.constants.GlobalConstants.ERROR_DETAILS_MISMATCH_AS_MESSAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_200;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE;
import static java.util.stream.Collectors.toList;

public class Z2RatingsUtil extends BaseCocaApiTest {

    public static Utils utils = new Utils();

    public static <T> List<T> extractFromYears(ResponseDto responseDto, Function<YearDto, T> mapper) {
        return responseDto.getData().getYears().stream()
                .map(mapper)
                .distinct()
                .collect(toList());
    }

    public static <T> List<T> extractFromClusters(ResponseDto responseDto, Function<ClusterDto, T> mapper) {
        return responseDto.getData().getYears().stream()
                .flatMap(year -> year.getClusters().stream())
                .map(mapper)
                .distinct()
                .collect(toList());
    }

    public static <T> List<T> extractFromParts(ResponseDto responseDto, Function<PartDto, T> mapper) {
        return responseDto.getData().getYears().stream()
                .flatMap(year -> year.getClusters().stream())
                .flatMap(cluster -> cluster.getParts().stream())
                .map(mapper)
                .distinct()
                .collect(toList());
    }

    public static List<Integer> getYearsBetween(long firstContractYear, long lastContractYear) {
        Calendar calendarFirstYear = Calendar.getInstance();
        calendarFirstYear.setTimeInMillis(firstContractYear);
        int year1 = calendarFirstYear.get(Calendar.YEAR);

        Calendar calendarLastYear = Calendar.getInstance();
        calendarLastYear.setTimeInMillis(lastContractYear);
        int year2 = calendarLastYear.get(Calendar.YEAR);

        List<Integer> years = new ArrayList<>();
        for (int i = year1; i <= year2; i++) {
            years.add(i);
        }
        return years;
    }

    public static <T> List<T> extractFromPartsUniqueValues(ResponseDto responseDto, Function<PartDto, T> mapper) {
        return responseDto.getData().getYears().stream()
                .flatMap(year -> year.getClusters().stream())
                .flatMap(cluster -> cluster.getParts().stream())
                .map(mapper)
                .distinct()
                .collect(toList());
    }

    public static List<Z2RatingInputDto> buildZ2RatingInputs(List<PartDto> parts, Double z2RatingInputValue) {
        return parts.stream()
                .map(part -> {
                    Z2RatingInputDto dto = new Z2RatingInputDto();
                    dto.setId(part.getId());
                    dto.setZ2RatingInput(z2RatingInputValue);
                    return dto;
                })
                .collect(toList());
    }

    public static List<Z2RatingInputDto> buildZ2RatingInputsSpecificYearAndValue(
            List<PartDto> parts, Double z2RatingInputValue, ResponseDto responseDto, String years, Double value
    ) {
        return parts.stream()
                .map(part -> {
                    Z2RatingInputDto dto = new Z2RatingInputDto();
                    dto.setId(part.getId());

                    Optional<YearDto> yearDtoForPart = responseDto.getData().getYears().stream()
                            .filter(year -> year.getClusters().stream().anyMatch(cluster -> cluster.getParts().contains(part)))
                            .findFirst();

                    // Check if the part is from year 2024
                    if (yearDtoForPart.isPresent() && yearDtoForPart.get().getYear().equals(years)) {
                        dto.setZ2RatingInput(value);
                        return dto;
                    } else {
                        dto.setZ2RatingInput(z2RatingInputValue);
                        return dto;
                    }
                })
                .collect(toList());
    }

    public static List<Z2RatingInputDto> buildZ2RatingInputsSpecificYearValueAndPartName(
            List<PartDto> parts, Double z2RatingInputValue, ResponseDto responseDto, String years, Double value, String partName
    ) {
        return parts.stream()
                .filter(part -> part.getName().equals(partName))
                .map(part -> {
                    Z2RatingInputDto dto = new Z2RatingInputDto();
                    dto.setId(part.getId());

                    Optional<YearDto> yearDtoForPart = responseDto.getData().getYears().stream()
                            .filter(year -> year.getClusters().stream().anyMatch(cluster -> cluster.getParts().contains(part)))
                            .findFirst();

                    // Check if the part is from the specified year
                    if (yearDtoForPart.isPresent() && String.valueOf(yearDtoForPart.get().getYear()).equals(years)) {
                        dto.setZ2RatingInput(value);
                        return dto;
                    } else {
                        dto.setZ2RatingInput(z2RatingInputValue);
                        return dto;
                    }
                })
                .collect(toList());
    }

    public static Z2RatingsRequestDto createRatingsUpdate(List<Z2RatingInputDto> z2RatingInputs) {
        Z2RatingsRequestDto ratingsUpdate = new Z2RatingsRequestDto();
        ratingsUpdate.setZ2ratingsInput(z2RatingInputs);
        return ratingsUpdate;
    }

    public static Response updateZ2Ratings(String token, String body, String quotationId) {
        PutZ2RatingsRequest request = new PutZ2RatingsRequest(quotationId, body).withBearerToken(token);
        return request.callAPI();
    }

    public static <T> List<T> getAttributeForSpecificYear(ResponseDto responseDto, String targetYear, Function<PartDto, T> mapper) {
        List<YearDto> extractedYears = Z2RatingsUtil.extractFromYears(responseDto, Function.identity());

        return extractedYears.stream()
                .filter(yearDto -> yearDto.getYear().equals(targetYear))
                .flatMap(yearDto -> yearDto.getClusters().stream())
                .flatMap(clusterDto -> clusterDto.getParts().stream())
                .map(mapper)
                .collect(toList());
    }

    public static <T> List<T> getAttributeForSpecificYearForPartName(
            ResponseDto responseDto,
            String targetYear,
            String targetPartName,
            Function<PartDto, T> mapper
    ) {
        List<YearDto> extractedYears = Z2RatingsUtil.extractFromYears(responseDto, Function.identity());

        return extractedYears.stream()
                .filter(yearDto -> yearDto.getYear().equals(targetYear))
                .flatMap(yearDto -> yearDto.getClusters().stream())
                .flatMap(clusterDto -> clusterDto.getParts().stream())
                .filter(partDto -> partDto.getName().equals(targetPartName))
                .map(mapper)
                .collect(toList());
    }

    public static ResponseDto fetchZ2RatingsResponseDto(String accessToken, String quotationId) throws IOException {
        GetZ2RatingsRequest getZ2RatingsRequest = new GetZ2RatingsRequest(quotationId).withBearerToken(accessToken);
        Response response = getZ2RatingsRequest.callAPI();
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        return utils.convert.jsonToDto(response, ResponseDto.class);
    }

    public static List<String> fetchQuotationYears(String accessToken, String quotationId) throws IOException {
        GetQuotationByIdRequest getQuotationByIdRequest = new GetQuotationByIdRequest(quotationId).withBearerToken(accessToken);
        Response response = getQuotationByIdRequest.callAPI();
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        QuotationDetailsDto quotationDetailsDto = utils.convert.jsonToDto(response, QuotationDetailsDto.class);
        List<Integer> years = Z2RatingsUtil.getYearsBetween(
                quotationDetailsDto.getQuotation().getContractStart(),
                quotationDetailsDto.getQuotation().getContractEnd());

        return years.stream().map(Object::toString).collect(Collectors.toList());
    }

    public static void validateUpdatedRatings(ResponseDto responseDtoAfterUpdate, Double expectedValue) {
        List<String> extractedUniqueZ2RatingsValues =
                Collections.singletonList(Z2RatingsUtil.extractFromPartsUniqueValues(responseDtoAfterUpdate, PartDto::getZ2RatingInput).toString());
        Assert.assertEquals(extractedUniqueZ2RatingsValues, Collections.singletonList("[" + expectedValue + "]"), ERROR_DETAILS_MISMATCH_AS_MESSAGE);
    }

    public static void assertResponseStatusCode(Response response, int expectedStatusCode) {
        Assert.assertEquals(response.statusCode(), expectedStatusCode, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
    }
}

