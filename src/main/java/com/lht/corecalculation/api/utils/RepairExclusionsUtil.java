package com.lht.corecalculation.api.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.lht.corecalculation.api.pojo.dto.repairexclusions.ClusterDto;
import com.lht.corecalculation.api.pojo.dto.repairexclusions.PartDto;
import com.lht.corecalculation.api.pojo.dto.repairexclusions.RepairDto;
import com.lht.corecalculation.api.pojo.dto.repairexclusions.RepairExclusionInputDto;
import com.lht.corecalculation.api.pojo.dto.repairexclusions.RepairExclusionWorkscopeDataDto;
import com.lht.corecalculation.api.pojo.dto.repairexclusions.RepairExclusionsRequestDto;
import com.lht.corecalculation.api.request.repairexclusions.GetRepairExclusionsRequest;
import com.lht.corecalculation.api.request.repairexclusions.PutRepairExclusionsRequest;
import com.lht.corecalculation.base.cp.utils.Utils;
import io.restassured.response.Response;
import java.io.IOException;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import org.testng.Assert;


import static com.lht.corecalculation.api.constants.GlobalConstants.MESSAGE_NOT_ALL_IS_EXCLUDED_VALUES_ARE_FALSE;
import static com.lht.corecalculation.api.constants.GlobalConstants.MESSAGE_NOT_ALL_IS_EXCLUDED_VALUES_ARE_TRUE;
import static com.lht.corecalculation.api.constants.GlobalConstants.PART_TYPE_COMPONENT;
import static com.lht.corecalculation.api.constants.GlobalConstants.PART_TYPE_KIT;
import static com.lht.corecalculation.api.constants.GlobalConstants.PART_TYPE_NON_ROUTINE_MATERIAL;
import static com.lht.corecalculation.api.constants.GlobalConstants.PART_TYPE_PARTS_PACKAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.PART_TYPE_ROUTINE_MATERIAL;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_200;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE;
import static java.util.stream.Collectors.toList;
import static org.testng.Assert.assertEquals;
import static org.testng.Assert.assertTrue;

public class RepairExclusionsUtil {

    public static Utils utils = new Utils();

    public static RepairExclusionWorkscopeDataDto getRepairExclusionsResponse(
            String accessToken, String quotationId
    ) throws JsonProcessingException {
        GetRepairExclusionsRequest getRepairExclusionsRequest = new GetRepairExclusionsRequest(quotationId).withBearerToken(accessToken);

        Response response = getRepairExclusionsRequest.callAPI();
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        return utils.convert.jsonToDto(response, RepairExclusionWorkscopeDataDto.class);
    }

    public static Response sendPutRepairExclusionsRequest(
            String accessToken, String quotationId, RepairExclusionsRequestDto requestDto
    ) throws JsonProcessingException {
        return new PutRepairExclusionsRequest(quotationId, utils.convert.dtoToJsonString(requestDto))
                .withBearerToken(accessToken)
                .callAPI();
    }

    public static List<String> getUniqueClusterNames(RepairExclusionWorkscopeDataDto workscopeDataDto) {
        if (workscopeDataDto.getData() == null || workscopeDataDto.getData().getWorkscopes() == null) {
            return Collections.emptyList();
        }
        return workscopeDataDto.getData().getWorkscopes().stream()
                .flatMap(workscope -> workscope.getClusters().stream())
                .map(ClusterDto::getName)
                .distinct()
                .collect(toList());
    }

    public static List<String> getUniquePartNamesWithoutExcludedTypes(RepairExclusionWorkscopeDataDto workscopeDataDto) {
        if (workscopeDataDto.getData() == null || workscopeDataDto.getData().getWorkscopes() == null) {
            return Collections.emptyList();
        }
        return workscopeDataDto.getData().getWorkscopes().stream()
                .flatMap(workscope -> workscope.getClusters().stream())
                .filter(cluster -> !isExcludedPartType(cluster.getPartsType()))
                .flatMap(cluster -> cluster.getParts().stream())
                .map(PartDto::getName)
                .distinct()
                .collect(toList());
    }

    public static List<Boolean> getIsExcludedForClusterNameParts(RepairExclusionWorkscopeDataDto workscopeDataDto, String clusterName) {
        if (workscopeDataDto.getData() == null || workscopeDataDto.getData().getWorkscopes() == null) {
            return Collections.emptyList();
        }
        return workscopeDataDto.getData().getWorkscopes().stream()
                .flatMap(workscope -> workscope.getClusters().stream())
                .filter(cluster -> clusterName.equals(cluster.getName()))
                .flatMap(cluster -> cluster.getParts().stream())
                .flatMap(part -> part.getRepairs().stream())
                .map(RepairDto::getIsExcluded)
                .filter(isExcluded -> isExcluded != null)
                .collect(toList());
    }

    public static List<Integer> getAllRepairIds(RepairExclusionWorkscopeDataDto workscopeDataDto) {
        if (workscopeDataDto.getData() == null || workscopeDataDto.getData().getWorkscopes() == null) {
            return Collections.emptyList();
        }
        return workscopeDataDto.getData().getWorkscopes().stream()
                .flatMap(workscope -> workscope.getClusters().stream())
                .flatMap(cluster -> cluster.getParts().stream())
                .flatMap(part -> Optional.ofNullable(part.getRepairs()).orElse(Collections.emptyList()).stream())
                .map(RepairDto::getId)
                .filter(id -> id != null)
                .collect(toList());
    }

    public static List<Boolean> getAllIsExcluded(RepairExclusionWorkscopeDataDto workscopeDataDto) {
        if (workscopeDataDto.getData() == null || workscopeDataDto.getData().getWorkscopes() == null) {
            return Collections.emptyList();
        }
        return workscopeDataDto.getData().getWorkscopes().stream()
                .flatMap(workscope -> workscope.getClusters().stream())
                .flatMap(cluster -> cluster.getParts().stream())
                .flatMap(part -> Optional.ofNullable(part.getRepairs()).orElse(Collections.emptyList()).stream())
                .map(RepairDto::getIsExcluded)
                .filter(isExcluded -> isExcluded != null)
                .collect(toList());
    }

    public static RepairExclusionsRequestDto createRepairExclusionsRequestDto(List<Integer> repairIds, boolean isExcluded) {
        List<RepairExclusionInputDto> repairExclusionsInput = repairIds.stream()
                .map(id -> RepairExclusionInputDto.builder()
                        .id(id)
                        .isExcluded(isExcluded)
                        .build())
                .collect(toList());

        RepairExclusionsRequestDto requestDto = new RepairExclusionsRequestDto();
        requestDto.setRepairExclusionsInput(repairExclusionsInput);
        return requestDto;
    }

    public static void testRepairExclusionForEngine(
            String accessToken, String quotationId, boolean isExcluded, String errorMessage
    ) throws IOException {
        RepairExclusionWorkscopeDataDto initialData = RepairExclusionsUtil.getRepairExclusionsResponse(accessToken, quotationId);

        RepairExclusionsRequestDto requestDto = createRepairExclusionsRequestDto(getAllRepairIds(initialData), isExcluded);

        Response response = sendPutRepairExclusionsRequest(accessToken, quotationId, requestDto);
        assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        RepairExclusionWorkscopeDataDto updatedData = RepairExclusionsUtil.getRepairExclusionsResponse(accessToken, quotationId);
        List<Boolean> isExcludedValues = getAllIsExcluded(updatedData);

        if (isExcluded) {
            assertTrue(isExcludedValues.stream().allMatch(Boolean::booleanValue), errorMessage);
        } else {
            assertTrue(isExcludedValues.stream().noneMatch(Boolean::booleanValue), errorMessage);
        }
    }

    public static List<Integer> getRepairIdsForClusterName(RepairExclusionWorkscopeDataDto workscopeDataDto, String clusterName) {
        if (workscopeDataDto.getData() == null || workscopeDataDto.getData().getWorkscopes() == null) {
            return Collections.emptyList();
        }
        return workscopeDataDto.getData().getWorkscopes().stream()
                .flatMap(workscope -> workscope.getClusters().stream())
                .filter(cluster -> clusterName.equals(cluster.getName()))
                .flatMap(cluster -> cluster.getParts().stream())
                .flatMap(part -> part.getRepairs().stream())
                .map(RepairDto::getId)
                .filter(id -> id != null)
                .collect(toList());
    }

    private static boolean isExcludedPartType(String partType) {
        return PART_TYPE_KIT.equals(partType) ||
                PART_TYPE_COMPONENT.equals(partType) ||
                PART_TYPE_PARTS_PACKAGE.equals(partType) ||
                PART_TYPE_ROUTINE_MATERIAL.equals(partType) ||
                PART_TYPE_NON_ROUTINE_MATERIAL.equals(partType);
    }

    public static void updateRepairExclusionsForCluster(
            String accessToken, String quotationId, String clusterName, boolean isExcluded
    ) throws IOException {
        RepairExclusionWorkscopeDataDto workscopeDataDto = RepairExclusionsUtil.getRepairExclusionsResponse(accessToken, quotationId);

        List<Integer> repairIds = getRepairIdsForClusterName(workscopeDataDto, clusterName);

        RepairExclusionsRequestDto requestDto = createRepairExclusionsRequestDto(repairIds, isExcluded);

        Response response = sendPutRepairExclusionsRequest(accessToken, quotationId, requestDto);
        assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        RepairExclusionWorkscopeDataDto repairExclusionAfterUpdate = RepairExclusionsUtil.getRepairExclusionsResponse(accessToken, quotationId);
        List<Boolean> getAllIsExcludedValues = getIsExcludedForClusterNameParts(repairExclusionAfterUpdate, clusterName);

        if (isExcluded) {
            assertTrue(getAllIsExcludedValues.stream().allMatch(Boolean::booleanValue), MESSAGE_NOT_ALL_IS_EXCLUDED_VALUES_ARE_TRUE);
        } else {
            assertTrue(getAllIsExcludedValues.stream().noneMatch(Boolean::booleanValue), MESSAGE_NOT_ALL_IS_EXCLUDED_VALUES_ARE_FALSE);
        }
    }
}
