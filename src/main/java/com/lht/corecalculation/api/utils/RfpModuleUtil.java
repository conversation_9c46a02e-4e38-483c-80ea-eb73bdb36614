package com.lht.corecalculation.api.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.lht.corecalculation.api.pojo.dto.rfpmodule.RfpModuleItemDto;
import com.lht.corecalculation.api.pojo.dto.rfpmodule.RfpModuleItemInputDto;
import com.lht.corecalculation.api.pojo.dto.rfpmodule.RfpModuleRequestDto;
import com.lht.corecalculation.api.pojo.dto.rfpmodule.RfpModuleResponseDto;
import com.lht.corecalculation.api.pojo.dto.rfpmodule.WorkscopeDto;
import com.lht.corecalculation.api.request.rfpmodule.GetRfpModuleRequest;
import com.lht.corecalculation.api.request.rfpmodule.PutRfpModuleRequest;
import com.lht.corecalculation.base.BaseCocaApiTest;
import com.lht.corecalculation.base.cp.utils.Utils;
import io.restassured.response.Response;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import org.testng.Assert;


import static com.lht.corecalculation.api.constants.GlobalConstants.DBII_IN_TARGET_WORKSCOPE_NOT_FOUND;
import static com.lht.corecalculation.api.constants.GlobalConstants.DIFFERENCE_BETWEEN_ACTUAL_AND_EXPECTED_PRICE;
import static com.lht.corecalculation.api.constants.GlobalConstants.PERCENTAGE_DIVISOR;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_200;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.WORKSCOPE_NOT_FOUND;
import static java.util.stream.Collectors.toList;

public class RfpModuleUtil extends BaseCocaApiTest {

    public static Utils utils = new Utils();

    public static void checkForListMismatch(List<String> namesFromJson, List<String> namesFromClusterOrder) {
        IntStream.range(0, namesFromJson.size())
                .filter(i -> !namesFromJson.get(i).equals(namesFromClusterOrder.get(i)))
                .findFirst()
                .ifPresent(i -> Assert.fail("Mismatch found at index " + i + ": Expected: " + namesFromClusterOrder.get(i) + ", But was: " + namesFromJson.get(i)));
    }

    public static List<String> extractRfpClusterNamesFromResponse(Response response) throws JsonProcessingException {
        RfpModuleResponseDto responseDtoRfpModule = utils.convert.jsonToDto(response, RfpModuleResponseDto.class);
        List<WorkscopeDto> workscopes = responseDtoRfpModule.getData().getRfpModuleData().getWorkscopes();
        List<String> clusterNames = new ArrayList<>();
        for (WorkscopeDto workscope : workscopes) {
            workscope.getRfpItems().stream()
                    .map(RfpModuleItemDto::getName)
                    .forEach(clusterNames::add);
        }
        return clusterNames.stream().distinct().collect(toList());
    }

    public static List<String> extractRfpWorkscopesFromResponse(Response response) throws JsonProcessingException {
        RfpModuleResponseDto responseDtoRfpModule = utils.convert.jsonToDto(response, RfpModuleResponseDto.class);
        List<WorkscopeDto> workscopes = responseDtoRfpModule.getData().getRfpModuleData().getWorkscopes();
        List<String> clusterNames = new ArrayList<>();
        for (WorkscopeDto workscope : workscopes) {
            clusterNames.add(workscope.getName());
        }
        return clusterNames.stream().distinct().collect(toList());
    }

    public static List<Double> calculateSumPriceCostAndCiHoursForModule(RfpModuleResponseDto rfpModuleResponse) {
        List<Double> cost = extractCostValuesFromModuleResponse(rfpModuleResponse);
        List<Double> ciHoursCost = extractCiHoursCostValuesFromModuleResponse(rfpModuleResponse);
        List<Double> dbiiValues = extractDbiiValuesFromModuleResponse(rfpModuleResponse);
        return calculateSumCostAndCiHoursWithDbii(cost, ciHoursCost, dbiiValues);
    }

    public static List<Double> calculateSumCostAndCiHoursWithDbii(List<Double> cost, List<Double> ciHoursCost, List<Double> dbiiValues) {
        List<Double> sumCostAndCiHours = new ArrayList<>();
        int minLength = Math.min(cost.size(), Math.min(ciHoursCost.size(), dbiiValues.size()));
        for (int i = 0; i < minLength; i++) {
            double dbii = dbiiValues.get(i);
            sumCostAndCiHours.add((cost.get(i) + ciHoursCost.get(i)) / (1 - (dbii / PERCENTAGE_DIVISOR)));
        }
        return sumCostAndCiHours;
    }

    public static List<Double> calculateSumPriceCostForModuleWithoutCiHoursIncluded(RfpModuleResponseDto rfpModuleResponse) {
        List<Double> cost = extractCostValuesFromModuleResponse(rfpModuleResponse);
        List<Double> ciHoursCost = extractCiHoursCostValuesFromModuleResponse(rfpModuleResponse);
        List<Double> dbiiValues = extractDbiiValuesFromModuleResponse(rfpModuleResponse);
        return calculateSumCostAdjustedByDbii(cost, ciHoursCost, dbiiValues);
    }

    public static List<Double> calculateSumCostAdjustedByDbii(List<Double> cost, List<Double> ciHoursCost, List<Double> dbiiValues) {
        List<Double> sumCost = new ArrayList<>();
        int minLength = Math.min(cost.size(), Math.min(ciHoursCost.size(), dbiiValues.size()));
        for (int i = 0; i < minLength; i++) {
            double dbii = dbiiValues.get(i);
            sumCost.add(cost.get(i) / (1 - (dbii / PERCENTAGE_DIVISOR)));
        }
        return sumCost;
    }

    public static List<RfpModuleItemInputDto> transformRfpItemsWithModifiedDbii(
            RfpModuleResponseDto rfpModuleResponseDto,
            Double defaultDbiiValue,
            String targetWorkscopeName,
            Double targetWorkscopeDbiiValue
    ) {
        List<RfpModuleItemInputDto> rfpModuleItemInputs = new ArrayList<>();
        for (WorkscopeDto workscope : rfpModuleResponseDto.getData().getRfpModuleData().getWorkscopes()) {
            double dbiiValueToUse = workscope.getName().equals(targetWorkscopeName) ? targetWorkscopeDbiiValue : defaultDbiiValue;
            for (RfpModuleItemDto rfpModuleItem : workscope.getRfpItems()) {
                double price = rfpModuleItem.getCost() != null && rfpModuleItem.getCost() == 0 ? 0 : rfpModuleItem.getPrice();
                rfpModuleItemInputs.add(new RfpModuleItemInputDto(
                        rfpModuleItem.getId(),
                        price,
                        rfpModuleItem.getIsTestrunMaterial(),
                        dbiiValueToUse
                ));
            }
        }
        return rfpModuleItemInputs;
    }

    public static void verifyRfpModuleChangeGlobalDbii(
            String quotationId,
            String targetWorkscopeName,
            Boolean ciIncluded,
            Double defaultDbiiValue,
            Double targetWorkscopeDbiiValue,
            String accessToken
    ) throws JsonProcessingException {
        RfpModuleResponseDto rfpModuleResponse = getAndAssertRfpModule(accessToken, quotationId);

        List<RfpModuleItemInputDto> rfpModuleItemInputs = transformRfpItemsWithModifiedDbii(rfpModuleResponse, defaultDbiiValue, targetWorkscopeName, targetWorkscopeDbiiValue);
        callPutRfpModuleApi(accessToken, ciIncluded, rfpModuleItemInputs, quotationId);

        RfpModuleResponseDto updatedRfpModuleResponseDto = getAndAssertRfpModule(accessToken, quotationId);
        assertPrices(updatedRfpModuleResponseDto, ciIncluded);

        assertWorkscopeAndDbii(updatedRfpModuleResponseDto, targetWorkscopeName, targetWorkscopeDbiiValue);
    }

    public static void assertPrices(RfpModuleResponseDto rfpModuleResponseDto, Boolean ciIncluded) {
        List<Double> actualPrice = extractCiHoursPriceValuesFromModuleResponse(rfpModuleResponseDto);
        List<Double> calculatedValues = ciIncluded ? calculateSumPriceCostAndCiHoursForModule(rfpModuleResponseDto)
                : calculateSumPriceCostForModuleWithoutCiHoursIncluded(rfpModuleResponseDto);

        List<Integer> actualPriceRounded = roundDoublesToIntegers(actualPrice);
        List<Integer> calculatedValuesRounded = roundDoublesToIntegers(calculatedValues);

        Assert.assertEquals(actualPriceRounded, calculatedValuesRounded, DIFFERENCE_BETWEEN_ACTUAL_AND_EXPECTED_PRICE);
    }

    public static void assertWorkscopeAndDbii(
            RfpModuleResponseDto updatedRfpModuleResponseDto, String targetWorkscopeName, Double targetWorkscopeDbiiValue
    ) {
        Optional<WorkscopeDto> targetWorkscopeOptional = updatedRfpModuleResponseDto.getData().getRfpModuleData().getWorkscopes().stream()
                .filter(workscope -> targetWorkscopeName.equals(workscope.getName()))
                .findFirst();
        Assert.assertTrue(targetWorkscopeOptional.isPresent(), WORKSCOPE_NOT_FOUND);

        boolean dbiiValueCorrect = targetWorkscopeOptional.get().getRfpItems().stream()
                .anyMatch(item -> targetWorkscopeDbiiValue.equals(item.getDbii()));
        Assert.assertTrue(dbiiValueCorrect, DBII_IN_TARGET_WORKSCOPE_NOT_FOUND);
    }

    public static List<Integer> roundDoublesToIntegers(List<Double> values) {
        return values.stream()
                .mapToInt(Double::intValue)
                .boxed()
                .collect(Collectors.toList());
    }

    public static Response callPutRfpModuleApi(
            String accessToken, Boolean ciIncluded, List<RfpModuleItemInputDto> rfpModuleItemInputs,
            String quotationId
    ) throws JsonProcessingException {
        RfpModuleRequestDto rfpModuleRequestDto = new RfpModuleRequestDto(ciIncluded, rfpModuleItemInputs);
        PutRfpModuleRequest putRfpModuleRequest =
                new PutRfpModuleRequest(quotationId, utils.convert.dtoToJsonString(rfpModuleRequestDto))
                        .withBearerToken(accessToken);
        return putRfpModuleRequest.callAPI();
    }

    public static Response callGetRfpModuleApi(String accessToken, String quotationId) {
        GetRfpModuleRequest getRfpModuleRequest = new GetRfpModuleRequest(quotationId).withBearerToken(accessToken);
        return getRfpModuleRequest.callAPI();
    }

    public static RfpModuleResponseDto getAndAssertRfpModule(String adminToken, String quotationId) throws JsonProcessingException {
        Response rfpModuleResponse = callGetRfpModuleApi(adminToken, quotationId);
        Assert.assertEquals(rfpModuleResponse.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
        return utils.convert.jsonToDto(rfpModuleResponse, RfpModuleResponseDto.class);
    }

    public static List<Double> extractValuesFromModuleResponse(
            RfpModuleResponseDto rfpModuleResponseDto, Function<RfpModuleItemDto, Double> mapper
    ) {
        return rfpModuleResponseDto.getData().getRfpModuleData().getWorkscopes().stream()
                .flatMap(workscope -> workscope.getRfpItems().stream())
                .map(mapper)
                .collect(Collectors.toList());
    }

    public static List<Double> extractCostValuesFromModuleResponse(RfpModuleResponseDto rfpModuleResponseDto) {
        return extractValuesFromModuleResponse(rfpModuleResponseDto, RfpModuleItemDto::getCost);
    }

    public static List<Double> extractCiHoursCostValuesFromModuleResponse(RfpModuleResponseDto rfpModuleResponseDto) {
        return extractValuesFromModuleResponse(rfpModuleResponseDto, RfpModuleItemDto::getCiHoursCost);
    }

    public static List<Double> extractDbiiValuesFromModuleResponse(RfpModuleResponseDto rfpModuleResponseDto) {
        return extractValuesFromModuleResponse(rfpModuleResponseDto, RfpModuleItemDto::getDbii);
    }

    public static List<Double> extractCiHoursPriceValuesFromModuleResponse(RfpModuleResponseDto rfpModuleResponseDto) {
        return extractValuesFromModuleResponse(rfpModuleResponseDto, RfpModuleItemDto::getPrice);
    }

    public static void verifyGlobalRfpModuleChangeDbii(
            String engineTypeId,
            Boolean ciIncluded,
            Double dbiiValue,
            String accessToken
    ) throws JsonProcessingException {
        RfpModuleResponseDto rfpModuleResponse = getAndAssertRfpModule(accessToken, engineTypeId);
        List<RfpModuleItemInputDto> rfpModuleItemInputs = transformRfpItemsWithGlobalDbii(rfpModuleResponse, dbiiValue);
        callPutRfpModuleApi(accessToken, ciIncluded, rfpModuleItemInputs, engineTypeId);

        RfpModuleResponseDto updatedRfpModuleResponseDto = getAndAssertRfpModule(accessToken, engineTypeId);
        assertPrices(updatedRfpModuleResponseDto, ciIncluded);
    }

    private static List<RfpModuleItemInputDto> transformRfpItemsWithGlobalDbii(
            RfpModuleResponseDto rfpModuleResponseDto, Double dbiiValue
    ) {
        List<RfpModuleItemInputDto> rfpModuleItemInputs = new ArrayList<>();
        for (WorkscopeDto workscope : rfpModuleResponseDto.getData().getRfpModuleData().getWorkscopes()) {
            for (RfpModuleItemDto rfpModuleItem : workscope.getRfpItems()) {
                double price = Objects.equals(rfpModuleItem.getCost(), 0.0) ? 0 : rfpModuleItem.getPrice();
                rfpModuleItemInputs.add(new RfpModuleItemInputDto(
                        rfpModuleItem.getId(),
                        price,
                        rfpModuleItem.getIsTestrunMaterial(),
                        dbiiValue
                ));
            }
        }
        return rfpModuleItemInputs;
    }
}
