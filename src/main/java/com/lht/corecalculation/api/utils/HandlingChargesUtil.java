package com.lht.corecalculation.api.utils;

import com.google.gson.Gson;
import com.lht.corecalculation.api.pojo.dto.handlingcharges.Cluster;
import com.lht.corecalculation.api.pojo.dto.handlingcharges.HandlingChargesData;
import com.lht.corecalculation.api.pojo.dto.handlingcharges.Part;
import com.lht.corecalculation.api.pojo.dto.handlingcharges.ResultDto;
import com.lht.corecalculation.api.request.handlingcharges.GetHandlingChargesClustersParts;
import com.lht.corecalculation.api.request.handlingcharges.GetHandlingChargesRequest;
import com.lht.corecalculation.api.request.handlingcharges.PutHandlingChargesRequest;
import com.lht.corecalculation.base.BaseCocaApiTest;
import io.restassured.response.Response;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import org.testng.Assert;


import static com.lht.corecalculation.api.constants.GlobalConstants.ERROR_DETAILS_MISMATCH_AS_MESSAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.INVALID_CAP_CONFIGURATION_PART_NAME;
import static com.lht.corecalculation.api.constants.GlobalConstants.PART_TYPE_COMPONENT;
import static com.lht.corecalculation.api.constants.GlobalConstants.PART_TYPE_KIT;
import static com.lht.corecalculation.api.constants.GlobalConstants.PART_TYPE_NON_ROUTINE_MATERIAL;
import static com.lht.corecalculation.api.constants.GlobalConstants.PART_TYPE_PARTS_PACKAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.PART_TYPE_ROUTINE_MATERIAL;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_200;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE;

public class HandlingChargesUtil extends BaseCocaApiTest {

    public static final double MIN_VALUE = 0.1;
    public static final double MAX_VALUE = 99.9;

    public static Response getHandlingCharges(String accessToken, String quotationId) {
        GetHandlingChargesRequest getHandlingChargesRequest = new GetHandlingChargesRequest(quotationId).withBearerToken(accessToken);

        return getHandlingChargesRequest.callAPI();
    }

    public static void updateClustersAndParts(
            HandlingChargesData handlingChargesData, Double z1Value, Double z2Value, Double pmaValue, Double csmValue, String clusterName
    ) {
        for (Cluster cluster : handlingChargesData.getClusters()) {
            if (clusterName.equals(cluster.getName())) {
                for (Part part : cluster.getParts()) {
                    updatePartValues(part, z1Value, z2Value, pmaValue, csmValue);
                }
            }
        }
    }

    public static void updateClustersAndParts(
            HandlingChargesData handlingChargesData, Double z1Value, Double z2Value,
            Double pmaValue, Double csmValue, String clusterName, String partName
    ) {
        for (Cluster cluster : handlingChargesData.getClusters()) {
            if (clusterName.equals(cluster.getName())) {
                for (Part part : cluster.getParts()) {
                    if (partName.equals(part.getName())) { // Check if the part's name matches the given partName
                        updatePartValues(part, z1Value, z2Value, pmaValue, csmValue);
                    }
                }
            }
        }
    }

    private static void updatePartValues(Part part, Double z1Value, Double z2Value, Double pmaValue, Double csmValue) {
        part.setZ1(z1Value);
        part.setZ2(z2Value);
        part.setPma(pmaValue);
        part.setCsm(csmValue);
    }

    private static void updatePartValues(
            Part part, Double z1Value, Double z2Value, Double pmaValue, Double csmValue, Integer oneItemCap, Integer lineItemCapValue
    ) {
        updatePartValues(part, z1Value, z2Value, pmaValue, csmValue);
        updatePartCapValues(part, oneItemCap, lineItemCapValue);
    }

    private static void updatePartCapValues(Part part, Integer oneItemCap, Integer lineItemCapValue) {
        part.setOneItemCap(oneItemCap);
        part.setLineItemCap(lineItemCapValue);
    }

    public static void updateClustersAndPartsValues(HandlingChargesData handlingChargesData, String clusterName) {
        double currentValue = MIN_VALUE;

        Optional<Cluster> matchedCluster = handlingChargesData.getClusters().stream()
                .filter(cluster -> clusterName.equals(cluster.getName()))
                .findFirst();

        if (matchedCluster.isPresent()) {
            for (Part part : matchedCluster.get().getParts()) {
                updatePartValues(part, currentValue);
                currentValue = updateCurrentValue(currentValue);

                if (currentValue > MAX_VALUE) {
                    break;
                }
            }
        }
    }

    private static void updatePartValues(Part part, double currentValue) {
        part.setZ1(currentValue);
        part.setZ2(currentValue + MIN_VALUE);
        part.setPma(currentValue + 2 * MIN_VALUE);
        part.setCsm(currentValue + 3 * MIN_VALUE);
    }

    private static double updateCurrentValue(double currentValue) {
        return currentValue + 4 * MIN_VALUE;  // Increment for the next part.
    }

    public static HandlingChargesData getHandlingChargesData(String accessToken, String quotationId) {
        Response response = getHandlingCharges(accessToken, quotationId);
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
        return response.as(ResultDto.class).getData();
    }

    public static void updateAndAssertHandlingCharges(
            String accessToken, String quotationId, HandlingChargesData handlingChargesData, String clusterName,
            double expectedZ1, double expectedZ2, double expectedPma, double expectedCsm, String... partNames
    ) {
        String handlingChargesDataJson = new Gson().toJson(handlingChargesData);
        PutHandlingChargesRequest putHandlingChargesRequest = new PutHandlingChargesRequest(quotationId, handlingChargesDataJson)
                .withBearerToken(accessToken);
        Response responsePut = putHandlingChargesRequest.callAPI();
        Assert.assertEquals(responsePut.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
        HandlingChargesData putIndividualData = responsePut.as(ResultDto.class).getData();

        for (String partName : partNames) {
            String z1Value = GetHandlingChargesClustersParts.getZ1FromParts(putIndividualData, clusterName, partName);
            String z2Value = GetHandlingChargesClustersParts.getZ2FromParts(putIndividualData, clusterName, partName);
            String pmaValue = GetHandlingChargesClustersParts.getPmaFromParts(putIndividualData, clusterName, partName);
            String csmValue = GetHandlingChargesClustersParts.getCsmFromParts(putIndividualData, clusterName, partName);

            Assert.assertEquals(z1Value, Double.toString(expectedZ1), ERROR_DETAILS_MISMATCH_AS_MESSAGE);
            Assert.assertEquals(z2Value, Double.toString(expectedZ2), ERROR_DETAILS_MISMATCH_AS_MESSAGE);
            Assert.assertEquals(pmaValue, Double.toString(expectedPma), ERROR_DETAILS_MISMATCH_AS_MESSAGE);
            Assert.assertEquals(csmValue, Double.toString(expectedCsm), ERROR_DETAILS_MISMATCH_AS_MESSAGE);
        }
    }

    public static void updateClustersAndParts(
            HandlingChargesData handlingChargesData, Double z1Value, Double z2Value,
            Double pmaValue, Double csmValue, Integer oneItemCapValue, Integer lineItemCapValue
    ) {
        for (Cluster cluster : handlingChargesData.getClusters()) {
            updatePartValues(cluster, z1Value, z2Value, pmaValue, csmValue, oneItemCapValue, lineItemCapValue);

            Integer commonOneItemCap = findCommonValue(cluster.getParts(), Part::getOneItemCap);
            Integer commonLineItemCap = findCommonValue(cluster.getParts(), Part::getLineItemCap);

            cluster.setOneItemCap(commonOneItemCap);
            cluster.setLineItemCap(commonLineItemCap);
        }
    }

    public static void updateClustersAndPartsForCluster(
            HandlingChargesData handlingChargesData, Double z1Value, Double z2Value, Double pmaValue,
            Double csmValue, Integer oneItemCapValue, Integer lineItemCapValue, String clusterName
    ) {
        for (Cluster cluster : handlingChargesData.getClusters()) {
            if (cluster.getName().equals(clusterName)) {
                updatePartValues(cluster, z1Value, z2Value, pmaValue, csmValue, oneItemCapValue, lineItemCapValue);

                Integer commonOneItemCap = findCommonValue(cluster.getParts(), Part::getOneItemCap);
                Integer commonLineItemCap = findCommonValue(cluster.getParts(), Part::getLineItemCap);

                cluster.setOneItemCap(commonOneItemCap);
                cluster.setLineItemCap(commonLineItemCap);
            }
        }
    }

    public static <T> T findCommonValue(List<Part> parts, Function<Part, T> valueExtractor) {
        T commonValue = null;
        boolean isCommon = true;

        for (Part part : parts) {
            T value = valueExtractor.apply(part);
            if (commonValue == null) {
                commonValue = value;
            } else if (!Objects.equals(commonValue, value)) {
                isCommon = false;
                break;
            }
        }
        return isCommon ? commonValue : null;
    }

    public static void updatePartValues(
            Cluster cluster, Double z1Value, Double z2Value, Double pmaValue,
            Double csmValue, Integer oneItemCapValue, Integer lineItemCapValue
    ) {
        for (Part part : cluster.getParts()) {
            String partType = String.valueOf(part.getType());
            switch (partType) {
                case PART_TYPE_NON_ROUTINE_MATERIAL:
                case PART_TYPE_ROUTINE_MATERIAL:
                    updatePartValues(part, z1Value, null, null, null, null, null);
                    break;
                case PART_TYPE_PARTS_PACKAGE:
                case PART_TYPE_COMPONENT:
                case PART_TYPE_KIT:
                    updatePartValues(part, z1Value, z2Value, pmaValue, null, null, null);
                    break;
                default:
                    updatePartValues(part, z1Value, z2Value, pmaValue, csmValue);
                    updatePartCapValues(part, part.getQuantity() <= 1 ? oneItemCapValue : null,
                            part.getQuantity() > 1 ? lineItemCapValue : null);
                    break;
            }
        }
    }

    public static class Assertion {

        public static void validateCapConfiguration(ResultDto resultDto) {
            List<Cluster> clusters = resultDto.getData().getClusters();

            for (Cluster cluster : clusters) {
                List<Part> parts = cluster.getParts();
                for (Part part : parts) {
                    String errorMessage = String.format(
                            INVALID_CAP_CONFIGURATION_PART_NAME,
                            part.getName(),
                            part.getQuantity(),
                            part.getOneItemCap(),
                            part.getLineItemCap()
                    );
                    if (part.getQuantity() > 1) {
                        Assert.assertNull(part.getOneItemCap(), errorMessage);
                    } else {
                        Assert.assertNull(part.getLineItemCap(), errorMessage);
                    }
                }
            }
        }
    }
}