package com.lht.corecalculation.api.enums;

import com.lht.corecalculation.api.pojo.dto.quotation.QuotationRowDto;
import java.util.Comparator;

public enum QuotationComparator {

    BY_STATUS_AND_DATE((a, b) -> {
        int result = QuotationStatus.valueOf(a.getStatus()).ordinal() - QuotationStatus.valueOf(b.getStatus()).ordinal();
        if (result == 0) {
            return (int) (b.getLastUpdate() - a.getLastUpdate());
        }
        return result;
    }),

    BY_DATE(Comparator.comparing(QuotationRowDto::getLastUpdate).reversed()),

    BY_OFFER_NUMBER(Comparator.comparing(QuotationRowDto::getOfferNumber));

    private final Comparator<QuotationRowDto> comparator;

    QuotationComparator(Comparator<QuotationRowDto> comparator) {
        this.comparator = comparator;
    }

    public Comparator<QuotationRowDto> get() {
        return this.comparator;
    }
}
