package com.lht.corecalculation.api.enums;

public enum TestUser {

    ADMIN("Testuser Admin Core Calculation", "admin", "M888157", "adminToken"),
    VIEW_ONLY("Testuser View Only Core Calculation", "view_only", "M002412", "viewOnlyToken"),
    TEST_USER_1("Testuser User1 Core Calculation", "user", "M995681", "calcOneToken"),
    TEST_USER_2("Testuser User2 Core Calculation", "user", "M255086", "calcTwoToken"),
    TEST_USER_3("Testuser User3 Core Calculation", "user", "M143561", "calcThreeToken"),
    NO_ROLE_USER("Testuser NoRole Core Calculation", "no_role", "M081228", "noRoleToken");

    TestUser(String name, String role, String uNumber, String contextKey) {
        this.name = name;
        this.role = role;
        this.uNumber = uNumber;
        this.key = contextKey;
    }

    private final String name;
    private final String uNumber;
    private final String role;
    private final String key;

    public String getName() {
        return this.name;
    }

    public String getRole() {
        return this.role;
    }

    public String getContextKey() {
        return this.key;
    }

    public String getUNumber() {
        return this.uNumber;
    }
}
