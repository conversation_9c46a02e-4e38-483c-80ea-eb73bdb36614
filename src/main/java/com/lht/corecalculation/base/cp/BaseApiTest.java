package com.lht.corecalculation.base.cp;

import com.lht.corecalculation.base.cp.utils.Utils;
import com.qaprosoft.carina.core.foundation.IAbstractTest;
import java.lang.invoke.MethodHandles;
import javax.persistence.Persistence;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.testng.annotations.BeforeClass;

public abstract class BaseApiTest implements IAbstractTest {

    protected final Utils utils;
    protected final Logger log;

    public BaseApiTest() {
        this.utils = new Utils();
        this.log = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());
    }

    @BeforeClass(alwaysRun = true)
    public void setUpPersistence() {
        this.utils.persistence.setEmf(Persistence.createEntityManagerFactory("persistence"));
    }
}
