package com.lht.corecalculation.base.cp.utils.inner;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import io.restassured.response.Response;
import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;
import org.jetbrains.annotations.NotNull;


import static com.lht.corecalculation.api.constants.GlobalConstants.DTF;
import static com.lht.corecalculation.api.constants.GlobalConstants.JSON_FILES_PATH;
import static com.lht.corecalculation.api.constants.GlobalConstants.OUTPUT_FILE_NOT_CREATED_EX_MESSAGE;

public class Mapper {

    private final ObjectMapper mapper;

    public Mapper() {
        this.mapper = new ObjectMapper();
        this.mapper.disable(SerializationFeature.FAIL_ON_EMPTY_BEANS);
        this.mapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
    }

    public <T> T jsonToDto(@NotNull Response response, Class<T> returnType) throws JsonProcessingException {
        return mapper.readValue(response.asString(), returnType);
    }

    public <T> List<T> jsonToDto(Response response, TypeReference<List<T>> typeRef) throws JsonProcessingException {
        return mapper.readValue(response.asString(), typeRef);
    }

    public <T> T jsonToDto(@NotNull String responseBody, Class<T> returnType) throws JsonProcessingException {
        return mapper.readValue(responseBody, returnType);
    }

    public <T> T jsonToDto(@NotNull File jsonFIle, Class<T> returnType) throws IOException {
        return mapper.readValue(jsonFIle, returnType);
    }

    public String dtoToJsonString(@NotNull Object pojo) throws JsonProcessingException {
        return mapper.writeValueAsString(pojo);
    }

    public String dtoToFile(@NotNull Object pojo) throws IOException {
        File file = new File(JSON_FILES_PATH + "mapper-output-" + DTF.format(LocalDateTime.now()) + ".json");
        if (!file.createNewFile()) {
            throw new IOException(OUTPUT_FILE_NOT_CREATED_EX_MESSAGE);
        }
        mapper.writeValue(file, pojo);
        return file.getAbsolutePath();
    }
}