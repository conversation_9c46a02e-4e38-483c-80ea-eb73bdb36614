package com.lht.corecalculation.base.cp.utils.sso;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class DirectGrantAccessTokenDto {

    @JsonProperty(value = "access_token")
    private String accessToken;

    @JsonProperty(value = "expires_in")
    private Long expiresInMs;

    @JsonProperty(value = "refresh_token")
    private String refreshToken;

    @JsonProperty(value = "refresh_expires_in")
    private Long refreshExpiresInMs;
    @JsonProperty(value = "token_type")
    private String tokenType;

    public String parse() {
        return this.tokenType + " " + this.accessToken;
    }
}
