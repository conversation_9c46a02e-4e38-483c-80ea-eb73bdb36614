package com.lht.corecalculation.base.cp.utils.inner;

import java.util.HashMap;
import java.util.Map;

public class TestContext {

    protected final Map<String, Object> contents;

    public TestContext() {
        this.contents = new HashMap<>();
    }

    public boolean addContextItem(String key, Object value) {
        if (this.contents.get(key) != null) {
            return false;
        }
        this.contents.put(key, value);
        return true;
    }

    public void overwriteContextItem(String key, Object value) {
        this.contents.put(key, value);
    }

    public String getContextItem(String key) {
        Object value = this.contents.get(key);
        return value == null ? null : value.toString();
    }

    public boolean deleteContextItem(String key) {
        return this.contents.remove(key) != null;
    }
}
