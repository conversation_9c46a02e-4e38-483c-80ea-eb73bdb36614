package com.lht.corecalculation.base.cp.utils.sso;

import com.zebrunner.carina.api.AbstractApiMethodV2;
import com.zebrunner.carina.api.annotation.Endpoint;
import com.zebrunner.carina.api.http.HttpMethodType;
import com.zebrunner.carina.utils.R;


import static com.lht.corecalculation.api.constants.GlobalConstants.NON_PROD_SSO_URL;

@Endpoint(url = "${url}", methodType = HttpMethodType.POST)
public class DirectGrantAccessTokenRequest extends AbstractApiMethodV2 {

    public DirectGrantAccessTokenRequest(String usernameRef, String passwordRef) {
        this.replaceUrlPlaceholder("url", NON_PROD_SSO_URL);
        this.setHeaders("Content-Type=application/x-www-form-urlencoded");
        this.addParameter("grant_type", "password");
        this.addParameter("client_id", R.TESTDATA.get("sso_client_id"));
        this.addParameter("username", R.TESTDATA.get(usernameRef));
        this.addParameter("password", R.TESTDATA.get(passwordRef));
    }
}
