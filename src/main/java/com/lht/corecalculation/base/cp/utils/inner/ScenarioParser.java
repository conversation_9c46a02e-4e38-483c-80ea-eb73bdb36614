package com.lht.corecalculation.base.cp.utils.inner;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public class ScenarioParser {

    public boolean parseToTxt(String featuresDirPath, String outputFilePath) {
        File output = new File(outputFilePath);
        File inputDir = new File(featuresDirPath);

        List<File> featureFiles = Arrays.stream(Objects.requireNonNull(inputDir.listFiles()))
                .filter(f -> f.isFile() && f.getName().endsWith(".feature"))
                .sorted()
                .collect(Collectors.toList());

        StringBuilder outputAsSb = new StringBuilder();

        for (File ff : featureFiles) {
            try (BufferedReader br = new BufferedReader(new FileReader(ff))) {
                appendPrettyFeatureName(outputAsSb, ff);
                appendPrettyFeatureSteps(outputAsSb, br);
            } catch (IOException e) {
                return false;
            }
        }

        try (BufferedWriter bw = new BufferedWriter(new FileWriter(output, false))) {
            bw.write(outputAsSb.toString());
            bw.flush();
        } catch (IOException e) {
            return false;
        }

        return true;
    }

    private static void appendPrettyFeatureSteps(StringBuilder outputAsSb, BufferedReader br) throws IOException {
        String nextLine;
        while ((nextLine = br.readLine()) != null) {
            nextLine = nextLine
                    .replaceAll("Given", "GIVEN:")
                    .replaceAll("When", "WHEN:")
                    .replaceAll("Then", "THEN:")
                    .replaceAll("And", "AND:");

            outputAsSb.append(nextLine)
                    .append(System.lineSeparator());
        }
    }

    private static void appendPrettyFeatureName(StringBuilder outputAsSb, File file) {
        String name = "# ".concat(file.getName()
                        .replaceAll("\\.feature", "")
                        .replaceAll("-", " ")
                        .toUpperCase())
                .concat(" TESTS");

        name = name + " ".repeat(99 - name.length()) + "#";

        outputAsSb.append(System.lineSeparator())
                .append(System.lineSeparator())
                .append("#".repeat(100))
                .append(System.lineSeparator())
                .append(name)
                .append(System.lineSeparator())
                .append("#".repeat(100))
                .append(System.lineSeparator())
                .append(System.lineSeparator());
    }
}
