package com.lht.corecalculation.base.cp.utils.inner;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.lht.corecalculation.base.cp.utils.sso.DirectGrantAccessTokenDto;
import com.lht.corecalculation.base.cp.utils.sso.DirectGrantAccessTokenRequest;
import io.restassured.response.Response;

public class ProjectSSO {

    private final Mapper convert;

    public ProjectSSO(Mapper mapper) {
        this.convert = mapper;
    }

    public String getAccessTokenFor(String usernameRef, String passwordRef) throws JsonProcessingException {
        DirectGrantAccessTokenRequest directGrantAccessTokenRequest = new DirectGrantAccessTokenRequest(usernameRef, passwordRef);
        Response response = directGrantAccessTokenRequest.callAPI();

        if (response.statusCode() != 200) {
            throw new IllegalStateException("Access token could not be retrieved");
        }

        DirectGrantAccessTokenDto directGrantAccessTokenDto = convert.jsonToDto(response, DirectGrantAccessTokenDto.class);

        return directGrantAccessTokenDto.parse();
    }
}
