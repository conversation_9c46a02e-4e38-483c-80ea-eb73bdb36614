package com.lht.corecalculation.base.cp.utils;

import com.lht.corecalculation.base.cp.utils.inner.Generator;
import com.lht.corecalculation.base.cp.utils.inner.Mapper;
import com.lht.corecalculation.base.cp.utils.inner.Persistence;
import com.lht.corecalculation.base.cp.utils.inner.ProjectSSO;
import com.lht.corecalculation.base.cp.utils.inner.ScenarioParser;
import com.lht.corecalculation.base.cp.utils.inner.TestContext;

public class Utils {

    public final TestContext context = new TestContext();
    public final Mapper convert = new Mapper();
    public final Persistence persistence = new Persistence();
    public final ProjectSSO sso = new ProjectSSO(convert);
}
