package com.lht.corecalculation.base.cp.utils.inner;

import java.security.SecureRandom;
import java.time.LocalDateTime;
import org.apache.commons.lang3.RandomStringUtils;

public class Generator {

    private final SecureRandom secureRandom = new SecureRandom();

    public String randomString(int length, boolean hasDigits) {
        return hasDigits
                ? RandomStringUtils.random(length, 0, Character.MAX_CODE_POINT, true, true, null, secureRandom)
                : RandomStringUtils.random(length, 0, Character.MAX_CODE_POINT, false, false, null, secureRandom);
    }

    public String randomString(int minLength, int maxLength, boolean hasDigits) {
        int length = secureRandom.nextInt((maxLength - minLength) + 1) + minLength;

        return hasDigits
                ? RandomStringUtils.random(length, 0, Character.MAX_CODE_POINT, true, true, null, secureRandom)
                : RandomStringUtils.random(length, 0, Character.MAX_CODE_POINT, false, false, null, secureRandom);
    }

    public LocalDateTime localDateTime(long offsetMinutes) {
        return LocalDateTime.now().plusMinutes(offsetMinutes);
    }
}
