package com.lht.corecalculation.base.cp.utils.inner;

import java.util.Arrays;
import java.util.List;
import javax.persistence.EntityManager;
import javax.persistence.EntityManagerFactory;
import javax.persistence.Query;
import javax.persistence.TypedQuery;
import lombok.Setter;
import org.hibernate.Session;
import org.hibernate.Transaction;
import org.hibernate.exception.ConstraintViolationException;
import org.testng.Assert;

@Setter
public class Persistence {

    private EntityManagerFactory emf;

    public <T> List<T> getFromDB(String jqlQuery, Class<T> clazz, String... parameters) {
        EntityManager em = null;
        try {
            em = emf.createEntityManager();
            TypedQuery<T> query = em.createQuery(jqlQuery, clazz);
            Arrays.stream(parameters)
                    .forEach(param -> {
                        String[] tokens = param.split("::");
                        if ("int".equalsIgnoreCase(tokens[2])) {
                            query.setParameter(tokens[0], Integer.parseInt(tokens[1]));
                        } else if ("double".equalsIgnoreCase(tokens[2])) {
                            query.setParameter(tokens[0], Double.parseDouble(tokens[1]));
                        } else if ("long".equalsIgnoreCase(tokens[2])) {
                            query.setParameter(tokens[0], Long.parseLong(tokens[1]));
                        } else {
                            query.setParameter(tokens[0], tokens[1]);
                        }
                    });
            return query.getResultList();
        } finally {
            if (em != null) {
                em.close();
            }
        }
    }

    public int manipulateDB(String jqlQuery, String... parameters) {
        EntityManager em = null;
        try {
            em = emf.createEntityManager();
            Transaction transaction = ((Session) em.getDelegate()).beginTransaction();
            Query query = em.createQuery(jqlQuery);
            Arrays.stream(parameters)
                    .forEach(param -> {
                        String[] tokens = param.split("::");
                        if ("int".equalsIgnoreCase(tokens[2])) {
                            query.setParameter(tokens[0], Integer.parseInt(tokens[1]));
                        } else if ("double".equalsIgnoreCase(tokens[2])) {
                            query.setParameter(tokens[0], Double.parseDouble(tokens[1]));
                        } else if ("long".equalsIgnoreCase(tokens[2])) {
                            query.setParameter(tokens[0], Long.parseLong(tokens[1]));
                        } else {
                            query.setParameter(tokens[0], tokens[1]);
                        }
                    });
            int result = query.executeUpdate();
            transaction.commit();
            return result;

        } catch (ConstraintViolationException e) {
            Assert.fail("Could not manipulate DB", e);
            return -1;

        } finally {
            if (em != null) {
                em.close();
            }
        }
    }

    public int insertIntoDB(String jqlQuery, String... parameters) {
        EntityManager em = null;
        try {
            em = emf.createEntityManager();
            Transaction transaction = ((Session) em.getDelegate()).beginTransaction();
            Query query = em.createNativeQuery(jqlQuery);

            for (int i = 0; i < parameters.length; ) {
                String[] tokens = parameters[i++].split("::");
                if ("int".equalsIgnoreCase(tokens[1])) {
                    query.setParameter(i, Integer.parseInt(tokens[0]));
                } else if ("double".equalsIgnoreCase(tokens[1])) {
                    query.setParameter(i, Double.parseDouble(tokens[0]));
                } else if ("long".equalsIgnoreCase(tokens[1])) {
                    query.setParameter(i, Long.parseLong(tokens[0]));
                } else {
                    query.setParameter(i, tokens[0]);
                }
            }
            int result = query.executeUpdate();
            transaction.commit();
            return result;

        } catch (ConstraintViolationException e) {
            Assert.fail("Could not insert into DB", e);
            return -1;

        } finally {
            if (em != null) {
                em.close();
            }
        }
    }
}
