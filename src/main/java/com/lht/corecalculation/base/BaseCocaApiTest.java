package com.lht.corecalculation.base;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.lht.corecalculation.api.pojo.entity.user.UserEntity;
import com.lht.corecalculation.base.cp.BaseApiTest;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;


import static com.lht.corecalculation.api.constants.GlobalConstants.ACCESS_TOKEN_1;
import static com.lht.corecalculation.api.constants.GlobalConstants.ADMIN_TOKEN;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_CFM56_5B_PMA_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_CFM56_5B_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_CFM56_7B_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_LEAP_1A_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_LEAP_1B_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_V2500_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.NO_ROLE_TOKEN;
import static com.lht.corecalculation.api.constants.GlobalConstants.USER_1_TOKEN;
import static com.lht.corecalculation.api.constants.GlobalConstants.USER_2_TOKEN;
import static com.lht.corecalculation.api.constants.GlobalConstants.USER_3_TOKEN;
import static com.lht.corecalculation.api.constants.GlobalConstants.VIEW_ONLY_TOKEN;
import static com.lht.corecalculation.api.constants.GlobalConstants.WS1;
import static com.lht.corecalculation.api.constants.GlobalConstants.WS2;

public class BaseCocaApiTest extends BaseApiTest {

    private LocalDateTime lastTokenUpdate;

    @BeforeClass(alwaysRun = true)
    public void refreshAccessTokens() throws JsonProcessingException {
        if (lastTokenUpdate == null || lastTokenUpdate.plusMinutes(4L).isBefore(LocalDateTime.now())) {
            lastTokenUpdate = LocalDateTime.now();
            utils.context.overwriteContextItem(ADMIN_TOKEN, utils.sso.getAccessTokenFor("admin_user_username", "admin_user_password"));
            utils.context.overwriteContextItem(USER_1_TOKEN, utils.sso.getAccessTokenFor("calculation_user_01_username", "calculation_user_01_password"));
            utils.context.overwriteContextItem(USER_2_TOKEN, utils.sso.getAccessTokenFor("calculation_user_02_username", "calculation_user_02_password"));
            utils.context.overwriteContextItem(USER_3_TOKEN, utils.sso.getAccessTokenFor("calculation_user_03_username", "calculation_user_03_password"));
            utils.context.overwriteContextItem(VIEW_ONLY_TOKEN, utils.sso.getAccessTokenFor("view_only_user_username", "view_only_user_password"));
            utils.context.overwriteContextItem(NO_ROLE_TOKEN, utils.sso.getAccessTokenFor("no_role_user_username", "no_role_user_password"));
        }
    }

    // shared data providers
    @DataProvider(name = "calcAccessTokenProvider")
    public Object[][] supplyCalculationUserAccessToken() {
        return new Object[][] {
                {utils.context.getContextItem(USER_1_TOKEN)},
                {utils.context.getContextItem(USER_2_TOKEN)},
                {utils.context.getContextItem(USER_3_TOKEN)},
        };
    }

    @DataProvider(name = "adminAccessTokenProvider")
    public Object[][] supplyAdminAccessToken() {
        return new Object[][] {
                {utils.context.getContextItem(ADMIN_TOKEN)},
        };
    }

    @DataProvider(name = "viewOnlyAccessTokenProvider")
    public Object[][] supplyViewOnlyAccessToken() {
        return new Object[][] {
                {utils.context.getContextItem(VIEW_ONLY_TOKEN)},
        };
    }

    @DataProvider(name = "positiveValues")
    public Object[][] createPositiveData() {
        return new Object[][] {
                {ACCESS_TOKEN_1, 0.1, 0.1, 0.1, 0.1},
                {ACCESS_TOKEN_1, 0.1, 0.1, 0.1, 99.9},
                {ACCESS_TOKEN_1, 0.1, 0.1, 99.9, 0.1},
                {ACCESS_TOKEN_1, 0.1, 99.9, 0.1, 0.1},
                {ACCESS_TOKEN_1, 99.9, 0.1, 0.1, 0.1},
                {ACCESS_TOKEN_1, 99.9, 99.9, 99.9, 99.9},
                {ACCESS_TOKEN_1, 1, 10, 50, 99},
        };
    }

    @DataProvider(name = "positiveLabourRatesValues")
    public Object[][] createPositiveLabourRatesData() {
        return new Object[][] {
                {111111111, 999999999, 99.9},
                {1, 1, 0.1},
                {999999999, 111111111, 99.9}
        };
    }

    @DataProvider(name = "doubleValues")
    public Object[][] createDoubleData() {
        return new Object[][] {
                {ACCESS_TOKEN_1, 0.0},
                {ACCESS_TOKEN_1, 0.1},
                {ACCESS_TOKEN_1, 1.0},
                {ACCESS_TOKEN_1, 99.9}
        };
    }

    @DataProvider(name = "pmaValuesProvider")
    public Object[][] providePmaValues() {
        return new Object[][] {
                {10.5, 1.5, "2025"},
                {0.5, 0.9, "2026"},
                {0.0, 99.9, "2027"},
                {33.3, 0.9, "2028"}
        };
    }

    @DataProvider(name = "pmaInvalidValuesProvider")
    public Object[] provideInvalidPmaValues() {
        return new Object[] {
                -0.01,
                -1.0,
                100.0,
                100.01,
                100.1
        };
    }

    @DataProvider(name = "validDbiiValues")
    public Object[][] createValidDbiiValues() {
        return new Object[][] {
                {0.0, 0.2},
                {-1.0, 1.0},
                {-160.0, 90.0},
                {-12.3, -33.3}
        };
    }

    @DataProvider(name = "validSubcontractPricesValues")
    public Object[][] createValidSubcontractPriceValues() {
        return new Object[][] {
                {0.0, 1},
                {99.9, 999999999},
                {0.1, 1234}
        };
    }

    @DataProvider(name = "validPricingEscalationValues")
    public Object[][] createValidPricingEscalationsValues() {
        return new Object[][] {
                {0.0, 0.00, 0.0, 0.0, 00.00},
                {99.99, 99.99, 99.99, 99.99, 99.99},
                {0.01, 0.01, 0.01, 0.01, 0.01},
                {1.09, 10.55, 33.33, 44.44, 77.77}
        };
    }

    @DataProvider(name = "validPricingEscalationValuesWithNtePrices")
    public Object[][] createValidPricingEscalationsValuesWithNtePrices() {
        return new Object[][] {
                {0.0, 0.00, 0.0, 0.0, 00.00, 0.0},
                {99.99, 99.99, 99.99, 99.99, 99.99, 99.99},
                {0.01, 0.01, 0.01, 0.01, 0.01, 0.01},
                {1.09, 10.55, 33.33, 44.44, 77.77, 88.88}
        };
    }

    @DataProvider(name = "summaryProductionCostCFM56_5B")
    public Object[][] workscopeSummaryProductionCostDataProviderCFM56_5B() {
        return new Object[][] {
                {WS1, Arrays.asList(5063910.0, 5347609.0, 5637725.0)},
                {WS2, Arrays.asList(4505689.0, 4757288.0, 5015448.0)}
        };
    }

    @DataProvider(name = "summaryProductionCostCFM56_7B")
    public Object[][] workscopeSummaryProductionCostDataProviderCFM56_7B() {
        return new Object[][] {
                {WS1, Arrays.asList(3407855.0, 3588863.0, 3768615.0)},
                {WS2, Arrays.asList(2921279.0, 3071381.0, 3214421.0)}
        };
    }

    @DataProvider(name = "combinedDataProviderCFM56_5B")
    public Object[][] combinedDataProviderCFM56_5B() {
        return mergeDataProviders(supplyAdminAccessToken(), workscopeSummaryProductionCostDataProviderCFM56_5B());
    }

    @DataProvider(name = "combinedDataProviderCFM56_7B")
    public Object[][] combinedDataProviderCFM56_7B() {
        return mergeDataProviders(supplyAdminAccessToken(), workscopeSummaryProductionCostDataProviderCFM56_7B());
    }

    @DataProvider(name = "summaryDiscountsCFM56_5B")
    public Object[][] workscopeSummaryDiscountsDataProviderCFM56_5B() {
        return new Object[][] {
                {WS1, Arrays.asList(641667.0, 717881.0, 800027.0)},
                {WS2, Arrays.asList(580023.0, 649874.0, 726101.0)}
        };
    }

    @DataProvider(name = "summaryDiscountsCFM56_7B")
    public Object[][] workscopeSummaryDiscountsDataProviderCFM56_7B() {
        return new Object[][] {
                {WS1, Arrays.asList(323670.0, 364137.0, 409813.0)},
                {WS2, Arrays.asList(216932.0, 244083.0, 271675.0)}
        };
    }

    @DataProvider(name = "summaryRevenueCFM56_5B")
    public Object[][] workscopeSummaryRevenueDataProviderCFM56_5B() {
        return new Object[][] {
                {WS1, Arrays.asList(6580071.0, 6959623.0, 7362050.0)},
                {WS2, Arrays.asList(5804120.0, 6137255.0, 6490834.0)}
        };
    }

    @DataProvider(name = "summaryRevenueCFM56_7B")
    public Object[][] workscopeSummaryRevenueDataProviderCFM56_7B() {
        return new Object[][] {
                {WS1, Arrays.asList(4645310.0, 4909703.0, 5188507.0)},
                {WS2, Arrays.asList(4174981.0, 4404280.0, 4644847.0)}
        };
    }

    @DataProvider(name = "summarySurchargesCostCFM56_5B")
    public Object[][] workscopeSummarySurchargesCostDataProviderCFM56_5B() {
        return new Object[][] {
                {WS1, Arrays.asList(367216.0, 344708.0, 333272.0)},
                {WS2, Arrays.asList(366904.0, 344383.0, 332950.0)}
        };
    }

    @DataProvider(name = "summarySurchargesCostCFM56_7B")
    public Object[][] workscopeSummarySurchargesCostDataProviderCFM56_7B() {
        return new Object[][] {
                {WS1, Arrays.asList(350109.0, 326516.0, 313889.0)},
                {WS2, Arrays.asList(342710.0, 318651.0, 305458.0)}
        };
    }

    @DataProvider(name = "scrapCapDataProvider")
    public Object[][] scrapCapDataProvider() {
        return new Object[][] {
                {0.0F, 1},
                {0.1F, 100000000},
                {33.3F, 3333333},
                {99.9F, 999999999},
                {100F, 100000000}
        };
    }

    @DataProvider(name = "scrapCapNegativeDataProvider")
    public Object[][] scrapCapNegativeDataProvider() {
        return new Object[][] {
                {-0.1F, 100000},
                {12.3F, -1},
                {100.1F, 3333333}
        };
    }

    @DataProvider(name = "noRoleAccessTokenProvider")
    public Object[][] supplyNoRoleAccessToken() {
        return new Object[][] {
                {utils.context.getContextItem("noRoleToken")},
        };
    }

    @DataProvider(name = "expiredAccessTokenProvider")
    public Object[][] supplyExpiredAccessToken() {
        return new Object[][] {
                {"Bearer eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJyck9sU2laNzRITVBNNmlKS0ttZmRRQmZlV1RIdDl1aXY5UU1MYTRQdGhnIn0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.fxyEqtZlbyzs4r8s3hbkL1YaIFKa0_iIRsEJSJYNDtGEgej93KNz-WJFREaYhHEGOgDI4FbpfjJCIYaBjwHFPZuGkFyCKXoufqAzsk7OoxE92zUFevY4W3pzqEyHeLg4N2wB93xyJFT2Z8Dd7IM3otoJtVWQ3WkvzjjJpajQd6ldU9W5kdxdYqUkP_7LGDDcxhQrzbnWmckOckNz-WIncVc-Z2KtiwOzrp1w6zx5DsGywcrbRgpZewjNqpQU7UbKdsO-TvvSWVPDtQeD_QGgPlwE6HIIUAT90CaSXXcbzk63Hz9hMDCF5E0wzkp4tH4-NAtfXusX0MRq7C5BkA4ScQ"},
        };
    }

    @DataProvider(name = "quotationIdsByEngineProvider")
    public Object[][] supplyQuotationIdsByEngine() {
        return new Object[][] {
                {ENGINE_V2500_QUOTATION_ID},
                {ENGINE_CFM56_5B_QUOTATION_ID},
                {ENGINE_CFM56_7B_QUOTATION_ID},
                {ENGINE_LEAP_1B_QUOTATION_ID},
                {ENGINE_LEAP_1A_QUOTATION_ID},
                {ENGINE_CFM56_5B_PMA_QUOTATION_ID}
        };
    }

    @DataProvider(name = "quotationIdsCommitmentLetterProvider")
    public Object[][] quotationIdsByEngineCommitmentLetter() {
        return new Object[][] {
                {ENGINE_CFM56_5B_QUOTATION_ID},
                {ENGINE_CFM56_7B_QUOTATION_ID},
                {ENGINE_LEAP_1B_QUOTATION_ID},
                {ENGINE_LEAP_1A_QUOTATION_ID}
        };
    }

    protected int dbTransferOwnershipToAdmin(String userId) {
        return utils.persistence.manipulateDB(
                "UPDATE QuotationOwnerEntity qo " +
                        "SET qo.currentOwnerId = 1 " +
                        "WHERE qo.currentOwnerId = :userId",
                "userId::" + userId + "::long");
    }

    protected long dbGetCurrentProjectOwnerId(long originalQuotationId) {
        return utils.persistence.getFromDB(
                "SELECT u " +
                        "FROM QuotationEntity q " +
                        "JOIN QuotationOwnerEntity qo " +
                        "    ON q.id = qo.quotationId " +
                        "JOIN UserEntity u " +
                        "    ON u.id = qo.currentOwnerId " +
                        "WHERE q.id = :originalQuotationId",
                UserEntity.class,
                "originalQuotationId::" + originalQuotationId + "::long"
        ).get(0).getId();
    }

    protected int dbResetProjectOwner(long originalQuotationId, long ownerId) {
        return utils.persistence.manipulateDB(
                "UPDATE QuotationOwnerEntity qo " +
                        "SET qo.currentOwnerId = :ownerId " +
                        "WHERE qo.quotationId = :originalQuotationId",
                "ownerId::" + ownerId + "::long",
                "originalQuotationId::" + originalQuotationId + "::long"
        );
    }

    protected long dbGetUserIdByName(String userName) {
        List<UserEntity> userResults = utils.persistence.getFromDB(
                "SELECT u " +
                        "FROM UserEntity u " +
                        "WHERE u.fullName = :userName",
                UserEntity.class,
                "userName::" + userName + "::string");

        if (userResults.size() != 1) {
            throw new IllegalStateException();
        }
        return userResults.get(0).getId();
    }

    protected void dbInsertUser(String name, String email, String uNumber) {
        int insertionsMade = utils.persistence.insertIntoDB(
                "INSERT INTO users (name, email, u_number)" +
                        "VALUES (?,?,?)",
                name + "::string",
                email + "::string",
                uNumber + "::string");

        if (insertionsMade != 1) {
            throw new IllegalStateException();
        }
    }

    protected List<UserEntity> dbGetAllPersistedUsers() {
        return this.utils.persistence.getFromDB(
                "SELECT u " +
                        "FROM UserEntity u",
                UserEntity.class);
    }

    protected UserEntity dbGetUserById(long id) {
        List<UserEntity> fromDB = utils.persistence.getFromDB("SELECT u " +
                        "FROM UserEntity u " +
                        "WHERE u.id=:id",
                UserEntity.class,
                "id::" + id + "::long");

        if (fromDB.size() != 1) {
            throw new IllegalStateException();
        }
        return fromDB.get(0);
    }

    protected int dbDeleteUserByUNumber(String uNumber) {
        return utils.persistence.manipulateDB("DELETE FROM UserEntity " +
                        "WHERE uNumber = :uNumber",
                "uNumber::" + uNumber + "::string");
    }

    protected int dbSetDummyUserDetailsByUNumber(String uNumber) {
        return utils.persistence.manipulateDB("UPDATE UserEntity " +
                        "SET fullName = 'If you see this in DB', email = 'then something is wrong' " +
                        "WHERE uNumber = :uNumber",
                "uNumber::" + uNumber + "::string");
    }

    protected List<UserEntity> dbGetCurrentOwners() {
        return utils.persistence.getFromDB("SELECT u " +
                        "FROM QuotationOwnerEntity qo " +
                        "JOIN UserEntity u on u.id = qo.currentOwnerId ",
                UserEntity.class);
    }

    private Object[][] mergeDataProviders(Object[][] tokenData, Object[][] productionCostData) {
        Object[][] combinedData = new Object[productionCostData.length][3];
        for (int i = 0; i < productionCostData.length; i++) {
            combinedData[i][0] = tokenData[0][0]; // Admin Token
            combinedData[i][1] = productionCostData[i][0]; // Workscope
            combinedData[i][2] = productionCostData[i][1]; // Expected Production Cost
        }
        return combinedData;
    }
}
