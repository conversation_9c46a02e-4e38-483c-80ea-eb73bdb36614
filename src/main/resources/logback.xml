<?xml version="1.0" encoding="UTF-8"?>
<configuration>
  <property name="LHT_LOGGING_ENABLED" value="${LHT_LOGGING_ENABLED}"/>

  <if condition='property("LHT_LOGGING_ENABLED").contains("true")'>
    <then>
      <appender name="stash" class="net.logstash.logback.appender.LogstashTcpSocketAppender">
        <destination>${LHT_LOGGING_REMOTE_HOST}:${LHT_LOGGING_REMOTE_PORT}</destination>
        <encoder class="net.logstash.logback.encoder.LogstashEncoder">
          <fieldNames>
            <message>log-message</message>
          </fieldNames>
          <customFields>
            {
            "lhtappid": "${APP_ID}",
            "namespace": "${LHT_LOGGING_NAMESPACE}",
            "stage": "${LHT_LOGGING_STAGE}",
            "serviceName": "${APP_NAME}",
            "trackingId": "${TRACKING_ID}"
            }
          </customFields>
        </encoder>
      </appender>
      <root level = "INFO">
        <appender-ref ref="stash"/>
      </root>
    </then>
  </if>

  <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
    <encoder>
      <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
    </encoder>
  </appender>
  <root level = "INFO">
    <appender-ref ref="STDOUT"/>
  </root>
</configuration>
