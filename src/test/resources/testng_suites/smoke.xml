<!DOCTYPE suite SYSTEM "https://testng.org/testng-1.0.dtd">
<suite verbose="1" name="LHT CoCa API" parallel="methods">

    <parameter name="suiteOwner" value="nino.simeonov"/>
    <parameter name="jenkinsJobName" value="CoCa API Tests"/>
    <parameter name="jenkinsJobType" value="smoke"/>
    <parameter name="jenkinsEnvironments" value="TEST"/>
    <parameter name="jenkinsEmail" value="<EMAIL>"/>

    <parameter name="jenkinsPipelineEnvironments" value="TEST"/>

    <parameter name="jenkinsJobExecutionOrder" value="1"/>
    <parameter name="jenkinsJobExecutionMode" value="continue"/>

    <test name="Coca API Smoke Suite">
        <groups>
            <run>
                <include name="smoke"/>
            </run>
        </groups>
        <classes>
            <class name="com.lht.corecalculation.api.user.GetAllUsersApiTests"/>
            <class name="com.lht.corecalculation.api.user.PutUserDetailsApiTests"/>
            <class name="com.lht.corecalculation.api.filters.GetFiltersApiTests"/>
            <class name="com.lht.corecalculation.api.project.PatchProjectOwnerApiTests"/>
            <class name="com.lht.corecalculation.api.quotation.GetAllQuotationsApiTests"/>
            <class name="com.lht.corecalculation.api.quotation.GetQuotationByIdApiTests"/>
            <class name="com.lht.corecalculation.api.quotation.PutQuotationBeginProgressApiTests"/>
            <class name="com.lht.corecalculation.api.handlingcharges.GetAllHandlingChargesApiTests"/>
            <class name="com.lht.corecalculation.api.handlingcharges.PutHandlingChargesApiTests"/>
            <class name="com.lht.corecalculation.api.handlingcharges.PutIndividualPartsValueApiTests"/>
            <class name="com.lht.corecalculation.api.z2ratings.GetAllZ2RatingsApiTests"/>
            <class name="com.lht.corecalculation.api.z2ratings.PutGlobalZ2RatingApiTests"/>
            <class name="com.lht.corecalculation.api.z2ratings.PutIndividualZ2RatingApiTests"/>
            <class name="com.lht.corecalculation.api.labourrates.LabourRatesApiTests"/>
            <class name="com.lht.corecalculation.api.rfpengine.GetRfpEngineApiTests"/>
            <class name="com.lht.corecalculation.api.rfpengine.PutRfpApiTests"/>
            <class name="com.lht.corecalculation.api.rfpmodule.GetRfpModuleApiTests"/>
            <class name="com.lht.corecalculation.api.rfpmodule.PutRfpModuleApiTests"/>
            <class name="com.lht.corecalculation.api.subcontractpricing.GetSubcontractPricingApiTests"/>
            <class name="com.lht.corecalculation.api.subcontractpricing.PutSubcontractPricingApiTests"/>
            <class name="com.lht.corecalculation.api.pricingescalation.GetPricingEscalationApiTests"/>
            <class name="com.lht.corecalculation.api.pricingescalation.PutPricingEscalationApiTests"/>
            <class name="com.lht.corecalculation.api.wssummary.GetWorkscopeSummaryApiTests"/>
            <class name="com.lht.corecalculation.api.wssummary.GetWorkscopeSummaryEngineLeap1ATests"/>
            <class name="com.lht.corecalculation.api.wssummary.GetWorkscopeSummaryEngineLeap1BTests"/>
            <class name="com.lht.corecalculation.api.wssummary.GetWorkscopeSummaryEngineCfm565BTests"/>
            <class name="com.lht.corecalculation.api.wssummary.GetWorkscopeSummaryEngineCfm567BTests"/>
            <class name="com.lht.corecalculation.api.wssummary.GetWorkscopeSummaryEngineV2500Tests"/>
        </classes>
    </test>
</suite>