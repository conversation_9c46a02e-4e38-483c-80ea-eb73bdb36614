<!DOCTYPE suite SYSTEM "https://testng.org/testng-1.0.dtd">
<suite verbose="1" name="LHT CoCa API" parallel="methods">

    <parameter name="suiteOwner" value="nino.simeonov"/>
    <parameter name="jenkinsJobName" value="Workscope Fixed Price and Nte API Tests"/>
    <parameter name="jenkinsJobType" value="workscopeFixedPriceAndNte"/>
    <parameter name="jenkinsEnvironments" value="TEST"/>
    <parameter name="jenkinsEmail" value="<EMAIL>"/>

    <parameter name="jenkinsPipelineEnvironments" value="TEST"/>

    <parameter name="jenkinsJobExecutionOrder" value="1"/>
    <parameter name="jenkinsJobExecutionMode" value="continue"/>

    <test name="Coca API Workscope Fixed Price and NTE Suite">
        <groups>
            <run>
                <include name="workscopeFixedPriceAndNte"/>
            </run>
        </groups>
        <classes>
            <class name="com.lht.corecalculation.api.quotation.PutQuotationBeginNteAndWfpTests"/>
            <class name="com.lht.corecalculation.api.scrapcaps.GetAllScrapCapsFixPriceAndNteApiTests"/>
            <class name="com.lht.corecalculation.api.scrapcaps.PutScrapCapsFixPriceAndNteApiTests"/>
            <class name="com.lht.corecalculation.api.handlingcharges.GetAllHandlingChargesApiTests"/>
            <class name="com.lht.corecalculation.api.handlingcharges.PutHandlingChargesApiTests"/>
            <class name="com.lht.corecalculation.api.handlingcharges.PutIndividualPartsValueApiTests"/>
            <class name="com.lht.corecalculation.api.z2ratings.GetAllZ2RatingsApiTests"/>
            <class name="com.lht.corecalculation.api.z2ratings.PutGlobalZ2RatingApiTests"/>
            <class name="com.lht.corecalculation.api.z2ratings.PutIndividualZ2RatingApiTests"/>
            <class name="com.lht.corecalculation.api.pmaratings.GetPmaRatingsApiTests"/>
            <class name="com.lht.corecalculation.api.commitmentletter.GetCommitmentLetterApiTests"/>
            <class name="com.lht.corecalculation.api.commitmentletter.PutCommitmentLetterApiTests"/>
            <class name="com.lht.corecalculation.api.labourrates.LabourRatesApiTests"/>
            <class name="com.lht.corecalculation.api.rfpengine.GetRfpEngineApiTests"/>
            <class name="com.lht.corecalculation.api.rfpengine.PutRfpApiTests"/>
            <class name="com.lht.corecalculation.api.rfpmodule.GetRfpModuleApiTests"/>
            <class name="com.lht.corecalculation.api.rfpmodule.PutRfpModuleApiTests"/>
            <class name="com.lht.corecalculation.api.subcontractpricing.GetSubcontractPricingApiTests"/>
            <class name="com.lht.corecalculation.api.subcontractpricing.PutSubcontractPricingApiTests"/>
            <class name="com.lht.corecalculation.api.repairexclusions.GetAllRepairExclusionsTests"/>
            <class name="com.lht.corecalculation.api.repairexclusions.PutRepairExclusionsTests"/>
            <class name="com.lht.corecalculation.api.pricingescalation.GetPricingEscalationApiTests"/>
            <class name="com.lht.corecalculation.api.pricingescalation.PutPricingEscalationWsFixedAndNteApiTests"/>
            <class name="com.lht.corecalculation.api.wssummarynteandfixprice.GetWorkscopeSummaryWsFixedAndNteApiTests"/>
            <class name="com.lht.corecalculation.api.wssummarynteandfixprice.GetWorkscopeSummaryWsFixedAndNteLeap1aApiTests"/>
            <class name="com.lht.corecalculation.api.wssummarynteandfixprice.GetWorkscopeSummaryNteCfm565bPmaApiTests"/>
            <class name="com.lht.corecalculation.api.wssummarynteandfixprice.GetWorkscopeSummaryNteLeap1bApiTests"/>
            <class name="com.lht.corecalculation.api.wssummarynteandfixprice.GetWorkscopeSummaryNteCfm567bApiTests"/>
            <class name="com.lht.corecalculation.api.wssummarynteandfixprice.GetWorkscopeSummaryWsFixedPriceV2500ApiTests"/>
            <class name="com.lht.corecalculation.api.copyquotation.CopyQuotationApiTests"/>
        </classes>
    </test>
</suite>