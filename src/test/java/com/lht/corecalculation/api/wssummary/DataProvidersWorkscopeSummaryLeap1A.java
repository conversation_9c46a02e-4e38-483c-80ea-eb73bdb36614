package com.lht.corecalculation.api.wssummary;

import com.lht.corecalculation.base.BaseCocaApiTest;
import java.util.Arrays;
import org.testng.annotations.DataProvider;


import static com.lht.corecalculation.api.constants.GlobalConstants.CPR;
import static com.lht.corecalculation.api.constants.GlobalConstants.HPC;
import static com.lht.corecalculation.api.constants.GlobalConstants.HPT_LLP;
import static com.lht.corecalculation.api.constants.GlobalConstants.HPT_S1B;
import static com.lht.corecalculation.api.constants.GlobalConstants.NSV;

public class DataProvidersWorkscopeSummaryLeap1A extends BaseCocaApiTest {

    @DataProvider(name = "summaryProductionCostLEAP_1A")
    public Object[][] workscopeSummaryProductionCostDataProviderLEAP_1A() {
        return new Object[][] {
                {CPR, Arrays.asList(8004343.0, 8454563.0, 8946349.0, 9250083.0, 9509838.0, 9724336.0)},
                {HPC, Arrays.asList(241751.0, 225872.0, 230754.0, 232770.0, 232961.0, 242138.0)},
                {HPT_S1B, Arrays.asList(1232970.0, 1285764.0, 1364109.0, 1445194.0, 1530181.0, 1628006.0)},
                {HPT_LLP, Arrays.asList(1246790.0, 1298566.0, 1377145.0, 1458287.0, 1543216.0, 1641523.0)},
                {NSV, Arrays.asList(231713.0, 216573.0, 221283.0, 223258.0, 223493.0, 232320.0)}
        };
    }

    @DataProvider(name = "summaryDiscountsLEAP_1A")
    public Object[][] workscopeSummaryDiscountsDataProviderLEAP_1A() {
        return new Object[][] {
                {CPR, Arrays.asList(1059749.0, 1551613.0, 1810085.0, 1444225.0, 1627346.0, 1805005.0)},
                {HPC, Arrays.asList(2034.0, 2464.0, 2874.0, 2756.0, 3051.0, 3539.0)},
                {HPT_S1B, Arrays.asList(147394.0, 223141.0, 267840.0, 210160.0, 251236.0, 297061.0)},
                {HPT_LLP, Arrays.asList(147394.0, 223141.0, 267840.0, 210160.0, 251236.0, 297061.0)},
                {NSV, Arrays.asList(2034.0, 2464.0, 2874.0, 2756.0, 3051.0, 3539.0)}
        };
    }

    @DataProvider(name = "summaryRevenueLEAP_1A")
    public Object[][] workscopeSummaryRevenueDataProviderLEAP_1A() {
        return new Object[][] {
                {CPR, Arrays.asList(8299312.0, 8805020.0, 9306194.0, 9629175.0, 9913565.0, 1.0153506E7)},
                {HPC, Arrays.asList(228656.0, 229146.0, 229689.0, 230266.0, 230878.0, 231528.0)},
                {HPT_S1B, Arrays.asList(1253670.0, 1319641.0, 1394744.0, 1474897.0, 1560439.0, 1651732.0)},
                {HPT_LLP, Arrays.asList(1253670.0, 1319641.0, 1394744.0, 1474897.0, 1560439.0, 1651732.0)},
                {NSV, Arrays.asList(250823.0, 251161.0, 251544.0, 251952.0, 252388.0, 252852.0)}
        };
    }

    @DataProvider(name = "summarySurchargesCostLEAP_1A")
    public Object[][] workscopeSummarySurchargesCostDataProviderLEAP_1A() {
        return new Object[][] {
                {CPR, Arrays.asList(334979.0, 405292.0, 385129.0, 383494.0, 392589.0, 403586.0)},
                {HPC, Arrays.asList(94568.0, 112755.0, 107998.0, 117148.0, 119165.0, 122775.0)},
                {HPT_S1B, Arrays.asList(163857.0, 186716.0, 187043.0, 201652.0, 209517.0, 219275.0)},
                {HPT_LLP, Arrays.asList(164548.0, 187356.0, 187695.0, 202307.0, 210168.0, 219950.0)},
                {NSV, Arrays.asList(94066.0, 112290.0, 107524.0, 116672.0, 118692.0, 122284.0)}
        };
    }

    @DataProvider(name = "summaryProdCostInclDiscountsAndSurchargesLEAP_1A")
    public Object[][] workscopeSummaryProdCostInclDiscountsAndSurchargesDataProviderLEAP_1A() {
        return new Object[][] {
                {CPR, Arrays.asList(7279572.0, 7308242.0, 7521393.0, 8189351.0, 8275082.0, 8322917.0)},
                {HPC, Arrays.asList(334285.0, 336164.0, 335878.0, 347161.0, 349074.0, 361373.0)},
                {HPT_S1B, Arrays.asList(1249432.0, 1249339.0, 1283310.0, 1436686.0, 1488462.0, 1550219.0)},
                {HPT_LLP, Arrays.asList(1263943.0, 1262781.0, 1296999.0, 1450434.0, 1502148.0, 1564412.0)},
                {NSV, Arrays.asList(323745.0, 326400.0, 325934.0, 337174.0, 339133.0, 351065.0)}
        };
    }

    @DataProvider(name = "summaryDb2ValuesLEAP_1A")
    public Object[][] workscopeSummaryDb2ValuesLEAP_1A() {
        return new Object[][] {
                {CPR, Arrays.asList(1019740.0, 1496778.0, 1784800.0, 1439824.0, 1638483.0, 1830588.0)},
                {HPC, Arrays.asList(-105629.0, -107018.0, -106189.0, -116895.0, -118196.0, -129844.0)},
                {HPT_S1B, Arrays.asList(4237.0, 70302.0, 111433.0, 38211.0, 71977.0, 101513.0)},
                {HPT_LLP, Arrays.asList(-10273.0, 56860.0, 97745.0, 24463.0, 58290.0, 87319.0)},
                {NSV, Arrays.asList(-72922.0, -75239.0, -74390.0, -85222.0, -86745.0, -98212.0)}
        };
    }

    @DataProvider(name = "summaryDb2PercentageValuesLEAP_1A")
    public Object[][] workscopeSummaryDb2PercentageValuesLEAP_1A() {
        return new Object[][] {
                {CPR, Arrays.asList(12.287047, 16.999144, 19.178630, 14.952723, 16.527687, 18.029130)},
                {HPC, Arrays.asList(-46.195583, -46.70299, -46.231606, -50.765305, -51.194157, -56.081543)},
                {HPT_S1B, Arrays.asList(0.33801615, 5.327393, 7.989555, 2.5907762, 4.6126494, 6.145858)},
                {HPT_LLP, Arrays.asList(-0.8194736, 4.308784, 7.0081186, 1.6586319, 3.7355406, 5.286562)},
                {NSV, Arrays.asList(-29.073013, -29.956688, -29.573408, -33.824753, -34.369904, -38.841732)}
        };
    }

    @DataProvider(name = "summaryEbitValuesLEAP_1A")
    public Object[][] workscopeSummaryEbitValuesLEAP_1A() {
        return new Object[][] {
                {CPR, Arrays.asList(526968.0, 1004005.0, 1215374.0, 979903.0, 1211413.0, 1419945.0)},
                {HPC, Arrays.asList(-204183.0, -205572.0, -220074.0, -208879.0, -203610.0, -211973.0)},
                {HPT_S1B, Arrays.asList(-94316.0, -28251.0, -2451.0, -53772.0, -13436.0, 19384.0)},
                {HPT_LLP, Arrays.asList(-108828.0, -41693.0, -16139.0, -67521.0, -27123.0, 5191.0)},
                {NSV, Arrays.asList(-171476.0, -173794.0, -188275.0, -177206.0, -172159.0, -180341.0)}
        };
    }

    @DataProvider(name = "summaryEbitPercentageValuesLEAP_1A")
    public Object[][] workscopeSummaryEbitPercentageValuesLEAP_1A() {
        return new Object[][] {
                {CPR, Arrays.asList(6.349538, 11.402648, 13.059846, 10.176395, 12.219757, 13.984777)},
                {HPC, Arrays.asList(-89.297134, -89.712395, -95.81383, -90.71219, -88.189316, -91.55391)},
                {HPT_S1B, Arrays.asList(-7.5232615, -2.1408825, -0.17575185, -3.6458743, -0.861058, 1.1735787)},
                {HPT_LLP, Arrays.asList(-8.680752, -3.1594915, -1.1571884, -4.5780187, -1.7381666, 0.3142827)},
                {NSV, Arrays.asList(-68.36533, -69.19624, -74.84787, -70.333305, -68.21218, -71.32258)}
        };
    }

    @DataProvider(name = "summaryEatPercentageValuesLEAP_1A")
    public Object[][] workscopeSummaryEatPercentageValuesLEAP_1A() {
        return new Object[][] {
                {CPR, Arrays.asList(4.762154, 8.551986, 9.794885, 7.632296, 9.164818, 10.488583)},
                {HPC, Arrays.asList(-66.972855, -67.284294, -71.860374, -68.03414, -66.14198, -68.665436)},
                {HPT_S1B, Arrays.asList(-5.642446, -1.6056617, -0.13181388, -2.7344058, -0.6457935, 0.88018405)},
                {HPT_LLP, Arrays.asList(-6.5105634, -2.3696187, -0.8678913, -3.433514, -1.303625, 0.235712)},
                {NSV, Arrays.asList(-51.274002, -51.897182, -56.135902, -52.749977, -51.159138, -53.491936)}
        };
    }

    @DataProvider(name = "summaryNetMarginePercentageLEAP_1A")
    public Object[][] workscopeSummaryNetMarginePercentageLEAP_1A() {
        return new Object[][] {
                {CPR, Arrays.asList(2.362153, 6.151986, 7.394885, 5.232297, 6.764818, 8.088583)},
                {HPC, Arrays.asList(-69.37285, -69.684296, -74.260376, -70.43414, -68.541985, -71.06543)},
                {HPT_S1B, Arrays.asList(-8.042446, -4.005662, -2.5318139, -5.1344056, -3.0457935, -1.519816)},
                {HPT_LLP, Arrays.asList(-8.910563, -4.7696185, -3.2678914, -5.833514, -3.703625, -2.164288)},
                {NSV, Arrays.asList(-53.674004, -54.29718, -58.5359, -55.14998, -53.559135, -55.891933)}
        };
    }
}