package com.lht.corecalculation.api.wssummary;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Utility class to extract actual values from API responses and output Java-formatted
 * DataProvider blocks to be pasted into test files across wssummarymodularntefixprice,
 * wssummary, and wssummarynteandfixprice packages.
 */
public class WorkscopeSummaryDataProviderUpdater {

    private static final ObjectMapper mapper = new ObjectMapper();

    public static void main(String[] args) throws IOException {
        String jsonPath = "example-responce.txt"; // Can be dynamically retrieved from test var
        JsonNode root = mapper.readTree(new File(jsonPath));

        JsonNode yearsNode = root.at("/summary/years");
        List<Integer> years = new ArrayList<>();
        for (JsonNode year : yearsNode) {
            years.add(year.asInt());
        }

        Map<String, String> metricPaths = new LinkedHashMap<>();
        metricPaths.put("ProductionCost", "/summary/productionCost");
        metricPaths.put("NetMargin", "/summary/netMargin");
        metricPaths.put("DB2", "/summary/db2");
        metricPaths.put("Discounts", "/summary/discounts");
        metricPaths.put("Surcharges", "/summary/surcharges");
        metricPaths.put("EATPercentage", "/summary/eatPercentage");
        metricPaths.put("EBIT", "/summary/ebit");
        metricPaths.put("Revenue", "/summary/revenue");

        for (Map.Entry<String, String> entry : metricPaths.entrySet()) {
            String metricName = entry.getKey();
            JsonNode metricNode = root.at(entry.getValue());
            if (metricNode.isMissingNode() || metricNode.isNull()) {
                System.err.println("Warning: Missing data for metric " + metricName);
                continue;
            }
            Map<String, List<Double>> dataByComponent = extractData(metricNode);
            printJavaDataProvider("summary" + metricName + "LEAP_1A", dataByComponent, years);
        }
    }

    private static Map<String, List<Double>> extractData(JsonNode parentNode) {
        Map<String, List<Double>> data = new LinkedHashMap<>();
        for (Iterator<String> it = parentNode.fieldNames(); it.hasNext(); ) {
            String component = it.next();
            JsonNode values = parentNode.get(component);
            List<Double> yearValues = new ArrayList<>();
            for (JsonNode v : values) {
                yearValues.add(v.asDouble());
            }
            data.put(component, yearValues);
        }
        return data;
    }

    private static void printJavaDataProvider(String name, Map<String, List<Double>> data, List<Integer> years) {
        System.out.println("@DataProvider(name = \"" + name + "\")");
        System.out.println("public Object[][] workscope" + name + "() {");
        System.out.println("    return new Object[][] {");

        for (Map.Entry<String, List<Double>> entry : data.entrySet()) {
            String component = entry.getKey();
            List<Double> values = entry.getValue();
            String valueList = values.stream().map(d -> String.format("%.1f", d)).collect(Collectors.joining(", "));
            System.out.println("        {" + component + ", Arrays.asList(" + valueList + ")},");
        }

        System.out.println("    };\n}");
    }
}