package com.lht.corecalculation.api.wssummary;

import com.lht.corecalculation.api.pojo.dto.wssummary.WorkscopeSummaryItemDto;
import com.lht.corecalculation.api.utils.WorkscopeUtil;
import io.restassured.response.Response;
import java.io.IOException;
import java.util.List;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;


import static com.lht.corecalculation.api.constants.GlobalConstants.ADMIN_USER_PASSWORD;
import static com.lht.corecalculation.api.constants.GlobalConstants.ADMIN_USER_USERNAME;
import static com.lht.corecalculation.api.constants.GlobalConstants.ATTRIBUTE_DO_NOT_MATCH_FOR_WORKSCOPE;
import static com.lht.corecalculation.api.constants.GlobalConstants.BIG_TOLERANCE;
import static com.lht.corecalculation.api.constants.GlobalConstants.DB2;
import static com.lht.corecalculation.api.constants.GlobalConstants.DB2_PERCENTAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.DISCOUNT;
import static com.lht.corecalculation.api.constants.GlobalConstants.EAT_PERCENTAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.EBIT;
import static com.lht.corecalculation.api.constants.GlobalConstants.EBIT_PERCENTAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_V2500_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.NET_MARGIN;
import static com.lht.corecalculation.api.constants.GlobalConstants.PRODUCTION_COST;
import static com.lht.corecalculation.api.constants.GlobalConstants.PRODUCTION_COST_AFTER_DISCOUNTS_AND_SURCHARGES;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_200;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.REVENUE;
import static com.lht.corecalculation.api.constants.GlobalConstants.SURCHARGES_COST;
import static com.lht.corecalculation.api.constants.GlobalConstants.TOLERANCE;
import static com.lht.corecalculation.api.utils.WorkscopeUtil.assertListEqualsWithTolerance;
import static com.lht.corecalculation.api.utils.WorkscopeUtil.updateEngineValuesForV2500;
import static org.testng.Assert.assertEquals;

public class GetWorkscopeSummaryEngineV2500Tests extends DataProvidersWorkscopeSummaryV2500 {

    private static Response sharedWorkscopeSummaryResponse;

    @BeforeClass(alwaysRun = true)
    public void fetchWorkscopeSummary() throws IOException, InterruptedException, WorkscopeUtil.ApiException {
        String adminToken = utils.sso.getAccessTokenFor(ADMIN_USER_USERNAME, ADMIN_USER_PASSWORD);
        updateEngineValuesForV2500(adminToken);
        sharedWorkscopeSummaryResponse = WorkscopeUtil.initializeWorkscopeAndAwaitCompletionThenFetchSummary(ENGINE_V2500_QUOTATION_ID, adminToken);
        assertEquals(sharedWorkscopeSummaryResponse.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = "smoke", dataProvider = "summaryProductionCostV2500")
    public void verifyWorkscopeSummary_ProductionCost_V2500(String workscope, List<Double> expectedProductionCost) throws IOException {
        String errorMessage = String.format(ATTRIBUTE_DO_NOT_MATCH_FOR_WORKSCOPE, PRODUCTION_COST, workscope);
        List<Double> actualProductionCost = WorkscopeUtil.extractValueFromResponse(sharedWorkscopeSummaryResponse, WorkscopeSummaryItemDto::getProductionCost, workscope);
        assertListEqualsWithTolerance(actualProductionCost, expectedProductionCost, BIG_TOLERANCE, errorMessage);
    }

    @Test(groups = "smoke", dataProvider = "summaryDiscountsV2500")
    public void verifyWorkscopeSummary_Discounts_V2500(String workscope, List<Double> expectedDiscount) throws IOException {
        String errorMessage = String.format(ATTRIBUTE_DO_NOT_MATCH_FOR_WORKSCOPE, DISCOUNT, workscope);
        List<Double> actualDiscount = WorkscopeUtil.extractValueFromResponse(sharedWorkscopeSummaryResponse, WorkscopeSummaryItemDto::getDiscount, workscope);
        assertEquals(actualDiscount, expectedDiscount, errorMessage);
    }

    @Test(groups = "smoke", dataProvider = "summaryRevenueV2500")
    public void verifyWorkscopeSummary_Revenue_V2500(String workscope, List<Double> expectedRevenue) throws IOException {
        String errorMessage = String.format(ATTRIBUTE_DO_NOT_MATCH_FOR_WORKSCOPE, REVENUE, workscope);
        List<Double> actualRevenue = WorkscopeUtil.extractValueFromResponse(sharedWorkscopeSummaryResponse, WorkscopeSummaryItemDto::getRevenue, workscope);
        assertListEqualsWithTolerance(actualRevenue, expectedRevenue, BIG_TOLERANCE, errorMessage);
    }

    @Test(groups = "smoke", dataProvider = "summarySurchargesCostV2500")
    public void verifyWorkscopeSummary_SurchargesCost_V2500(String workscope, List<Double> expectedSurchargesCost) throws IOException {
        String errorMessage = String.format(ATTRIBUTE_DO_NOT_MATCH_FOR_WORKSCOPE, SURCHARGES_COST, workscope);
        List<Double> actualSurchargesCost = WorkscopeUtil.extractValueFromResponse(sharedWorkscopeSummaryResponse, WorkscopeSummaryItemDto::getSurchargesCost, workscope);
        assertEquals(actualSurchargesCost, expectedSurchargesCost, errorMessage);
    }

    @Test(dataProvider = "summaryProdCostInclDiscountsAndSurchargesV2500")
    public void verifyWorkscopeSummary_ProdCostInclDiscountsAndSurcharges_V2500(
            String workscope,
            List<Double> expectedProdCostInclDiscountsAndSurcharges
    ) throws IOException {
        String errorMessage = String.format(ATTRIBUTE_DO_NOT_MATCH_FOR_WORKSCOPE, PRODUCTION_COST_AFTER_DISCOUNTS_AND_SURCHARGES, workscope);
        List<Double> actualProdCostInclDiscountsAndSurcharges = WorkscopeUtil.extractValueFromResponse(sharedWorkscopeSummaryResponse, WorkscopeSummaryItemDto::getProductionCostAfterDiscountAndSurcharges, workscope);
        assertListEqualsWithTolerance(actualProdCostInclDiscountsAndSurcharges, expectedProdCostInclDiscountsAndSurcharges, BIG_TOLERANCE, errorMessage);
    }

    @Test(dataProvider = "summaryDb2ValuesV2500")
    public void verifyWorkscopeSummary_db2_V2500(String workscope, List<Double> expectedDb2) throws IOException {
        String errorMessage = String.format(ATTRIBUTE_DO_NOT_MATCH_FOR_WORKSCOPE, DB2, workscope);
        List<Double> actualDb2 = WorkscopeUtil.extractValueFromResponse(sharedWorkscopeSummaryResponse, WorkscopeSummaryItemDto::getDb2, workscope);
        assertListEqualsWithTolerance(actualDb2, expectedDb2, BIG_TOLERANCE, errorMessage);
    }

    @Test(dataProvider = "summaryDb2PercentageValuesV2500")
    public void verifyWorkscopeSummary_db2Percentage_V2500(String workscope, List<Double> expectedDb2Percentage) throws IOException {
        String errorMessage = String.format(ATTRIBUTE_DO_NOT_MATCH_FOR_WORKSCOPE, DB2_PERCENTAGE, workscope);
        List<Double> actualDb2Percentage = WorkscopeUtil.extractValueFromResponse(sharedWorkscopeSummaryResponse, WorkscopeSummaryItemDto::getDb2Percentage, workscope);
        assertListEqualsWithTolerance(actualDb2Percentage, expectedDb2Percentage, TOLERANCE, errorMessage);
    }

    @Test(dataProvider = "summaryEbitValuesV2500")
    public void verifyWorkscopeSummary_ebit_V2500(String workscope, List<Double> expectedEbit) throws IOException {
        String errorMessage = String.format(ATTRIBUTE_DO_NOT_MATCH_FOR_WORKSCOPE, EBIT, workscope);
        List<Double> actualEbit = WorkscopeUtil.extractValueFromResponse(sharedWorkscopeSummaryResponse, WorkscopeSummaryItemDto::getEbit, workscope);
        assertListEqualsWithTolerance(actualEbit, expectedEbit, BIG_TOLERANCE, errorMessage);
    }

    @Test(dataProvider = "summaryEbitPercentageValuesV2500")
    public void verifyWorkscopeSummary_ebitPercentage_V2500(String workscope, List<Double> expectedEbitPercentage) throws IOException {
        String errorMessage = String.format(ATTRIBUTE_DO_NOT_MATCH_FOR_WORKSCOPE, EBIT_PERCENTAGE, workscope);
        List<Double> actualEbitPercentage = WorkscopeUtil.extractValueFromResponse(sharedWorkscopeSummaryResponse, WorkscopeSummaryItemDto::getEbitPercentage, workscope);
        assertListEqualsWithTolerance(actualEbitPercentage, expectedEbitPercentage, TOLERANCE, errorMessage);
    }

    @Test(dataProvider = "summaryEatPercentageValuesV2500")
    public void verifyWorkscopeSummary_eatPercentage_V2500(String workscope, List<Double> expectedEatPercentage) throws IOException {
        String errorMessage = String.format(ATTRIBUTE_DO_NOT_MATCH_FOR_WORKSCOPE, EAT_PERCENTAGE, workscope);
        List<Double> actualEatPercentage = WorkscopeUtil.extractValueFromResponse(sharedWorkscopeSummaryResponse, WorkscopeSummaryItemDto::getEatPercentage, workscope);
        assertListEqualsWithTolerance(actualEatPercentage, expectedEatPercentage, TOLERANCE, errorMessage);
    }

    @Test(dataProvider = "summaryNetMarginePercentageV2500")
    public void verifyWorkscopeSummary_netMargine_V2500(String workscope, List<Double> expectedNetMargin) throws IOException {
        String errorMessage = String.format(ATTRIBUTE_DO_NOT_MATCH_FOR_WORKSCOPE, NET_MARGIN, workscope);
        List<Double> actualNetMargin = WorkscopeUtil.extractValueFromResponse(sharedWorkscopeSummaryResponse, WorkscopeSummaryItemDto::getNetMargin, workscope);
        assertListEqualsWithTolerance(actualNetMargin, expectedNetMargin, TOLERANCE, errorMessage);
    }
}