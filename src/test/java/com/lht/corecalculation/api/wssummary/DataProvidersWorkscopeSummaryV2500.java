package com.lht.corecalculation.api.wssummary;

import com.lht.corecalculation.base.BaseCocaApiTest;
import java.util.Arrays;
import java.util.List;
import org.testng.annotations.DataProvider;


import static com.lht.corecalculation.api.constants.GlobalConstants.C_N_A;
import static com.lht.corecalculation.api.constants.GlobalConstants.C_N_A_K;
import static com.lht.corecalculation.api.constants.GlobalConstants.C_N_B;
import static com.lht.corecalculation.api.constants.GlobalConstants.C_N_B_K;

public class DataProvidersWorkscopeSummaryV2500 extends BaseCocaApiTest {

    @DataProvider(name = "summaryProductionCostV2500")
    public Object[][] workscopeSummaryProductionCostDataProviderV2500() {
        return new Object[][] {
                {C_N_B, Arrays.asList(3945611.0, 4211564.0, 4456909.0, 4707887.0, 4968334.0, 5276555.0)},
                {C_N_A, Arrays.asList(3722216.0, 3979933.0, 4209860.0, 4445377.0, 4689602.0, 4976188.0)},
                {C_N_B_K, Arrays.asList(2592780.0, 2771651.0, 2933418.0, 3098944.0, 3270994.0, 3474211.0)},
                {C_N_A_K, Arrays.asList(2945980.0, 3155349.0, 3338145.0, 3525664.0, 3720799.0, 3948869.0)}
        };
    }

    @DataProvider(name = "summaryDiscountsV2500")
    public Object[][] workscopeSummaryDiscountsDataProviderV2500() {
        return new Object[][] {
                {C_N_B, List.of(16565.0, 18387.0, 19491.0, 20660.0, 21900.0, 23214.0)},
                {C_N_A, List.of(15349.0, 17038.0, 18060.0, 19144.0, 20292.0, 21510.0)},
                {C_N_B_K, List.of(15345.0, 17033.0, 18055.0, 19138.0, 20287.0, 21504.0)},
                {C_N_A_K, List.of(13315.0, 14780.0, 15667.0, 16607.0, 17603.0, 18659.0)}
        };
    }

    @DataProvider(name = "summaryRevenueV2500")
    public Object[][] workscopeSummaryRevenueDataProviderV2500() {
        return new Object[][] {
                {C_N_B, Arrays.asList(5455486.0, 5916414.0, 6277174.0, 6660336.0, 7067111.0, 7500982.0)},
                {C_N_A, Arrays.asList(4895736.0, 5309939.0, 5629972.0, 5969572.0, 6329615.0, 6713222.0)},
                {C_N_B_K, Arrays.asList(3395473.0, 3685047.0, 3909083.0, 4146918.0, 4399498.0, 4669184.0)},
                {C_N_A_K, Arrays.asList(3747794.0, 4067989.0, 4312697.0, 4572300.0, 4847800.0, 5141670.0)}
        };
    }

    @DataProvider(name = "summarySurchargesCostV2500")
    public Object[][] workscopeSummarySurchargesCostV2500() {
        return new Object[][] {
                {C_N_B, List.of(294312.0, 364168.0, 341700.0, 330311.0, 340647.0, 353087.0)},
                {C_N_A, List.of(282414.0, 351136.0, 327795.0, 315477.0, 324816.0, 336181.0)},
                {C_N_B_K, List.of(276139.0, 344262.0, 320461.0, 307653.0, 316482.0, 327306.0)},
                {C_N_A_K, List.of(273227.0, 341070.0, 317055.0, 304016.0, 312600.0, 323160.0)}
        };
    }

    @DataProvider(name = "summaryProdCostInclDiscountsAndSurchargesV2500")
    public Object[][] workscopeSummaryProdCostInclDiscountsAndSurchargesDataProviderV2500() {
        return new Object[][] {
                {C_N_A, Arrays.asList(3989281.0, 4314030.0, 4519595.0, 4741710.0, 4994125.0, 5290858.0)},
                {C_N_A_K, Arrays.asList(3205892.0, 3481640.0, 3639533.0, 3813073.0, 4015797.0, 4253369.0)},
                {C_N_B, Arrays.asList(4223358.0, 4557345.0, 4779118.0, 5017539.0, 5287081.0, 5606429.0)},
                {C_N_B_K, Arrays.asList(2853574.0, 3098880.0, 3235824.0, 3387458.0, 3567190.0, 3780012.0)}
        };
    }

    @DataProvider(name = "summaryDb2ValuesV2500")
    public Object[][] workscopeSummaryDb2ValuesV2500() {
        return new Object[][] {
                {C_N_A, Arrays.asList(906455.0, 995908.0, 1110377.0, 1227862.0, 1335490.0, 1422364.0)},
                {C_N_A_K, Arrays.asList(541902.0, 586349.0, 673163.0, 759226.0, 832003.0, 888300.0)},
                {C_N_B, Arrays.asList(1232127.0, 1359069.0, 1498055.0, 1642796.0, 1780029.0, 1894552.0)},
                {C_N_B_K, Arrays.asList(541899.0, 586166.0, 673258.0, 759460.0, 832308.0, 889171.0)}
        };
    }

    @DataProvider(name = "summaryDb2PercentageValuesV2500")
    public Object[][] workscopeSummaryDb2PercentageValuesV2500() {
        return new Object[][] {
                {C_N_A, Arrays.asList(18.515192, 18.755556, 19.722600, 20.568678, 21.099080, 21.187498)},
                {C_N_A_K, Arrays.asList(14.459238, 14.413742, 15.608876, 16.604912, 17.162497, 17.276491)},
                {C_N_B, Arrays.asList(22.585115, 22.971160, 23.865130, 24.665375, 25.187517, 25.257397)},
                {C_N_B_K, Arrays.asList(15.959453, 15.906630, 17.222929, 18.313845, 18.918250, 19.043402)}
        };
    }

    @DataProvider(name = "summaryEbitValuesV2500")
    public Object[][] workscopeSummaryEbitValuesV2500() {
        return new Object[][] {
                {C_N_A, Arrays.asList(413682.0, 503136.0, 540951.0, 767941.0, 908421.0, 1011720.0)},
                {C_N_A_K, Arrays.asList(49130.0, 93577.0, 103737.0, 299305.0, 404934.0, 477656.0)},
                {C_N_B, Arrays.asList(739355.0, 866296.0, 928629.0, 1182875.0, 1352960.0, 1483909.0)},
                {C_N_B_K, Arrays.asList(49126.0, 93394.0, 103832.0, 299539.0, 405238.0, 478527.0)}
        };
    }

    @DataProvider(name = "summaryEbitPercentageValuesV2500")
    public Object[][] workscopeSummaryEbitPercentageValuesV2500() {
        return new Object[][] {
                {C_N_A, Arrays.asList(8.449853, 9.475366, 9.608413, 12.864256, 14.351917, 15.070561)},
                {C_N_A_K, Arrays.asList(1.310905, 2.300326, 2.405399, 6.546058, 8.352944, 9.289908)},
                {C_N_B, Arrays.asList(13.552511, 14.642256, 14.793754, 17.760002, 19.144460, 19.782864)},
                {C_N_B_K, Arrays.asList(1.446823, 2.534411, 2.656190, 7.2223177, 9.211019, 10.248637)}
        };
    }

    @DataProvider(name = "summaryEatPercentageValuesV2500")
    public Object[][] workscopeSummaryEatPercentageValuesV2500() {
        return new Object[][] {
                {C_N_A, Arrays.asList(6.337390, 7.106524, 7.206310, 9.648192, 10.763938, 11.302921)},
                {C_N_A_K, Arrays.asList(0.983179, 1.725244, 1.804049, 4.909544, 6.264708, 6.967432)},
                {C_N_B, Arrays.asList(10.164383, 10.981692, 11.095316, 13.320002, 14.358346, 14.837148)},
                {C_N_B_K, Arrays.asList(1.085118, 1.900809, 1.992143, 5.417383, 6.908264, 7.686478)}
        };
    }

    @DataProvider(name = "summaryNetMarginePercentageV2500")
    public Object[][] workscopeSummaryNetMarginePercentageV2500() {
        return new Object[][] {
                {C_N_A, Arrays.asList(3.937390, 4.706524, 4.806310, 7.248192, 8.363938, 8.902921)},
                {C_N_A_K, Arrays.asList(-1.416821, -0.674756, -0.595951, 2.509544, 3.864708, 4.567431)},
                {C_N_B, Arrays.asList(7.764383, 8.581692, 8.695316, 10.920002, 11.958346, 12.437148)},
                {C_N_B_K, Arrays.asList(-1.314883, -0.499191, -0.407857, 3.017382, 4.508264, 5.286478)}
        };
    }
}
