package com.lht.corecalculation.api.wssummary;

import com.lht.corecalculation.api.pojo.dto.wssummary.WorkscopeSummaryItemDto;
import com.lht.corecalculation.api.utils.WorkscopeUtil;
import com.lht.corecalculation.base.BaseCocaApiTest;
import io.restassured.response.Response;
import java.io.IOException;
import java.util.List;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;


import static com.lht.corecalculation.api.constants.GlobalConstants.ADMIN_USER_PASSWORD;
import static com.lht.corecalculation.api.constants.GlobalConstants.ADMIN_USER_USERNAME;
import static com.lht.corecalculation.api.constants.GlobalConstants.ATTRIBUTE_DO_NOT_MATCH_FOR_WORKSCOPE;
import static com.lht.corecalculation.api.constants.GlobalConstants.BIG_TOLERANCE;
import static com.lht.corecalculation.api.constants.GlobalConstants.DISCOUNT;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_CFM56_5B_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.PRODUCTION_COST;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_200;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.REVENUE;
import static com.lht.corecalculation.api.constants.GlobalConstants.SURCHARGES_COST;
import static com.lht.corecalculation.api.utils.WorkscopeUtil.assertListEqualsWithTolerance;
import static com.lht.corecalculation.api.utils.WorkscopeUtil.updateEngineValuesForCfm565b;
import static org.testng.Assert.assertEquals;

public class GetWorkscopeSummaryEngineCfm565BTests extends BaseCocaApiTest {

    private static Response sharedWorkscopeSummaryResponse;

    @BeforeClass(alwaysRun = true)
    public void fetchWorkscopeSummary() throws IOException, InterruptedException, WorkscopeUtil.ApiException {
        String adminToken = utils.sso.getAccessTokenFor(ADMIN_USER_USERNAME, ADMIN_USER_PASSWORD);
        updateEngineValuesForCfm565b(adminToken);
        sharedWorkscopeSummaryResponse = WorkscopeUtil.initializeWorkscopeAndAwaitCompletionThenFetchSummary(ENGINE_CFM56_5B_QUOTATION_ID, adminToken);
        assertEquals(sharedWorkscopeSummaryResponse.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
    }

    @Test(dataProvider = "combinedDataProviderCFM56_5B")
    public void verifyWorkscopeSummary_ProductionCost_CFM56_5B(
            String accessToken,
            String workscope,
            List<Double> expectedProductionCost
    ) throws IOException {
        String errorMessage = String.format(ATTRIBUTE_DO_NOT_MATCH_FOR_WORKSCOPE, PRODUCTION_COST, workscope);
        List<Double> actualProductionCost = WorkscopeUtil.extractValueFromResponse(sharedWorkscopeSummaryResponse, WorkscopeSummaryItemDto::getProductionCost, workscope);
        assertListEqualsWithTolerance(actualProductionCost, expectedProductionCost, BIG_TOLERANCE, errorMessage);
    }

    @Test(dataProvider = "summaryDiscountsCFM56_5B")
    public void verifyWorkscopeSummary_Discounts_CFM56_5B(String workscope, List<Double> expectedDiscount) throws IOException {
        String errorMessage = String.format(ATTRIBUTE_DO_NOT_MATCH_FOR_WORKSCOPE, DISCOUNT, workscope);
        List<Double> actualDiscount = WorkscopeUtil.extractValueFromResponse(sharedWorkscopeSummaryResponse, WorkscopeSummaryItemDto::getDiscount, workscope);
        assertEquals(actualDiscount, expectedDiscount, errorMessage);
    }

    @Test(groups = "smoke", dataProvider = "summaryRevenueCFM56_5B")
    public void verifyWorkscopeSummary_Revenue_CFM56_5B(String workscope, List<Double> expectedRevenue) throws IOException {
        String errorMessage = String.format(ATTRIBUTE_DO_NOT_MATCH_FOR_WORKSCOPE, REVENUE, workscope);
        List<Double> actualRevenue = WorkscopeUtil.extractValueFromResponse(sharedWorkscopeSummaryResponse, WorkscopeSummaryItemDto::getRevenue, workscope);
        assertListEqualsWithTolerance(actualRevenue, expectedRevenue, BIG_TOLERANCE, errorMessage);
    }

    @Test(dataProvider = "summarySurchargesCostCFM56_5B")
    public void verifyWorkscopeSummary_SurchargesCost_CFM56_5B(String workscope, List<Double> expectedSurchargesCost) throws IOException {
        String errorMessage = String.format(ATTRIBUTE_DO_NOT_MATCH_FOR_WORKSCOPE, SURCHARGES_COST, workscope);
        List<Double> actualSurchargesCost = WorkscopeUtil.extractValueFromResponse(sharedWorkscopeSummaryResponse, WorkscopeSummaryItemDto::getSurchargesCost, workscope);
        assertEquals(actualSurchargesCost, expectedSurchargesCost, errorMessage);
    }
}
