package com.lht.corecalculation.api.wssummary;

import com.lht.corecalculation.api.pojo.dto.wssummary.WorkscopeSummaryItemDto;
import com.lht.corecalculation.api.utils.WorkscopeUtil;
import io.restassured.response.Response;
import java.io.IOException;
import java.util.List;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;


import static com.lht.corecalculation.api.constants.GlobalConstants.ADMIN_USER_PASSWORD;
import static com.lht.corecalculation.api.constants.GlobalConstants.ADMIN_USER_USERNAME;
import static com.lht.corecalculation.api.constants.GlobalConstants.ATTRIBUTE_DO_NOT_MATCH_FOR_WORKSCOPE;
import static com.lht.corecalculation.api.constants.GlobalConstants.BIG_TOLERANCE;
import static com.lht.corecalculation.api.constants.GlobalConstants.DB2;
import static com.lht.corecalculation.api.constants.GlobalConstants.DB2_PERCENTAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.DISCOUNT;
import static com.lht.corecalculation.api.constants.GlobalConstants.EAT_PERCENTAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.EBIT;
import static com.lht.corecalculation.api.constants.GlobalConstants.EBIT_PERCENTAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_LEAP_1A_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.NET_MARGIN;
import static com.lht.corecalculation.api.constants.GlobalConstants.PRODUCTION_COST;
import static com.lht.corecalculation.api.constants.GlobalConstants.PRODUCTION_COST_AFTER_DISCOUNTS_AND_SURCHARGES;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_200;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.REVENUE;
import static com.lht.corecalculation.api.constants.GlobalConstants.SURCHARGES_COST;
import static com.lht.corecalculation.api.constants.GlobalConstants.TOLERANCE;
import static com.lht.corecalculation.api.utils.WorkscopeUtil.assertListEqualsWithTolerance;
import static com.lht.corecalculation.api.utils.WorkscopeUtil.updateEngineValuesForLeap1a;
import static org.testng.Assert.assertEquals;

public class GetWorkscopeSummaryEngineLeap1ATests extends DataProvidersWorkscopeSummaryLeap1A {

    private static Response sharedWorkscopeSummaryResponse;

    @BeforeClass(alwaysRun = true)
    public void fetchWorkscopeSummary() throws IOException, InterruptedException, WorkscopeUtil.ApiException {
        String adminToken = utils.sso.getAccessTokenFor(ADMIN_USER_USERNAME, ADMIN_USER_PASSWORD);
        updateEngineValuesForLeap1a(adminToken);
        sharedWorkscopeSummaryResponse = WorkscopeUtil.initializeWorkscopeAndAwaitCompletionThenFetchSummary(ENGINE_LEAP_1A_QUOTATION_ID, adminToken);
        assertEquals(sharedWorkscopeSummaryResponse.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
    }

    @Test(dataProvider = "summaryProductionCostLEAP_1A")
    public void verifyWorkscopeSummary_ProductionCost_LEAP_1A(String workscope, List<Double> expectedProductionCost) throws IOException {
        String errorMessage = String.format(ATTRIBUTE_DO_NOT_MATCH_FOR_WORKSCOPE, PRODUCTION_COST, workscope);
        List<Double> actualProductionCost = WorkscopeUtil.extractValueFromResponse(sharedWorkscopeSummaryResponse, WorkscopeSummaryItemDto::getProductionCost, workscope);
        assertListEqualsWithTolerance(actualProductionCost, expectedProductionCost, BIG_TOLERANCE, errorMessage);
    }

    @Test(groups = "smoke", dataProvider = "summaryDiscountsLEAP_1A")
    public void verifyWorkscopeSummary_Discounts_LEAP_1A(String workscope, List<Double> expectedDiscount) throws IOException {
        String errorMessage = String.format(ATTRIBUTE_DO_NOT_MATCH_FOR_WORKSCOPE, DISCOUNT, workscope);
        List<Double> actualDiscount = WorkscopeUtil.extractValueFromResponse(sharedWorkscopeSummaryResponse, WorkscopeSummaryItemDto::getDiscount, workscope);
        assertListEqualsWithTolerance(actualDiscount, expectedDiscount, BIG_TOLERANCE, errorMessage);
    }

    @Test(dataProvider = "summaryRevenueLEAP_1A")
    public void verifyWorkscopeSummary_Revenue_LEAP_1A(String workscope, List<Double> expectedRevenue) throws IOException {
        String errorMessage = String.format(ATTRIBUTE_DO_NOT_MATCH_FOR_WORKSCOPE, REVENUE, workscope);
        List<Double> actualRevenue = WorkscopeUtil.extractValueFromResponse(sharedWorkscopeSummaryResponse, WorkscopeSummaryItemDto::getRevenue, workscope);
        assertListEqualsWithTolerance(actualRevenue, expectedRevenue, BIG_TOLERANCE, errorMessage);
    }

    @Test(dataProvider = "summarySurchargesCostLEAP_1A")
    public void verifyWorkscopeSummary_SurchargesCost_LEAP_1A(String workscope, List<Double> expectedSurchargesCost) throws IOException {
        String errorMessage = String.format(ATTRIBUTE_DO_NOT_MATCH_FOR_WORKSCOPE, SURCHARGES_COST, workscope);
        List<Double> actualSurchargesCost = WorkscopeUtil.extractValueFromResponse(sharedWorkscopeSummaryResponse, WorkscopeSummaryItemDto::getSurchargesCost, workscope);
        assertEquals(actualSurchargesCost, expectedSurchargesCost, errorMessage);
    }

    @Test(dataProvider = "summaryProdCostInclDiscountsAndSurchargesLEAP_1A")
    public void verifyWorkscopeSummary_ProdCostInclDiscountsAndSurcharges_LEAP_1A(
            String workscope,
            List<Double> expectedProdCostInclDiscountsAndSurcharges
    ) throws IOException {
        String errorMessage = String.format(ATTRIBUTE_DO_NOT_MATCH_FOR_WORKSCOPE, PRODUCTION_COST_AFTER_DISCOUNTS_AND_SURCHARGES, workscope);
        List<Double> prodCostInclDiscountsAndSurcharges = WorkscopeUtil.extractValueFromResponse(sharedWorkscopeSummaryResponse, WorkscopeSummaryItemDto::getProductionCostAfterDiscountAndSurcharges, workscope);
        assertListEqualsWithTolerance(prodCostInclDiscountsAndSurcharges, expectedProdCostInclDiscountsAndSurcharges, BIG_TOLERANCE, errorMessage);
    }

    @Test(dataProvider = "summaryDb2ValuesLEAP_1A")
    public void verifyWorkscopeSummary_db2_LEAP_1A(String workscope, List<Double> expectedDb2) throws IOException {
        String errorMessage = String.format(ATTRIBUTE_DO_NOT_MATCH_FOR_WORKSCOPE, DB2, workscope);
        List<Double> db2Value = WorkscopeUtil.extractValueFromResponse(sharedWorkscopeSummaryResponse, WorkscopeSummaryItemDto::getDb2, workscope);
        assertListEqualsWithTolerance(db2Value, expectedDb2, BIG_TOLERANCE, errorMessage);
    }

    @Test(dataProvider = "summaryDb2PercentageValuesLEAP_1A")
    public void verifyWorkscopeSummary_db2Percentage_LEAP_1A(String workscope, List<Double> expectedDb2Percentage) throws IOException {
        String errorMessage = String.format(ATTRIBUTE_DO_NOT_MATCH_FOR_WORKSCOPE, DB2_PERCENTAGE, workscope);
        List<Double> db2PercentageValues = WorkscopeUtil.extractValueFromResponse(sharedWorkscopeSummaryResponse, WorkscopeSummaryItemDto::getDb2Percentage, workscope);
        assertListEqualsWithTolerance(db2PercentageValues, expectedDb2Percentage, TOLERANCE, errorMessage);
    }

    @Test(dataProvider = "summaryEbitValuesLEAP_1A")
    public void verifyWorkscopeSummary_ebit_LEAP_1A(String workscope, List<Double> expectedEbit) throws IOException {
        String errorMessage = String.format(ATTRIBUTE_DO_NOT_MATCH_FOR_WORKSCOPE, EBIT, workscope);
        List<Double> ebitValues = WorkscopeUtil.extractValueFromResponse(sharedWorkscopeSummaryResponse, WorkscopeSummaryItemDto::getEbit, workscope);
        assertListEqualsWithTolerance(ebitValues, expectedEbit, BIG_TOLERANCE, errorMessage);
    }

    @Test(dataProvider = "summaryEbitPercentageValuesLEAP_1A")
    public void verifyWorkscopeSummary_ebitPercentage_LEAP_1A(String workscope, List<Double> expectedEbitPercentage) throws IOException {
        String errorMessage = String.format(ATTRIBUTE_DO_NOT_MATCH_FOR_WORKSCOPE, EBIT_PERCENTAGE, workscope);
        List<Double> ebitPercentageValues = WorkscopeUtil.extractValueFromResponse(sharedWorkscopeSummaryResponse, WorkscopeSummaryItemDto::getEbitPercentage, workscope);
        assertListEqualsWithTolerance(ebitPercentageValues, expectedEbitPercentage, TOLERANCE, errorMessage);
    }

    @Test(dataProvider = "summaryEatPercentageValuesLEAP_1A")
    public void verifyWorkscopeSummary_eatPercentage_LEAP_1A(String workscope, List<Double> expectedEatPercentage) throws IOException {
        String errorMessage = String.format(ATTRIBUTE_DO_NOT_MATCH_FOR_WORKSCOPE, EAT_PERCENTAGE, workscope);
        List<Double> eatPercentageValues = WorkscopeUtil.extractValueFromResponse(sharedWorkscopeSummaryResponse, WorkscopeSummaryItemDto::getEatPercentage, workscope);
        assertListEqualsWithTolerance(eatPercentageValues, expectedEatPercentage, TOLERANCE, errorMessage);
    }

    @Test(dataProvider = "summaryNetMarginePercentageLEAP_1A")
    public void verifyWorkscopeSummary_netMargin_LEAP_1A(String workscope, List<Double> expectedNetMargin) throws IOException {
        String errorMessage = String.format(ATTRIBUTE_DO_NOT_MATCH_FOR_WORKSCOPE, NET_MARGIN, workscope);
        List<Double> netMarginPercentage = WorkscopeUtil.extractValueFromResponse(sharedWorkscopeSummaryResponse, WorkscopeSummaryItemDto::getNetMargin, workscope);
        assertListEqualsWithTolerance(netMarginPercentage, expectedNetMargin, TOLERANCE, errorMessage);
    }
}
