package com.lht.corecalculation.api.wssummary;

import com.lht.corecalculation.api.request.wssummary.PostWorkscopeSummaryRequest;
import com.lht.corecalculation.api.utils.WorkscopeUtil;
import com.lht.corecalculation.base.BaseCocaApiTest;
import io.restassured.response.Response;
import java.io.IOException;
import org.testng.annotations.Test;
import org.testng.asserts.SoftAssert;


import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_LEAP_1A_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.NO_ROLE_TOKEN;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_200;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_400;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_401;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_403;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.USER_1_TOKEN;
import static com.lht.corecalculation.api.constants.GlobalConstants.VIEW_ONLY_TOKEN;
import static com.lht.corecalculation.api.utils.WorkscopeUtil.initializeWorkscopeAndAwaitCompletionThenFetchSummary;
import static org.testng.Assert.assertEquals;

public class GetWorkscopeSummaryApiTests extends BaseCocaApiTest {

    @Test(groups = "smoke", dataProvider = "adminAccessTokenProvider")
    public void verifyWorkscopeSummary_ReturnResults_AfterAllStepsAreValid_AdminUser(String accessToken) throws IOException, InterruptedException, WorkscopeUtil.ApiException {
        WorkscopeUtil.updateEngineValues(ENGINE_LEAP_1A_QUOTATION_ID, accessToken,
                3, 3, 3, 3, 2500, 4000,
                75.0,
                60, 60, 0.0,
                10.0, false,
                10.0, false,
                3.0, 2500,
                3.0, 5.0, 0.0, 6.28, 6.28);

        Response response = initializeWorkscopeAndAwaitCompletionThenFetchSummary(ENGINE_LEAP_1A_QUOTATION_ID, accessToken);
        assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
    }

    @Test(dataProvider = "adminAccessTokenProvider")
    public void verifyWorkscopeSummary_CannotOpen_WithInvalidStep_InMaterialZ2Pricing(String accessToken) throws IOException {
        WorkscopeUtil.updateEngineValues(ENGINE_LEAP_1A_QUOTATION_ID, accessToken,
                3, 3, 3, 3, 2500, 4000,
                null,
                60, 60, 0.0,
                10.0, false,
                10.0, false,
                3.0, 2500,
                3.0, 5.0, 0.0, 6.28, 6.28);

        PostWorkscopeSummaryRequest postWorkscopeSummaryRequest = new PostWorkscopeSummaryRequest(ENGINE_LEAP_1A_QUOTATION_ID)
                .withBearerToken(accessToken);
        Response response = postWorkscopeSummaryRequest.callAPI();
        assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_400, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
    }

    @Test(dataProvider = "adminAccessTokenProvider")
    public void verifyWorkscopeSummary_CannotOpen_WithInvalidStep_InLabourRate(String accessToken) throws IOException {
        WorkscopeUtil.updateEngineValues(ENGINE_LEAP_1A_QUOTATION_ID, accessToken,
                0, 3, 3, 3, 2500, 4000,
                80.0,
                null, 60, 0.0,
                10.0, false,
                10.0, false,
                3.0, 2500,
                3.0, 5.0, 0.0, 6.28, 6.28);

        PostWorkscopeSummaryRequest postWorkscopeSummaryRequest = new PostWorkscopeSummaryRequest(ENGINE_LEAP_1A_QUOTATION_ID)
                .withBearerToken(accessToken);
        Response response = postWorkscopeSummaryRequest.callAPI();
        assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_400, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
    }

    @Test(dataProvider = "adminAccessTokenProvider")
    public void verifyWorkscopeSummary_CannotOpen_WithInvalidStep_InSubcontractPricingHC(String accessToken) throws IOException {
        WorkscopeUtil.updateEngineValues(ENGINE_LEAP_1A_QUOTATION_ID, accessToken,
                3.0, 3.0, 3.0, 3.0, 2500, 4000,
                80.0,
                60, 60, 0.0,
                10.0, false,
                10.0, false,
                null, 2500,
                3.0, 5.0, 0.0, 6.28, 6.28);

        PostWorkscopeSummaryRequest postWorkscopeSummaryRequest = new PostWorkscopeSummaryRequest(ENGINE_LEAP_1A_QUOTATION_ID)
                .withBearerToken(accessToken);
        Response response = postWorkscopeSummaryRequest.callAPI();
        assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_400, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
    }

    @Test(dataProvider = "adminAccessTokenProvider")
    public void verifyWorkscopeSummary_ReturnResults_AfterAllStepsAreValid_NotOwnerUser_And_ViewOnlyUser(String accessToken) throws IOException, InterruptedException, WorkscopeUtil.ApiException {
        String userToken = utils.context.getContextItem(USER_1_TOKEN);
        String viewOnlyUser = utils.context.getContextItem(VIEW_ONLY_TOKEN);
        WorkscopeUtil.updateEngineValues(ENGINE_LEAP_1A_QUOTATION_ID, accessToken,
                3, 3, 3, 3, 2500, 4000,
                75.0,
                60, 60, 0.0,
                10.0, false,
                10.0, false,
                3.0, 2500,
                3.0, 5.0, 0.0, 6.28, 6.28);

        Response notOwnerUser = initializeWorkscopeAndAwaitCompletionThenFetchSummary(ENGINE_LEAP_1A_QUOTATION_ID, userToken);
        Response viewOnlyUserResponse = initializeWorkscopeAndAwaitCompletionThenFetchSummary(ENGINE_LEAP_1A_QUOTATION_ID, viewOnlyUser);

        var soft = new SoftAssert();
        soft.assertEquals(notOwnerUser.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
        soft.assertEquals(viewOnlyUserResponse.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
        soft.assertAll();
    }

    @Test(dataProvider = "adminAccessTokenProvider")
    public void verifyWorkscopeSummary_ReturnResults_AfterAllStepsAreValid_NoRoleUser(String accessToken) throws IOException {
        String userToken = utils.context.getContextItem(NO_ROLE_TOKEN);
        WorkscopeUtil.updateEngineValues(ENGINE_LEAP_1A_QUOTATION_ID, accessToken,
                3, 3, 3, 3, 2500, 4000,
                75.0,
                60, 60, 0.0,
                10.0, false,
                10.0, false,
                3.0, 2500,
                3.0, 5.0, 0.0, 6.28, 6.28);

        PostWorkscopeSummaryRequest postWorkscopeSummaryRequest = new PostWorkscopeSummaryRequest(ENGINE_LEAP_1A_QUOTATION_ID)
                .withBearerToken(userToken);
        Response response = postWorkscopeSummaryRequest.callAPI();
        assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_401, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
    }
}