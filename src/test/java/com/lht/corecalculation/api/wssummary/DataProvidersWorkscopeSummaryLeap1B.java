package com.lht.corecalculation.api.wssummary;

import com.lht.corecalculation.base.BaseCocaApiTest;
import java.util.Arrays;
import org.testng.annotations.DataProvider;


import static com.lht.corecalculation.api.constants.GlobalConstants.WS01;
import static com.lht.corecalculation.api.constants.GlobalConstants.WS02;
import static com.lht.corecalculation.api.constants.GlobalConstants.WS03;
import static com.lht.corecalculation.api.constants.GlobalConstants.WS03_1;
import static com.lht.corecalculation.api.constants.GlobalConstants.WS04;
import static com.lht.corecalculation.api.constants.GlobalConstants.WS05;
import static com.lht.corecalculation.api.constants.GlobalConstants.WS06;
import static com.lht.corecalculation.api.constants.GlobalConstants.WS07;
import static com.lht.corecalculation.api.constants.GlobalConstants.WS08;
import static com.lht.corecalculation.api.constants.GlobalConstants.WS09;
import static com.lht.corecalculation.api.constants.GlobalConstants.WS10;

public class DataProvidersWorkscopeSummaryLeap1B extends BaseCocaApiTest {

    @DataProvider(name = "summaryProductionCostLEAP_1B_1")
    public Object[][] workscopeSummaryProductionCostDataProviderLEAP_1B_1() {
        return new Object[][] {
                {WS03_1, Arrays.asList(8245152.0, 8651978.0, 9155245.0, 9456415.0)},
                {WS01, Arrays.asList(5021635.0, 5224833.0, 5482452.0, 5795279.0)},
                {WS04, Arrays.asList(1.0539898E7, 1.095987E7, 1.1576433E7, 1.1674498E7)},
                {WS06, Arrays.asList(924125.0, 963893.0, 1017042.0, 1045735.0)},
                {WS05, Arrays.asList(2660138.0, 2758314.0, 2921762.0, 2726403.0)}
        };
    }

    @DataProvider(name = "summaryProductionCostLEAP_1B_2")
    public Object[][] workscopeSummaryProductionCostDataProviderLEAP_1B_2() {
        return new Object[][] {
                {WS07, Arrays.asList(1202293.0, 1239672.0, 1309464.0, 1312253.0)},
                {WS03, Arrays.asList(8595898.0, 8987287.0, 9481017.0, 9801789.0)},
                {WS08, Arrays.asList(202255.0, 196050.0, 202555.0, 207519.0)},
                {WS09, Arrays.asList(158716.0, 151953.0, 156469.0, 159575.0)},
                {WS02, Arrays.asList(5658744.0, 5890038.0, 6188241.0, 6432125.0)},
                {WS10, Arrays.asList(1228113.0, 1280948.0, 1353169.0, 1427386.0)}
        };
    }

    @DataProvider(name = "summaryDiscountsLEAP_1B_1")
    public Object[][] workscopeSummaryDiscountsDataProviderLEAP_1B_1() {
        return new Object[][] {
                {WS03_1, Arrays.asList(982466.0, 1446582.0, 1699908.0, 1322000.0)},
                {WS01, Arrays.asList(673794.0, 969737.0, 1132267.0, 908914.0)},
                {WS04, Arrays.asList(1375585.0, 1929917.0, 2270558.0, 1753872.0)},
                {WS06, Arrays.asList(137847.0, 176958.0, 200154.0, 176666.0)},
                {WS05, Arrays.asList(351700.0, 502660.0, 598434.0, 417792.0)}
        };
    }

    @DataProvider(name = "summaryDiscountsLEAP_1B_2")
    public Object[][] workscopeSummaryDiscountsDataProviderLEAP_1B_2() {
        return new Object[][] {
                {WS07, Arrays.asList(144286.0, 205909.0, 244418.0, 184896.0)},
                {WS03, Arrays.asList(1037111.0, 1514269.0, 1772211.0, 1384564.0)},
                {WS08, Arrays.asList(8451.0, 11836.0, 14007.0, 11709.0)},
                {WS09, Arrays.asList(5301.0, 7190.0, 8486.0, 7228.0)},
                {WS02, Arrays.asList(767435.0, 1100686.0, 1286868.0, 1018570.0)},
                {WS10, Arrays.asList(147064.0, 222712.0, 266008.0, 207628.0)}
        };
    }

    @DataProvider(name = "summaryRevenueLEAP_1B_1")
    public Object[][] workscopeSummaryRevenueDataProviderLEAP_1B_1() {
        return new Object[][] {
                {WS03_1, Arrays.asList(8509825.0, 9018616.0, 9546785.0, 9880982.0)},
                {WS01, Arrays.asList(5175300.0, 5446906.0, 5719277.0, 6053664.0)},
                {WS04, Arrays.asList(1.0991831E7, 1.1591526E7, 1.2251781E7, 1.2407144E7)},
                {WS06, Arrays.asList(999298.0, 1064009.0, 1123687.0, 1161145.0)},
                {WS05, Arrays.asList(2760225.0, 2906305.0, 3079707.0, 2899306.0)}
        };
    }

    @DataProvider(name = "summaryRevenueLEAP_1B_2")
    public Object[][] workscopeSummaryRevenueDataProviderLEAP_1B_2() {
        return new Object[][] {
                {WS07, Arrays.asList(1280527.0, 1348831.0, 1426158.0, 1443544.0)},
                {WS03, Arrays.asList(8873447.0, 9370691.0, 9890212.0, 1.0245305E7)},
                {WS08, Arrays.asList(245725.0, 255796.0, 266127.0, 276925.0)},
                {WS09, Arrays.asList(194788.0, 202002.0, 209581.0, 217477.0)},
                {WS02, Arrays.asList(5824905.0, 6132941.0, 6447460.0, 6712561.0)},
                {WS10, Arrays.asList(1269763.0, 1342283.0, 1418254.0, 1498715.0)}
        };
    }

    @DataProvider(name = "summarySurchargesCostLEAP_1B_1")
    public Object[][] workscopeSummarySurchargesCostDataProviderLEAP_1B_1() {
        return new Object[][] {
                {WS03_1, Arrays.asList(347004.0, 417185.0, 397739.0, 399239.0)},
                {WS01, Arrays.asList(372633.0, 402621.0, 413628.0, 442262.0)},
                {WS04, Arrays.asList(414892.0, 487325.0, 471580.0, 469535.0)},
                {WS06, Arrays.asList(131287.0, 152267.0, 150047.0, 161815.0)},
                {WS05, Arrays.asList(261639.0, 287571.0, 293728.0, 290383.0)}
        };
    }

    @DataProvider(name = "summarySurchargesCostLEAP_1B_2")
    public Object[][] workscopeSummarySurchargesCostDataProviderLEAP_1B_2() {
        return new Object[][] {
                {WS07, Arrays.asList(158975.0, 180577.0, 180112.0, 190510.0)},
                {WS03, Arrays.asList(352636.0, 422349.0, 402576.0, 404376.0)},
                {WS08, Arrays.asList(93460.0, 112186.0, 107565.0, 118662.0)},
                {WS09, Arrays.asList(90858.0, 109526.0, 104777.0, 115752.0)},
                {WS02, Arrays.asList(415529.0, 447388.0, 461136.0, 484544.0)},
                {WS10, Arrays.asList(163570.0, 186428.0, 186333.0, 202210.0)}
        };
    }

    @DataProvider(name = "summaryProdCostInclDiscountsAndSurchargesLEAP_1B")
    public Object[][] workscopeSummaryProdCostInclDiscountsAndSurchargesDataProviderLEAP_1B() {
        return new Object[][] {
                {WS03_1, Arrays.asList(7609690.0, 7622581.0, 7853077.0, 8533655.0)},
                {WS01, Arrays.asList(4720474.0, 4657717.0, 4763813.0, 5328627.0)},
                {WS04, Arrays.asList(9579204.0, 9517278.0, 9777455.0, 10390161.0)},
                {WS06, Arrays.asList(917565.0, 939202.0, 966934.0, 1030884.0)},
                {WS05, Arrays.asList(2570077.0, 2543225.0, 2617056.0, 2598993.0)},
                {WS07, Arrays.asList(1216982.0, 1214339.0, 1245158.0, 1317867.0)},
                {WS03, Arrays.asList(7911423.0, 7895367.0, 8111382.0, 8821602.0)},
                {WS08, Arrays.asList(287265.0, 296400.0, 296114.0, 314472.0)},
                {WS09, Arrays.asList(244273.0, 254289.0, 252760.0, 268099.0)},
                {WS02, Arrays.asList(5306838.0, 5236739.0, 5362509.0, 5898100.0)},
                {WS10, Arrays.asList(1244619.0, 1244665.0, 1273494.0, 1421968.0)}
        };
    }

    @DataProvider(name = "summaryDb2ValuesLEAP_1B")
    public Object[][] workscopeSummaryDb2ValuesLEAP_1B() {
        return new Object[][] {
                {WS03_1, Arrays.asList(900134.0, 1396035.0, 1693708.0, 1347326.0)},
                {WS01, Arrays.asList(454826.0, 789189.0, 955463.0, 725036.0)},
                {WS04, Arrays.asList(1412627.0, 2074247.0, 2474326.0, 2016983.0)},
                {WS06, Arrays.asList(81732.0, 124807.0, 156752.0, 130260.0)},
                {WS05, Arrays.asList(190148.0, 363078.0, 462650.0, 300313.0)},
                {WS07, Arrays.asList(63545.0, 134491.0, 181000.0, 125676.0)},
                {WS03, Arrays.asList(962023.0, 1475324.0, 1778830.0, 1423702.0)},
                {WS08, Arrays.asList(-41539.0, -40604.0, -29986.0, -37547.0)},
                {WS09, Arrays.asList(-49485.0, -52287.0, -43179.0, -50622.0)},
                {WS02, Arrays.asList(518066.0, 896201.0, 1084950.0, 814461.0)},
                {WS10, Arrays.asList(25143.0, 97617.0, 144759.0, 76746.0)}
        };
    }

    @DataProvider(name = "summaryDb2PercentageValuesLEAP_1B")
    public Object[][] workscopeSummaryDb2PercentageValuesLEAP_1B() {
        return new Object[][] {
                {WS03_1, Arrays.asList(10.577593, 15.479488, 17.741140, 13.635550)},
                {WS01, Arrays.asList(8.788400, 14.488765, 16.706022, 11.976825)},
                {WS04, Arrays.asList(12.851607, 17.894514, 20.195648, 16.256626)},
                {WS06, Arrays.asList(8.178981, 11.729878, 13.949821, 11.218294)},
                {WS05, Arrays.asList(6.8983707, 12.492800, 15.022556, 10.358107)},
                {WS07, Arrays.asList(4.962422, 9.970986, 12.691470, 8.706100)},
                {WS03, Arrays.asList(10.841603, 15.744027, 17.985765, 13.896149)},
                {WS08, Arrays.asList(-16.904903, -15.873817, -11.267821, -13.558637)},
                {WS09, Arrays.asList(-25.404697, -25.884588, -20.60255, -23.277048)},
                {WS02, Arrays.asList(8.893992, 14.612919, 16.827560, 12.133395)},
                {WS10, Arrays.asList(1.9802015, 7.272532, 10.206897, 5.1208434)}
        };
    }

    @DataProvider(name = "summaryEbitValuesLEAP_1B")
    public Object[][] workscopeSummaryEbitValuesLEAP_1B() {
        return new Object[][] {
                {WS03_1, Arrays.asList(407362.0, 903263.0, 1124282.0, 887405.0)},
                {WS01, Arrays.asList(356271.0, 690635.0, 841578.0, 633052.0)},
                {WS04, Arrays.asList(919854.0, 1581474.0, 1904900.0, 1557062.0)},
                {WS06, Arrays.asList(-16822.0, 26252.0, 42867.0, 38276.0)},
                {WS05, Arrays.asList(91593.0, 264524.0, 348765.0, 208329.0)},
                {WS07, Arrays.asList(-35009.0, 35937.0, 67115.0, 33692.0)},
                {WS03, Arrays.asList(469251.0, 982551.0, 1209404.0, 963781.0)},
                {WS08, Arrays.asList(-140094.0, -139159.0, -143871.0, -129531.0)},
                {WS09, Arrays.asList(-148039.0, -150841.0, -157064.0, -142606.0)},
                {WS02, Arrays.asList(419512.0, 797647.0, 971065.0, 722477.0)},
                {WS10, Arrays.asList(-73410.0, -936.0, 30874.0, -15237.0)}
        };
    }

    @DataProvider(name = "summaryEbitPercentageValuesLEAP_1B")
    public Object[][] workscopeSummaryEbitPercentageValuesLEAP_1B() {
        return new Object[][] {
                {WS03_1, Arrays.asList(4.786963, 10.015540, 11.776558, 8.980942)},
                {WS01, Arrays.asList(6.884076, 12.679399, 14.714770, 10.457346)},
                {WS04, Arrays.asList(8.368527, 13.643370, 15.547949, 12.549722)},
                {WS06, Arrays.asList(-1.683390, 2.467324, 3.814867, 3.296442)},
                {WS05, Arrays.asList(3.3278477, 9.101741, 11.324633, 7.185479)},
                {WS07, Arrays.asList(-2.733975, 2.664330, 4.706020, 2.333991)},
                {WS03, Arrays.asList(5.288265, 10.485370, 12.228295, 9.407059)},
                {WS08, Arrays.asList(-57.01247, -54.402363, -54.0613, -46.77488)},
                {WS09, Arrays.asList(-76.00035, -74.67337, -74.941864, -65.57303)},
                {WS02, Arrays.asList(7.202042, 13.005949, 15.061203, 10.763065)},
                {WS10, Arrays.asList(-5.7814426, -0.0697718, 2.17694, -1.0166907)}
        };
    }

    @DataProvider(name = "summaryEatPercentageValuesLEAP_1B")
    public Object[][] workscopeSummaryEatPercentageValuesLEAP_1B() {
        return new Object[][] {
                {WS03_1, Arrays.asList(3.590222, 7.511655, 8.832418, 6.735706)},
                {WS01, Arrays.asList(5.163057, 9.509549, 11.036078, 7.843010)},
                {WS04, Arrays.asList(6.276396, 10.232528, 11.660962, 9.412292)},
                {WS06, Arrays.asList(-1.262543, 1.850493, 2.861150, 2.472332)},
                {WS05, Arrays.asList(2.4958858, 6.833817, 8.500988, 5.389110)},
                {WS07, Arrays.asList(-2.050481, 1.998248, 3.529515, 1.750493)},
                {WS03, Arrays.asList(3.966199, 7.864028, 9.171222, 7.055295)},
                {WS08, Arrays.asList(-42.759354, -40.801773, -40.545975, -35.08116)},
                {WS09, Arrays.asList(-57.000263, -56.00503, -56.206398, -49.17977)},
                {WS02, Arrays.asList(5.401531, 9.754462, 11.295902, 8.072299)},
                {WS10, Arrays.asList(-4.336082, -0.052328847, 1.632705, -0.76251805)}
        };
    }

    @DataProvider(name = "summaryNetMarginePercentageLEAP_1B")
    public Object[][] workscopeSummaryNetMarginePercentageLEAP_1B() {
        return new Object[][] {
                {WS01, Arrays.asList(2.763057, 7.109550, 8.636190, 5.443009)},
                {WS04, Arrays.asList(3.876396, 7.832528, 9.260962, 7.012291)},
                {WS06, Arrays.asList(-3.662543, -0.549507, 0.461150, 0.072332)},
                {WS05, Arrays.asList(0.0958858, 4.4338174, 6.092276, 2.989110)},
                {WS07, Arrays.asList(-4.450481, -0.401753, 1.129516, -0.649507)},
                {WS03, Arrays.asList(1.566199, 5.464027, 6.769282, 4.655294)},
                {WS08, Arrays.asList(-45.159355, -43.201775, -42.945972, -37.48116)},
                {WS09, Arrays.asList(-59.400265, -58.40503, -58.6064, -51.579773)},
                {WS02, Arrays.asList(3.001531, 7.354462, 8.893699, 5.672299)},
                {WS10, Arrays.asList(-6.736082, -2.4523287, -0.767295, -3.162518)}
        };
    }
}