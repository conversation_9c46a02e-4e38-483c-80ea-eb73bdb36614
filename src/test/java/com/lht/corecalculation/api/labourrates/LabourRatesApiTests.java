package com.lht.corecalculation.api.labourrates;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.lht.corecalculation.api.pojo.dto.labourrates.LabourRateDto;
import com.lht.corecalculation.api.request.labourrates.PutLabourRates;
import com.lht.corecalculation.api.utils.LabourRatesUtil;
import com.lht.corecalculation.base.BaseCocaApiTest;
import io.restassured.response.Response;
import org.testng.Assert;
import org.testng.annotations.Test;


import static com.lht.corecalculation.api.constants.GlobalConstants.ADMIN_TOKEN;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_CFM56_5B_PMA_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_CFM56_5B_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_LEAP_1A_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_LEAP_1B_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_V2500_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ERROR_DETAIL;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_400;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE;

public class LabourRatesApiTests extends BaseCocaApiTest {

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "positiveLabourRatesValues")
    public void verifyLabourRatesValidInputs_LEAP_1A_255(
            Integer routineLabourRateValue,
            Integer nonRoutineLabourRateValue,
            Double eparDiscountValue
    ) throws JsonProcessingException {
        String accessToken = utils.context.getContextItem(ADMIN_TOKEN);
        LabourRatesUtil.verifyLabourRatesValidInputs(routineLabourRateValue, nonRoutineLabourRateValue, eparDiscountValue, ENGINE_LEAP_1A_QUOTATION_ID, accessToken);
    }

    @Test(dataProvider = "positiveLabourRatesValues")
    public void verifyLabourRatesValidInputs_CFM56_5B_PMA(
            Integer routineLabourRateValue,
            Integer nonRoutineLabourRateValue,
            Double eparDiscountValue
    ) throws JsonProcessingException {
        String accessToken = utils.context.getContextItem(ADMIN_TOKEN);
        LabourRatesUtil.verifyLabourRatesValidInputs(routineLabourRateValue, nonRoutineLabourRateValue, eparDiscountValue, ENGINE_CFM56_5B_PMA_QUOTATION_ID, accessToken);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "positiveLabourRatesValues")
    public void verifyLabourRatesValidInputs_LEAP_1B(
            Integer routineLabourRateValue,
            Integer nonRoutineLabourRateValue,
            Double eparDiscountValue
    ) throws JsonProcessingException {
        String accessToken = utils.context.getContextItem(ADMIN_TOKEN);
        LabourRatesUtil.verifyLabourRatesValidInputs(routineLabourRateValue, nonRoutineLabourRateValue, eparDiscountValue, ENGINE_LEAP_1B_QUOTATION_ID, accessToken);
    }

    @Test(groups = {"smoke", "workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "positiveLabourRatesValues")
    public void verifyLabourRatesValidInputs_V2500(
            Integer routineLabourRateValue,
            Integer nonRoutineLabourRateValue,
            Double eparDiscountValue
    ) throws JsonProcessingException {
        String accessToken = utils.context.getContextItem(ADMIN_TOKEN);
        LabourRatesUtil.verifyLabourRatesValidInputs(routineLabourRateValue, nonRoutineLabourRateValue, eparDiscountValue, ENGINE_V2500_QUOTATION_ID, accessToken);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "positiveLabourRatesValues")
    public void verifyLabourRatesValidInputs_CFM56_5B(
            Integer routineLabourRateValue,
            Integer nonRoutineLabourRateValue,
            Double eparDiscountValue
    ) throws JsonProcessingException {
        String accessToken = utils.context.getContextItem(ADMIN_TOKEN);
        LabourRatesUtil.verifyLabourRatesValidInputs(routineLabourRateValue, nonRoutineLabourRateValue, eparDiscountValue, ENGINE_CFM56_5B_QUOTATION_ID, accessToken);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "adminAccessTokenProvider")
    public void verifyLabourRatesEparDiscountCannotBeNull_LEAP_1A(String accessToken) throws JsonProcessingException {
        LabourRateDto labourRatesInputValues = LabourRateDto.builder()
                .nonRoutineLabourRate(123)
                .routineLabourRate(321)
                .build();

        PutLabourRates putLabourRates =
                new PutLabourRates(ENGINE_LEAP_1A_QUOTATION_ID, utils.convert.dtoToJsonString(labourRatesInputValues))
                        .withBearerToken(accessToken);
        Response putResponse = putLabourRates.callAPI();

        Assert.assertEquals(putResponse.statusCode(), RESPONSE_STATUS_CODE_400, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
        Assert.assertTrue((putResponse.getBody().jsonPath().get(ERROR_DETAIL).toString()).contains("Epar discount cannot be null"));
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "adminAccessTokenProvider")
    public void verifyLabourRatesRoutineLabourCannotBeNegativeValue_LEAP_1A(String accessToken) throws JsonProcessingException {
        LabourRateDto labourRatesInputValues = LabourRateDto.builder()
                .routineLabourRate(-123)
                .nonRoutineLabourRate(123)
                .eparDiscount(33.3)
                .build();

        PutLabourRates putLabourRates =
                new PutLabourRates(ENGINE_LEAP_1A_QUOTATION_ID, utils.convert.dtoToJsonString(labourRatesInputValues))
                        .withBearerToken(accessToken);
        Response putResponse = putLabourRates.callAPI();

        Assert.assertEquals(putResponse.statusCode(), RESPONSE_STATUS_CODE_400, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
        Assert.assertTrue((putResponse.getBody().jsonPath().get(ERROR_DETAIL).toString()).contains("Routine labour rate cannot be negative value"));
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "adminAccessTokenProvider")
    public void verifyLabourRatesNonRoutineLabourCannotBeNegativeValue_LEAP_1A(String accessToken) throws JsonProcessingException {
        LabourRateDto labourRatesInputValues = LabourRateDto.builder()
                .routineLabourRate(123)
                .nonRoutineLabourRate(-123)
                .eparDiscount(33.3)
                .build();

        PutLabourRates putLabourRates =
                new PutLabourRates(ENGINE_LEAP_1A_QUOTATION_ID, utils.convert.dtoToJsonString(labourRatesInputValues))
                        .withBearerToken(accessToken);
        Response putResponse = putLabourRates.callAPI();

        Assert.assertEquals(putResponse.statusCode(), RESPONSE_STATUS_CODE_400, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
        Assert.assertTrue((putResponse.getBody().jsonPath().get(ERROR_DETAIL).toString()).contains("Non-routine labour rate cannot be negative value"));
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "adminAccessTokenProvider")
    public void verifyLabourRatesEparDiscountCannotBeNegativeValue_LEAP_1A(String accessToken) throws JsonProcessingException {
        LabourRateDto labourRatesInputValues = LabourRateDto.builder()
                .routineLabourRate(123)
                .nonRoutineLabourRate(321)
                .eparDiscount(-33.33)
                .build();

        PutLabourRates putLabourRates =
                new PutLabourRates(ENGINE_LEAP_1A_QUOTATION_ID, utils.convert.dtoToJsonString(labourRatesInputValues))
                        .withBearerToken(accessToken);
        Response putResponse = putLabourRates.callAPI();

        Assert.assertEquals(putResponse.statusCode(), RESPONSE_STATUS_CODE_400, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
    }
}
