package com.lht.corecalculation.api.repairexclusions;

import com.lht.corecalculation.api.handlingcharges.ClusterPartsNameOrderAssertion;
import com.lht.corecalculation.api.pojo.dto.repairexclusions.RepairExclusionWorkscopeDataDto;
import com.lht.corecalculation.api.request.repairexclusions.GetRepairExclusionsRequest;
import com.lht.corecalculation.api.utils.RepairExclusionsUtil;
import com.lht.corecalculation.base.BaseCocaApiTest;
import io.restassured.response.Response;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import org.testng.annotations.Test;


import static com.lht.corecalculation.api.constants.GlobalConstants.ADMIN_TOKEN;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_CFM56_5B_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_LEAP_1A_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_V2500_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ERROR_DETAILS_MISMATCH_AS_MESSAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.NO_ROLE_TOKEN;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_200;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_401;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_403;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.VIEW_ONLY_TOKEN;
import static com.lht.corecalculation.api.handlingcharges.ClusterOrderAssertion.getRepairExclusionsClusterOrderCfm565B;
import static com.lht.corecalculation.api.handlingcharges.ClusterOrderAssertion.getRepairExclusionsClusterOrderLeap1a;
import static com.lht.corecalculation.api.handlingcharges.ClusterOrderAssertion.getRepairExclusionsClusterOrderV2500;
import static com.lht.corecalculation.api.utils.RepairExclusionsUtil.getUniqueClusterNames;
import static com.lht.corecalculation.api.utils.RepairExclusionsUtil.getUniquePartNamesWithoutExcludedTypes;
import static org.testng.Assert.assertEquals;

public class GetAllRepairExclusionsTests extends BaseCocaApiTest {

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"})
    public void testRepairExclusionGet_V2500_ReturnsStatusCode200() {
        String adminToken = utils.context.getContextItem(ADMIN_TOKEN);
        GetRepairExclusionsRequest getRepairExclusionsRequest =
                new GetRepairExclusionsRequest(ENGINE_V2500_QUOTATION_ID)
                        .withBearerToken(adminToken);

        Response response = getRepairExclusionsRequest.callAPI();
        assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"})
    public void testRepairExclusionGet_V2500_ReturnsStatusCode200_ViewOnlyUser() {
        String adminToken = utils.context.getContextItem(VIEW_ONLY_TOKEN);
        GetRepairExclusionsRequest getRepairExclusionsRequest =
                new GetRepairExclusionsRequest(ENGINE_V2500_QUOTATION_ID)
                        .withBearerToken(adminToken);

        Response response = getRepairExclusionsRequest.callAPI();
        assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"})
    public void testRepairExclusionGet_V2500_ReturnsStatusCode200_NoRoleUser() {
        String adminToken = utils.context.getContextItem(NO_ROLE_TOKEN);
        GetRepairExclusionsRequest getRepairExclusionsRequest =
                new GetRepairExclusionsRequest(ENGINE_V2500_QUOTATION_ID)
                        .withBearerToken(adminToken);

        Response response = getRepairExclusionsRequest.callAPI();
        assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_401, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "adminAccessTokenProvider")
    public void testRepairExclusionGetClusterNames_V2500_ReturnsExpectedOrder(String accessToken) throws IOException {
        RepairExclusionWorkscopeDataDto workscopeDataDto = RepairExclusionsUtil.getRepairExclusionsResponse(accessToken, ENGINE_V2500_QUOTATION_ID);

        List<String> actualUniqueClusterNamesList = getUniqueClusterNames(workscopeDataDto);
        List<String> expectedClusterOrderList = Arrays.asList(getRepairExclusionsClusterOrderV2500());

        assertEquals(actualUniqueClusterNamesList, expectedClusterOrderList, ERROR_DETAILS_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "adminAccessTokenProvider")
    public void testRepairExclusionGetClusterNames_Leap1a_ReturnsExpectedOrder(String accessToken) throws IOException {
        RepairExclusionWorkscopeDataDto workscopeDataDto = RepairExclusionsUtil.getRepairExclusionsResponse(accessToken, ENGINE_LEAP_1A_QUOTATION_ID);

        List<String> actualUniqueClusterNamesList = getUniqueClusterNames(workscopeDataDto);
        List<String> expectedClusterOrderList = Arrays.asList(getRepairExclusionsClusterOrderLeap1a());

        assertEquals(actualUniqueClusterNamesList, expectedClusterOrderList, ERROR_DETAILS_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "adminAccessTokenProvider")
    public void testRepairExclusionGetClusterNames_Cfm565b_ReturnsExpectedOrder(String accessToken) throws IOException {
        RepairExclusionWorkscopeDataDto workscopeDataDto = RepairExclusionsUtil.getRepairExclusionsResponse(accessToken, ENGINE_CFM56_5B_QUOTATION_ID);

        List<String> actualUniqueClusterNamesList = getUniqueClusterNames(workscopeDataDto);
        List<String> expectedClusterOrderList = Arrays.asList(getRepairExclusionsClusterOrderCfm565B());

        assertEquals(actualUniqueClusterNamesList, expectedClusterOrderList, ERROR_DETAILS_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "adminAccessTokenProvider")
    public void testRepairExclusionGetPartsName_V2500_ReturnsExpectedOrder(String accessToken) throws IOException {
        RepairExclusionWorkscopeDataDto workscopeDataDto = RepairExclusionsUtil.getRepairExclusionsResponse(accessToken, ENGINE_V2500_QUOTATION_ID);

        List<String> actualUniqueClusterPartNamesList = getUniquePartNamesWithoutExcludedTypes(workscopeDataDto);
        List<String> expectedClusterPartsOrderList = Arrays.asList(ClusterPartsNameOrderAssertion.getScrapCapsClusterPartsOrderV2500());

        assertEquals(actualUniqueClusterPartNamesList, expectedClusterPartsOrderList, ERROR_DETAILS_MISMATCH_AS_MESSAGE);
    }
}
