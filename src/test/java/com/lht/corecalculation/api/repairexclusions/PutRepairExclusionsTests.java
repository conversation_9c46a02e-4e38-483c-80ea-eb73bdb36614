package com.lht.corecalculation.api.repairexclusions;

import com.lht.corecalculation.base.BaseCocaApiTest;
import java.io.IOException;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;


import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_CFM56_5B_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_CFM56_7B_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_LEAP_1A_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_LEAP_1B_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_V2500_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.FAN_BLADE;
import static com.lht.corecalculation.api.constants.GlobalConstants.HPC_VANE;
import static com.lht.corecalculation.api.constants.GlobalConstants.LPT_BLADES;
import static com.lht.corecalculation.api.constants.GlobalConstants.LPT_NOZZLES_STG_2_4;
import static com.lht.corecalculation.api.constants.GlobalConstants.MAIN_BEARINGS;
import static com.lht.corecalculation.api.constants.GlobalConstants.MESSAGE_NOT_ALL_IS_EXCLUDED_VALUES_ARE_FALSE;
import static com.lht.corecalculation.api.constants.GlobalConstants.MESSAGE_NOT_ALL_IS_EXCLUDED_VALUES_ARE_TRUE;
import static com.lht.corecalculation.api.utils.RepairExclusionsUtil.testRepairExclusionForEngine;
import static com.lht.corecalculation.api.utils.RepairExclusionsUtil.updateRepairExclusionsForCluster;

public class PutRepairExclusionsTests extends BaseCocaApiTest {

    @BeforeMethod
    public void refreshToken() throws IOException {
        refreshAccessTokens();
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "adminAccessTokenProvider")
    public void testRepairExclusion_ExcludeAllRepairs_LEAP_1A(String accessToken) throws IOException {
        testRepairExclusionForEngine(accessToken, ENGINE_LEAP_1A_QUOTATION_ID, true, MESSAGE_NOT_ALL_IS_EXCLUDED_VALUES_ARE_TRUE);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "adminAccessTokenProvider")
    public void testRepairExclusion_ExcludeAllRepairs_LEAP_1B(String accessToken) throws IOException {
        testRepairExclusionForEngine(accessToken, ENGINE_LEAP_1B_QUOTATION_ID, true, MESSAGE_NOT_ALL_IS_EXCLUDED_VALUES_ARE_TRUE);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "adminAccessTokenProvider")
    public void testRepairExclusion_ExcludeAllRepairs_V2500(String accessToken) throws IOException {
        testRepairExclusionForEngine(accessToken, ENGINE_V2500_QUOTATION_ID, true, MESSAGE_NOT_ALL_IS_EXCLUDED_VALUES_ARE_TRUE);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "adminAccessTokenProvider")
    public void testRepairExclusion_ExcludeAllRepairs_CFM56_5B(String accessToken) throws IOException {
        testRepairExclusionForEngine(accessToken, ENGINE_CFM56_5B_QUOTATION_ID, true, MESSAGE_NOT_ALL_IS_EXCLUDED_VALUES_ARE_TRUE);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "adminAccessTokenProvider")
    public void testRepairExclusion_ExcludeAllRepairs_CFM56_7B(String accessToken) throws IOException {
        testRepairExclusionForEngine(accessToken, ENGINE_CFM56_7B_QUOTATION_ID, true, MESSAGE_NOT_ALL_IS_EXCLUDED_VALUES_ARE_TRUE);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "adminAccessTokenProvider")
    public void testRepairExclusion_IncludeAllRepairs_LEAP_1A(String accessToken) throws IOException {
        testRepairExclusionForEngine(accessToken, ENGINE_LEAP_1A_QUOTATION_ID, false, MESSAGE_NOT_ALL_IS_EXCLUDED_VALUES_ARE_FALSE);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "adminAccessTokenProvider")
    public void testRepairExclusion_IncludeAllRepairs_LEAP_1B(String accessToken) throws IOException {
        testRepairExclusionForEngine(accessToken, ENGINE_LEAP_1B_QUOTATION_ID, false, MESSAGE_NOT_ALL_IS_EXCLUDED_VALUES_ARE_FALSE);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "adminAccessTokenProvider")
    public void testRepairExclusion_IncludeAllRepairs_V2500(String accessToken) throws IOException {
        testRepairExclusionForEngine(accessToken, ENGINE_V2500_QUOTATION_ID, false, MESSAGE_NOT_ALL_IS_EXCLUDED_VALUES_ARE_FALSE);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "adminAccessTokenProvider")
    public void testRepairExclusion_IncludeAllRepairs_CFM56_5B(String accessToken) throws IOException {
        testRepairExclusionForEngine(accessToken, ENGINE_CFM56_5B_QUOTATION_ID, false, MESSAGE_NOT_ALL_IS_EXCLUDED_VALUES_ARE_FALSE);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "adminAccessTokenProvider")
    public void testRepairExclusion_IncludeAllRepairs_CFM56_7B(String accessToken) throws IOException {
        testRepairExclusionForEngine(accessToken, ENGINE_CFM56_7B_QUOTATION_ID, false, MESSAGE_NOT_ALL_IS_EXCLUDED_VALUES_ARE_FALSE);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "adminAccessTokenProvider")
    public void testRepairExclusion_Exclude_FanBlade_Repairs_LEAP_1A(String accessToken) throws IOException {
        updateRepairExclusionsForCluster(accessToken, ENGINE_LEAP_1A_QUOTATION_ID, FAN_BLADE, true);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "adminAccessTokenProvider")
    public void testRepairExclusion_Exclude_HPC_Vane_Repairs_LEAP_1B(String accessToken) throws IOException {
        updateRepairExclusionsForCluster(accessToken, ENGINE_LEAP_1B_QUOTATION_ID, HPC_VANE, true);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "adminAccessTokenProvider")
    public void testRepairExclusion_Exclude_LPT_Blades_Repairs_V2500(String accessToken) throws IOException {
        updateRepairExclusionsForCluster(accessToken, ENGINE_V2500_QUOTATION_ID, LPT_BLADES, true);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "adminAccessTokenProvider")
    public void testRepairExclusion_Exclude_Main_Bearings_Repairs_CFM56_5B(String accessToken) throws IOException {
        updateRepairExclusionsForCluster(accessToken, ENGINE_CFM56_5B_QUOTATION_ID, MAIN_BEARINGS, true);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "adminAccessTokenProvider")
    public void testRepairExclusion_Exclude_LPT_Nozzles_Stg_2_4_Repairs_CFM56_7B(String accessToken) throws IOException {
        updateRepairExclusionsForCluster(accessToken, ENGINE_CFM56_7B_QUOTATION_ID, LPT_NOZZLES_STG_2_4, true);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "adminAccessTokenProvider")
    public void testRepairExclusion_Include_FanBlade_Repairs_LEAP_1A(String accessToken) throws IOException {
        updateRepairExclusionsForCluster(accessToken, ENGINE_LEAP_1A_QUOTATION_ID, FAN_BLADE, false);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "adminAccessTokenProvider")
    public void testRepairExclusion_Include_HPC_Vane_Repairs_LEAP_1B(String accessToken) throws IOException {
        updateRepairExclusionsForCluster(accessToken, ENGINE_LEAP_1B_QUOTATION_ID, HPC_VANE, false);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "adminAccessTokenProvider")
    public void testRepairExclusion_Include_LPT_Blades_Repairs_V2500(String accessToken) throws IOException {
        updateRepairExclusionsForCluster(accessToken, ENGINE_V2500_QUOTATION_ID, LPT_BLADES, false);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "adminAccessTokenProvider")
    public void testRepairExclusion_Include_Main_Bearings_Repairs_CFM56_5B(String accessToken) throws IOException {
        updateRepairExclusionsForCluster(accessToken, ENGINE_CFM56_5B_QUOTATION_ID, MAIN_BEARINGS, false);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "adminAccessTokenProvider")
    public void testRepairExclusion_Include_LPT_Nozzles_Stg_2_4_Repairs_CFM56_7B(String accessToken) throws IOException {
        updateRepairExclusionsForCluster(accessToken, ENGINE_CFM56_7B_QUOTATION_ID, LPT_NOZZLES_STG_2_4, false);
    }
}
