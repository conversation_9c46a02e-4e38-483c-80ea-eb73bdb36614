package com.lht.corecalculation.api.handlingcharges;

import com.google.gson.Gson;
import com.lht.corecalculation.api.pojo.dto.handlingcharges.HandlingChargesData;
import com.lht.corecalculation.api.pojo.dto.handlingcharges.ResultDto;
import com.lht.corecalculation.api.request.handlingcharges.GetHandlingChargesClustersParts;
import com.lht.corecalculation.api.request.handlingcharges.PutHandlingChargesRequest;
import com.lht.corecalculation.api.utils.HandlingChargesUtil;
import com.lht.corecalculation.base.BaseCocaApiTest;
import io.restassured.response.Response;
import org.testng.Assert;
import org.testng.annotations.Test;


import static com.lht.corecalculation.api.constants.GlobalConstants.ADMIN_TOKEN;
import static com.lht.corecalculation.api.constants.GlobalConstants.COMPONENTS;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_CFM56_5B_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_LEAP_1A_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_LEAP_1B_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_V2500_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ERROR_DETAILS_MISMATCH_AS_MESSAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.KITS;
import static com.lht.corecalculation.api.constants.GlobalConstants.LPT_SHROUD_SEGMENTS;
import static com.lht.corecalculation.api.constants.GlobalConstants.OTHER;
import static com.lht.corecalculation.api.constants.GlobalConstants.PARTS_PACKAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.REPAIR_MATERIAL;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_200;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_400;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.ROUTINE_MATERIAL;

public class PutIndividualPartsValueApiTests extends BaseCocaApiTest {

    @Test(groups = {"smoke", "workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "positiveValues")
    public void verifyHandlingCharges_HpcShroudValues_Engine_LEAP_1(
            String accessToken,
            double expectedZ1,
            double expectedZ2,
            double expectedPma,
            double expectedCsm
    ) {
        String adminToken = utils.context.getContextItem(ADMIN_TOKEN);
        HandlingChargesData handlingChargesData = HandlingChargesUtil.getHandlingChargesData(adminToken, ENGINE_LEAP_1A_QUOTATION_ID);
        HandlingChargesUtil.updateClustersAndParts(handlingChargesData, expectedZ1, expectedZ2, expectedPma, expectedCsm, "HPC Shroud");
        HandlingChargesUtil.updateAndAssertHandlingCharges(
                adminToken,
                ENGINE_LEAP_1A_QUOTATION_ID,
                handlingChargesData, "HPC Shroud",
                expectedZ1, expectedZ2, expectedPma, expectedCsm,
                "SM32 - Shroud HPC Stg. 1",
                "SM32 - Shroud HPC Stg. 2",
                "SM32 - Shroud HPC Stg. 3",
                "SM32 - Shroud HPC Stg. 4");
    }

    @Test(groups = {"smoke", "workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "positiveValues")
    public void verifyHandlingCharges_LpcStatorValues_Engine_V2500(
            String accessToken,
            double expectedZ1,
            double expectedZ2,
            double expectedPma,
            double expectedCsm
    ) {
        //5100162 -> id=1 ,engine=V2500
        String adminToken = utils.context.getContextItem(ADMIN_TOKEN);
        HandlingChargesData handlingChargesData = HandlingChargesUtil.getHandlingChargesData(adminToken, ENGINE_V2500_QUOTATION_ID);
        HandlingChargesUtil.updateClustersAndParts(handlingChargesData, expectedZ1, expectedZ2, expectedPma, expectedCsm, "LPC Stator");
        HandlingChargesUtil.updateAndAssertHandlingCharges(
                adminToken,
                ENGINE_V2500_QUOTATION_ID,
                handlingChargesData, "LPC Stator",
                expectedZ1, expectedZ2, expectedPma, expectedCsm,
                "M2L - Stator LPC Stg.  1.5",
                "M2L - Stator LPC Stg.  2.0",
                "M2L - Stator LPC Stg.  2.3",
                "M2L - Stator LPC Stg.  2.5");
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "positiveValues")
    public void verifyHandlingCharges_OtherValues_Engine_V2500(
            String accessToken,
            double expectedZ1,
            double expectedZ2,
            double expectedPma,
            double expectedCsm
    ) {
        //5100162 -> id=1 ,engine=V2500
        String adminToken = utils.context.getContextItem(ADMIN_TOKEN);
        HandlingChargesData handlingChargesData = HandlingChargesUtil.getHandlingChargesData(adminToken, ENGINE_V2500_QUOTATION_ID);
        HandlingChargesUtil.updateClustersAndParts(handlingChargesData, expectedZ1, expectedZ2, expectedPma, expectedCsm, OTHER);
        HandlingChargesUtil.updateAndAssertHandlingCharges(
                adminToken,
                ENGINE_V2500_QUOTATION_ID,
                handlingChargesData, OTHER,
                expectedZ1, expectedZ2, expectedPma, expectedCsm,
                "M2S - Acoustic Liner",
                "M4 - Bearing No.  4 Scavenge Tube Assy",
                "M6 - Cooling Duct HPT Stg.  1",
                "M8S - Support Ring Assy");
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "positiveValues")
    public void verifyHandlingCharges_OtherAPartsValues_Engine_CFM56_5B(
            String accessToken,
            double expectedZ1,
            double expectedZ2,
            double expectedPma,
            double expectedCsm
    ) {
        //5100207 -> id=2 ,engine=CFM56-5B
        String adminToken = utils.context.getContextItem(ADMIN_TOKEN);
        HandlingChargesData handlingChargesData = HandlingChargesUtil.getHandlingChargesData(adminToken, ENGINE_CFM56_5B_QUOTATION_ID);
        HandlingChargesUtil.updateClustersAndParts(handlingChargesData, expectedZ1, expectedZ2, expectedPma, expectedCsm, "other A-Parts");
        HandlingChargesUtil.updateAndAssertHandlingCharges(
                adminToken,
                ENGINE_CFM56_5B_QUOTATION_ID,
                handlingChargesData, "other A-Parts",
                expectedZ1, expectedZ2, expectedPma, expectedCsm,
                "M2I - Horizontal Gearbevel",
                "M8 - Shroud Hanger HPT",
                "AGB - Drive Seals",
                "M9R - Rot. Airseal LPT Stg.  4");

    }

    @Test(groups = "workscopeFixedPriceAndNte", dataProvider = "positiveValues")
    public void verifyHandlingCharges_HpcShroudValues_Engine_LEAP_1A(
            String accessToken,
            double expectedZ1,
            double expectedZ2,
            double expectedPma,
            double expectedCsm
    ) {
        //5100255 -> id=4 ,engine=LEAP-1A
        String adminToken = utils.context.getContextItem(ADMIN_TOKEN);
        HandlingChargesData handlingChargesData = HandlingChargesUtil.getHandlingChargesData(adminToken, ENGINE_LEAP_1A_QUOTATION_ID);
        HandlingChargesUtil.updateClustersAndParts(handlingChargesData, expectedZ1, expectedZ2, expectedPma, expectedCsm, "HPC Shroud");
        HandlingChargesUtil.updateAndAssertHandlingCharges(
                adminToken,
                ENGINE_LEAP_1A_QUOTATION_ID,
                handlingChargesData, "HPC Shroud",
                expectedZ1, expectedZ2, expectedPma, expectedCsm,
                "SM32 - Shroud HPC Stg. 1",
                "SM32 - Shroud HPC Stg. 2",
                "SM32 - Shroud HPC Stg. 3",
                "SM32 - Shroud HPC Stg. 4");

    }

    @Test(groups = "workscopeFixedPriceAndNte", dataProvider = "positiveValues")
    public void verifyHandlingCharges_FwdAftMountValues_Engine_CFM56_5B(
            String accessToken,
            double expectedZ1,
            double expectedZ2,
            double expectedPma,
            double expectedCsm
    ) {
        //5100207 -> id=2 ,engine=CFM56_5B
        String adminToken = utils.context.getContextItem(ADMIN_TOKEN);
        HandlingChargesData handlingChargesData = HandlingChargesUtil.getHandlingChargesData(adminToken, ENGINE_CFM56_5B_QUOTATION_ID);
        HandlingChargesUtil.updateClustersAndParts(handlingChargesData, expectedZ1, expectedZ2, expectedPma, expectedCsm, "Fwd & Aft Mount");
        HandlingChargesUtil.updateAndAssertHandlingCharges(
                adminToken,
                ENGINE_CFM56_5B_QUOTATION_ID,
                handlingChargesData, "Fwd & Aft Mount",
                expectedZ1, expectedZ2, expectedPma, expectedCsm,
                "QEC - Engine Aft. Mount",
                "QEC - Engine Aft. Mount",
                "QEC - Engine Aft. Mount",
                "QEC - Engine Aft. Mount");
    }

    @Test(groups = "workscopeFixedPriceAndNte", dataProvider = "adminAccessTokenProvider")
    public void verifyHandlingCharges_LptShroudSegmentsValues_Engine_LEAP_1B(String accessToken) {
        //5100219 -> id=3 ,engine=LEAP-1B
        Response response = HandlingChargesUtil.getHandlingCharges(accessToken, ENGINE_LEAP_1B_QUOTATION_ID);
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        HandlingChargesData handlingChargesData = response.as(ResultDto.class).getData();
        HandlingChargesUtil.updateClustersAndParts(handlingChargesData, 99.9, 88.8, 77.7, 66.6, 11111, 99999);
        HandlingChargesUtil.updateClustersAndPartsValues(handlingChargesData, LPT_SHROUD_SEGMENTS);
        String handlingChargesDataJson = new Gson().toJson(handlingChargesData);

        PutHandlingChargesRequest putHandlingChargesRequest = new PutHandlingChargesRequest(ENGINE_LEAP_1B_QUOTATION_ID, handlingChargesDataJson)
                .withBearerToken(accessToken);
        Response responsePut = putHandlingChargesRequest.callAPI();
        Assert.assertEquals(responsePut.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        HandlingChargesData putIndividualData = responsePut.as(ResultDto.class).getData();
        String z1Value = GetHandlingChargesClustersParts.getZ1FromParts(putIndividualData, LPT_SHROUD_SEGMENTS, "SM58 - Shroud Seg. LPT Stg. 2");
        String z2Value = GetHandlingChargesClustersParts.getZ2FromParts(putIndividualData, LPT_SHROUD_SEGMENTS, "SM58 - Shroud Seg. LPT Stg. 3");
        String pmaValue = GetHandlingChargesClustersParts.getPmaFromParts(putIndividualData, LPT_SHROUD_SEGMENTS, "SM58 - Shroud Seg. LPT Stg. 4");
        String csmValue = GetHandlingChargesClustersParts.getCsmFromParts(putIndividualData, LPT_SHROUD_SEGMENTS, "SM58 - Shroud Seg. LPT Stg. 5");
        String lineItemCap = GetHandlingChargesClustersParts.getLineItemCap();

        Assert.assertEquals(z1Value, "0.5", ERROR_DETAILS_MISMATCH_AS_MESSAGE);
        Assert.assertEquals(z2Value, "1.0", ERROR_DETAILS_MISMATCH_AS_MESSAGE);
        Assert.assertEquals(pmaValue, "1.5", ERROR_DETAILS_MISMATCH_AS_MESSAGE);
        Assert.assertEquals(csmValue, "2.0", ERROR_DETAILS_MISMATCH_AS_MESSAGE);
        Assert.assertEquals(lineItemCap, "99999", ERROR_DETAILS_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = "workscopeFixedPriceAndNte", dataProvider = "adminAccessTokenProvider")
    public void verifyHandlingCharges_KitsValues_Engine_LEAP_1B(String accessToken) {
        //5100219 -> id=3 ,engine=LEAP-1B
        Response response = HandlingChargesUtil.getHandlingCharges(accessToken, ENGINE_LEAP_1B_QUOTATION_ID);
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        HandlingChargesData handlingChargesData = response.as(ResultDto.class).getData();
        HandlingChargesUtil.updateClustersAndParts(handlingChargesData, 99.9, 88.8, 77.7, 66.6, 11111, 99999);
        HandlingChargesUtil.updateClustersAndPartsForCluster(handlingChargesData, 0.1, 0.2, 0.3, null, null, null, KITS);
        String handlingChargesDataJson = new Gson().toJson(handlingChargesData);

        PutHandlingChargesRequest putHandlingChargesRequest = new PutHandlingChargesRequest(ENGINE_LEAP_1B_QUOTATION_ID, handlingChargesDataJson)
                .withBearerToken(accessToken);
        Response responsePut = putHandlingChargesRequest.callAPI();
        Assert.assertEquals(responsePut.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        HandlingChargesData putIndividualData = responsePut.as(ResultDto.class).getData();

        String putResponse = GetHandlingChargesClustersParts.getUpdateClustersAndParts(putIndividualData, KITS).toString();

        Assert.assertTrue(putResponse.contains("name=Kits, order=41, type=KIT, quantity=-1, z1=0.1, z2=0.2, pma=0.3, csm=null, oneItemCap=null, lineItemCap=null"), ERROR_DETAILS_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = {"smoke", "workscopeFixedPriceAndNte"}, dataProvider = "adminAccessTokenProvider")
    public void verifyHandlingCharges_ComponentsValues_Engine_LEAP_1B(String accessToken) {
        //5100219 -> id=3 ,engine=LEAP-1B
        Response response = HandlingChargesUtil.getHandlingCharges(accessToken, ENGINE_LEAP_1B_QUOTATION_ID);
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        HandlingChargesData handlingChargesData = response.as(ResultDto.class).getData();
        HandlingChargesUtil.updateClustersAndParts(handlingChargesData, 99.9, 88.8, 77.7, 66.6, 11111, 99999);
        HandlingChargesUtil.updateClustersAndPartsForCluster(handlingChargesData, 0.1, 0.2, 0.3, null, null, null, COMPONENTS);
        String handlingChargesDataJson = new Gson().toJson(handlingChargesData);

        PutHandlingChargesRequest putHandlingChargesRequest = new PutHandlingChargesRequest(ENGINE_LEAP_1B_QUOTATION_ID, handlingChargesDataJson)
                .withBearerToken(accessToken);
        Response responsePut = putHandlingChargesRequest.callAPI();
        Assert.assertEquals(responsePut.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        HandlingChargesData putIndividualData = responsePut.as(ResultDto.class).getData();

        String putResponse = GetHandlingChargesClustersParts.getUpdateClustersAndParts(putIndividualData, COMPONENTS).toString();

        Assert.assertTrue(putResponse.contains("name=Components, order=42, type=COMPONENT, quantity=-1, z1=0.1, z2=0.2, pma=0.3, csm=null, oneItemCap=null, lineItemCap=null"), ERROR_DETAILS_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = "workscopeFixedPriceAndNte", dataProvider = "adminAccessTokenProvider")
    public void verifyHandlingCharges_PartPackageValues_Engine_LEAP_1B(String accessToken) {
        //5100219 -> id=3 ,engine=LEAP-1B
        Response response = HandlingChargesUtil.getHandlingCharges(accessToken, ENGINE_LEAP_1B_QUOTATION_ID);
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        HandlingChargesData handlingChargesData = response.as(ResultDto.class).getData();
        HandlingChargesUtil.updateClustersAndParts(handlingChargesData, 99.9, 88.8, 77.7, 66.6, 11111, 99999);
        HandlingChargesUtil.updateClustersAndPartsForCluster(handlingChargesData, 0.1, 0.2, 0.3, null, null, null, PARTS_PACKAGE);
        String handlingChargesDataJson = new Gson().toJson(handlingChargesData);

        PutHandlingChargesRequest putHandlingChargesRequest = new PutHandlingChargesRequest(ENGINE_LEAP_1B_QUOTATION_ID, handlingChargesDataJson)
                .withBearerToken(accessToken);
        Response responsePut = putHandlingChargesRequest.callAPI();
        Assert.assertEquals(responsePut.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        HandlingChargesData putIndividualData = responsePut.as(ResultDto.class).getData();

        String putResponse = GetHandlingChargesClustersParts.getUpdateClustersAndParts(putIndividualData, PARTS_PACKAGE).toString();

        Assert.assertTrue(putResponse.contains("name=Parts Packages, order=43, type=PARTS_PACKAGE, quantity=-1, z1=0.1, z2=0.2, pma=0.3, csm=null, oneItemCap=null, lineItemCap=null"), ERROR_DETAILS_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = {"smoke", "workscopeFixedPriceAndNte"}, dataProvider = "adminAccessTokenProvider")
    public void verifyHandlingCharges_RoutineMaterialValues_Engine_LEAP_1B(String accessToken) {
        //5100219 -> id=3 ,engine=LEAP-1B
        Response response = HandlingChargesUtil.getHandlingCharges(accessToken, ENGINE_LEAP_1B_QUOTATION_ID);
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        HandlingChargesData handlingChargesData = response.as(ResultDto.class).getData();
        HandlingChargesUtil.updateClustersAndParts(handlingChargesData, 99.9, 88.8, 77.7, 66.6, 11111, 99999);
        HandlingChargesUtil.updateClustersAndPartsForCluster(handlingChargesData, 0.1, null, null, null, null, null, ROUTINE_MATERIAL);
        String handlingChargesDataJson = new Gson().toJson(handlingChargesData);

        PutHandlingChargesRequest putHandlingChargesRequest = new PutHandlingChargesRequest(ENGINE_LEAP_1B_QUOTATION_ID, handlingChargesDataJson)
                .withBearerToken(accessToken);
        Response responsePut = putHandlingChargesRequest.callAPI();
        Assert.assertEquals(responsePut.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        HandlingChargesData putIndividualData = responsePut.as(ResultDto.class).getData();

        String putResponse = GetHandlingChargesClustersParts.getUpdateClustersAndParts(putIndividualData, ROUTINE_MATERIAL).toString();
        Assert.assertTrue(putResponse.contains("name=Routine Material, order=44, type=ROUTINE_MATERIAL, quantity=-1, z1=0.1, z2=null, pma=null, csm=null, oneItemCap=null, lineItemCap=null"), ERROR_DETAILS_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = "workscopeFixedPriceAndNte", dataProvider = "adminAccessTokenProvider")
    public void verifyHandlingCharges_NonRoutineMaterialValues_Engine_LEAP_1B(String accessToken) {
        //5100219 -> id=3 ,engine=LEAP-1B
        Response response = HandlingChargesUtil.getHandlingCharges(accessToken, ENGINE_LEAP_1B_QUOTATION_ID);
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        HandlingChargesData handlingChargesData = response.as(ResultDto.class).getData();
        HandlingChargesUtil.updateClustersAndParts(handlingChargesData, 99.9, 88.8, 77.7, 66.6, 11111, 99999);
        HandlingChargesUtil.updateClustersAndPartsForCluster(handlingChargesData, 0.1, 0.2, 0.3, null, null, null, REPAIR_MATERIAL);
        String handlingChargesDataJson = new Gson().toJson(handlingChargesData);

        PutHandlingChargesRequest putHandlingChargesRequest = new PutHandlingChargesRequest(ENGINE_LEAP_1B_QUOTATION_ID, handlingChargesDataJson)
                .withBearerToken(accessToken);
        Response responsePut = putHandlingChargesRequest.callAPI();
        Assert.assertEquals(responsePut.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        HandlingChargesData putIndividualData = responsePut.as(ResultDto.class).getData();

        String putResponse = GetHandlingChargesClustersParts.getUpdateClustersAndParts(putIndividualData, REPAIR_MATERIAL).toString();

        Assert.assertTrue(putResponse.contains("name=Non Routine Material, order=45, type=NON_ROUTINE_MATERIAL, quantity=-1, z1=0.1, z2=null, pma=null, csm=null, oneItemCap=null, lineItemCap=null"), ERROR_DETAILS_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = "workscopeFixedPriceAndNte", dataProvider = "adminAccessTokenProvider")
    public void verifyHandlingCharges_NegativeNonRoutineMaterialValues_Engine_LEAP_1B(String accessToken) {
        //5100219 -> id=3 ,engine=LEAP-1B
        Response response = HandlingChargesUtil.getHandlingCharges(accessToken, ENGINE_LEAP_1B_QUOTATION_ID);
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        HandlingChargesData handlingChargesData = response.as(ResultDto.class).getData();
        HandlingChargesUtil.updateClustersAndParts(handlingChargesData, -99.9, -88.8, -77.7, -66.66, -11111, -99999);
        HandlingChargesUtil.updateClustersAndPartsValues(handlingChargesData, REPAIR_MATERIAL);
        String handlingChargesDataJson = new Gson().toJson(handlingChargesData);

        PutHandlingChargesRequest putHandlingChargesRequest = new PutHandlingChargesRequest(ENGINE_LEAP_1B_QUOTATION_ID, handlingChargesDataJson)
                .withBearerToken(accessToken);
        Response responsePut = putHandlingChargesRequest.callAPI();
        Assert.assertEquals(responsePut.statusCode(), RESPONSE_STATUS_CODE_400, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
    }
}
