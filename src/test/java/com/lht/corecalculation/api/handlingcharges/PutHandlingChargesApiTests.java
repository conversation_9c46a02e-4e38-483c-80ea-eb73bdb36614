package com.lht.corecalculation.api.handlingcharges;

import com.google.gson.Gson;
import com.lht.corecalculation.api.enums.TestUser;
import com.lht.corecalculation.api.pojo.dto.handlingcharges.HandlingChargesData;
import com.lht.corecalculation.api.pojo.dto.handlingcharges.ResultDto;
import com.lht.corecalculation.api.request.handlingcharges.PutHandlingChargesRequest;
import com.lht.corecalculation.api.utils.HandlingChargesUtil;
import com.lht.corecalculation.api.utils.MessageFormater;
import com.lht.corecalculation.base.BaseCocaApiTest;
import io.restassured.response.Response;
import java.io.IOException;
import org.testng.Assert;
import org.testng.annotations.Test;


import static com.lht.corecalculation.api.constants.GlobalConstants.ADMIN_TOKEN;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_V2500_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ERROR_DETAIL;
import static com.lht.corecalculation.api.constants.GlobalConstants.GENERIC_ERROR_MESSAGE_MISMATCH_AS_MESSAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.INVALID_REQUEST_CONTENT;
import static com.lht.corecalculation.api.constants.GlobalConstants.OTHER;
import static com.lht.corecalculation.api.constants.GlobalConstants.QUOTATION_OWNERSHIP_CHANGED;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_200;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_400;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_403;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.USER_1_TOKEN;
import static com.lht.corecalculation.api.constants.GlobalConstants.VIEW_ONLY_TOKEN;

public class PutHandlingChargesApiTests extends BaseCocaApiTest {

    @Test(groups = "workscopeFixedPriceAndNte")
    public void verifyHandlingCharges_GlobalValues_Null_BaseCase_Engine_V2500() throws IOException {
        //5100162 -> id=1 ,engine=V2500
        String adminToken = utils.context.getContextItem(ADMIN_TOKEN);
        Response response = HandlingChargesUtil.getHandlingCharges(adminToken, ENGINE_V2500_QUOTATION_ID);

        HandlingChargesData handlingChargesData = response.as(ResultDto.class).getData();
        HandlingChargesUtil.updateClustersAndParts(handlingChargesData, 11.1, 22.2, 33.3, 44.4, 555, 666666);
        String handlingChargesDataJson = new Gson().toJson(handlingChargesData);

        PutHandlingChargesRequest putHandlingChargesRequest = new PutHandlingChargesRequest(ENGINE_V2500_QUOTATION_ID, handlingChargesDataJson)
                .withBearerToken(adminToken);
        Response responsePut = putHandlingChargesRequest.callAPI();
        Assert.assertEquals(responsePut.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        ResultDto resultDto = utils.convert.jsonToDto(responsePut, ResultDto.class);
        Assert.assertEquals(resultDto.getData().getGlobalValue().toString(), "GlobalValue(baseCase=null, z1=11.1, z2=22.2, pma=33.3, csm=44.4, oneItemCap=555, lineItemCap=666666, globalCap=null)");
    }

    @Test(groups = {"smoke", "workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "adminAccessTokenProvider")
    public void verifyHandlingCharges_GlobalValues_BaseCase_WithValue_Engine_V2500(String accessToken) throws IOException {
        //5100162 -> id=1 ,engine=V2500
        Response response = HandlingChargesUtil.getHandlingCharges(accessToken, ENGINE_V2500_QUOTATION_ID);

        HandlingChargesData handlingChargesData = response.as(ResultDto.class).getData();
        HandlingChargesUtil.updateClustersAndParts(handlingChargesData, 99.9, 99.9, 99.9, 99.9, 666, 666666);
        String handlingChargesDataJson = new Gson().toJson(handlingChargesData);

        PutHandlingChargesRequest putHandlingChargesRequest = new PutHandlingChargesRequest(ENGINE_V2500_QUOTATION_ID, handlingChargesDataJson)
                .withBearerToken(accessToken);
        Response responsePut = putHandlingChargesRequest.callAPI();
        Assert.assertEquals(responsePut.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        ResultDto resultDto = utils.convert.jsonToDto(responsePut, ResultDto.class);
        Assert.assertEquals(resultDto.getData().getGlobalValue().toString(), "GlobalValue(baseCase=99.9, z1=99.9, z2=99.9, pma=99.9, csm=99.9, oneItemCap=666, lineItemCap=666666, globalCap=null)");
    }

    @Test(groups = {"smoke", "workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "adminAccessTokenProvider")
    public void verifyHandlingCharges_GlobalValues_AreNull_WhenOneOfTheClusterValuesAreDifferent_Engine_V2500(String accessToken) throws IOException {
        //5100162 -> id=1 ,engine=V2500
        Response response = HandlingChargesUtil.getHandlingCharges(accessToken, ENGINE_V2500_QUOTATION_ID);

        HandlingChargesData handlingChargesData = response.as(ResultDto.class).getData();
        HandlingChargesUtil.updateClustersAndParts(handlingChargesData, 99.9, 99.9, 99.9, 99.9, 666, 666666);
        HandlingChargesUtil.updateClustersAndParts(handlingChargesData, 12.3, 34.5, 56.7, 78.9, "LPC Stator");
        String handlingChargesDataJson = new Gson().toJson(handlingChargesData);

        PutHandlingChargesRequest putHandlingChargesRequest = new PutHandlingChargesRequest(ENGINE_V2500_QUOTATION_ID, handlingChargesDataJson)
                .withBearerToken(accessToken);
        Response responsePut = putHandlingChargesRequest.callAPI();
        Assert.assertEquals(responsePut.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        ResultDto resultDto = utils.convert.jsonToDto(responsePut, ResultDto.class);
        Assert.assertEquals(resultDto.getData().getGlobalValue().toString(), "GlobalValue(baseCase=null, z1=null, z2=null, pma=null, csm=null, oneItemCap=666, lineItemCap=666666, globalCap=null)");
    }

    @Test(groups = "workscopeFixedPriceAndNte", dataProvider = "adminAccessTokenProvider")
    public void verifyHandlingCharges_GlobalValues_AreNull_WhenOneOfTheClusterValuesAreDifferent_Engine_V2500_2(String accessToken) throws IOException {
        //5100162 -> id=1 ,engine=V2500
        Response response = HandlingChargesUtil.getHandlingCharges(accessToken, ENGINE_V2500_QUOTATION_ID);

        HandlingChargesData handlingChargesData = response.as(ResultDto.class).getData();
        HandlingChargesUtil.updateClustersAndParts(handlingChargesData, 99.9, 99.9, 99.9, 99.9, 666, 666666);
        HandlingChargesUtil.updateClustersAndPartsValues(handlingChargesData, OTHER);
        String handlingChargesDataJson = new Gson().toJson(handlingChargesData);

        PutHandlingChargesRequest putHandlingChargesRequest = new PutHandlingChargesRequest(ENGINE_V2500_QUOTATION_ID, handlingChargesDataJson)
                .withBearerToken(accessToken);
        Response responsePut = putHandlingChargesRequest.callAPI();
        Assert.assertEquals(responsePut.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        ResultDto resultDto = utils.convert.jsonToDto(responsePut, ResultDto.class);
        Assert.assertEquals(resultDto.getData().getGlobalValue().toString(), "GlobalValue(baseCase=null, z1=null, z2=null, pma=null, csm=null, oneItemCap=666, lineItemCap=666666, globalCap=null)");
    }

    @Test(groups = "workscopeFixedPriceAndNte", dataProvider = "adminAccessTokenProvider")
    public void verifyHandlingCharges_InvalidIntegerValueOver100_Integer_Engine_V2500(String accessToken) {
        //5100162 -> id=1 ,engine=V2500 BUG_COCA-802/COCA-802
        Response response = HandlingChargesUtil.getHandlingCharges(accessToken, ENGINE_V2500_QUOTATION_ID);

        HandlingChargesData handlingChargesData = response.as(ResultDto.class).getData();
        HandlingChargesUtil.updateClustersAndParts(handlingChargesData, 1000.0, 1000.0, 1000.0, 1000.0, 1000, 100000);

        String handlingChargesDataJson = new Gson().toJson(handlingChargesData);

        PutHandlingChargesRequest putHandlingChargesRequest = new PutHandlingChargesRequest(ENGINE_V2500_QUOTATION_ID, handlingChargesDataJson)
                .withBearerToken(accessToken);
        Response responsePut = putHandlingChargesRequest.callAPI();
        Assert.assertEquals(responsePut.statusCode(), RESPONSE_STATUS_CODE_400, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = "workscopeFixedPriceAndNte", dataProvider = "adminAccessTokenProvider")
    public void verifyHandlingCharges_InvalidDoubleValueOver100_Double_Engine_V2500(String accessToken) {
        //5100162 -> id=1 ,engine=V2500 BUG_COCA-802/COCA-802
        Response response = HandlingChargesUtil.getHandlingCharges(accessToken, ENGINE_V2500_QUOTATION_ID);

        HandlingChargesData handlingChargesData = response.as(ResultDto.class).getData();
        HandlingChargesUtil.updateClustersAndParts(handlingChargesData, 100.999, 100.999, 100.999, 100.999, 1000, 100000);

        String handlingChargesDataJson = new Gson().toJson(handlingChargesData);

        PutHandlingChargesRequest putHandlingChargesRequest = new PutHandlingChargesRequest(ENGINE_V2500_QUOTATION_ID, handlingChargesDataJson)
                .withBearerToken(accessToken);
        Response responsePut = putHandlingChargesRequest.callAPI();
        Assert.assertEquals(responsePut.statusCode(), RESPONSE_STATUS_CODE_400, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = "workscopeFixedPriceAndNte", dataProvider = "adminAccessTokenProvider")
    public void verifyHandlingCharges_Invalid_MinusValue_Engine_V2500(String accessToken) {
        //5100162 -> id=1 ,engine=V2500
        Response response = HandlingChargesUtil.getHandlingCharges(accessToken, ENGINE_V2500_QUOTATION_ID);

        HandlingChargesData handlingChargesData = response.as(ResultDto.class).getData();
        HandlingChargesUtil.updateClustersAndParts(handlingChargesData, -10.9, -10.9, -10.9, -10.9, 1000, 100000);

        String handlingChargesDataJson = new Gson().toJson(handlingChargesData);

        PutHandlingChargesRequest putHandlingChargesRequest = new PutHandlingChargesRequest(ENGINE_V2500_QUOTATION_ID, handlingChargesDataJson)
                .withBearerToken(accessToken);
        Response responsePut = putHandlingChargesRequest.callAPI();
        Assert.assertEquals(responsePut.statusCode(), RESPONSE_STATUS_CODE_400, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = "workscopeFixedPriceAndNte", dataProvider = "adminAccessTokenProvider")
    public void verifyHandlingCharges_Invalid_MinusValue_OneItemCap_Engine_V2500(String accessToken) {
        //5100162 -> id=1 ,engine=V2500
        Response response = HandlingChargesUtil.getHandlingCharges(accessToken, ENGINE_V2500_QUOTATION_ID);

        HandlingChargesData handlingChargesData = response.as(ResultDto.class).getData();
        HandlingChargesUtil.updateClustersAndParts(handlingChargesData, 0.0, 0.1, 1.0, 99.9, -1000, 100000);

        String handlingChargesDataJson = new Gson().toJson(handlingChargesData);

        PutHandlingChargesRequest putHandlingChargesRequest = new PutHandlingChargesRequest(ENGINE_V2500_QUOTATION_ID, handlingChargesDataJson)
                .withBearerToken(accessToken);
        Response responsePut = putHandlingChargesRequest.callAPI();
        Assert.assertEquals(responsePut.statusCode(), RESPONSE_STATUS_CODE_400, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
        Assert.assertTrue((responsePut.jsonPath().get(ERROR_DETAIL)).toString().contains(INVALID_REQUEST_CONTENT), RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "adminAccessTokenProvider")
    public void verifyHandlingCharges_Invalid_MinusValue_LineItemCap_Engine_V2500(String accessToken) {
        //5100162 -> id=1 ,engine=V2500
        Response response = HandlingChargesUtil.getHandlingCharges(accessToken, ENGINE_V2500_QUOTATION_ID);

        HandlingChargesData handlingChargesData = response.as(ResultDto.class).getData();
        HandlingChargesUtil.updateClustersAndParts(handlingChargesData, 0.0, 0.1, 1.0, 99.9, 1000, -100000);

        String handlingChargesDataJson = new Gson().toJson(handlingChargesData);

        PutHandlingChargesRequest putHandlingChargesRequest = new PutHandlingChargesRequest(ENGINE_V2500_QUOTATION_ID, handlingChargesDataJson)
                .withBearerToken(accessToken);
        Response responsePut = putHandlingChargesRequest.callAPI();
        Assert.assertEquals(responsePut.statusCode(), RESPONSE_STATUS_CODE_400, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
        Assert.assertTrue((responsePut.jsonPath().get(ERROR_DETAIL)).toString().contains(INVALID_REQUEST_CONTENT), RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = "workscopeFixedPriceAndNte", dataProvider = "adminAccessTokenProvider")
    public void verifyHandlingCharges_MaxIntegerValue_ItemCaps_Engine_V2500(String accessToken) {
        //5100162 -> id=1 ,engine=V2500
        Response response = HandlingChargesUtil.getHandlingCharges(accessToken, ENGINE_V2500_QUOTATION_ID);

        HandlingChargesData handlingChargesData = response.as(ResultDto.class).getData();
        HandlingChargesUtil.updateClustersAndParts(handlingChargesData, 0.0, 0.1, 1.0, 99.9, **********, **********);

        String handlingChargesDataJson = new Gson().toJson(handlingChargesData);

        PutHandlingChargesRequest putHandlingChargesRequest = new PutHandlingChargesRequest(ENGINE_V2500_QUOTATION_ID, handlingChargesDataJson)
                .withBearerToken(accessToken);
        Response responsePut = putHandlingChargesRequest.callAPI();
        Assert.assertEquals(responsePut.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = "workscopeFixedPriceAndNte", dataProvider = "adminAccessTokenProvider")
    public void verifyHandlingCharges_LPC_Blade_GlobalIndividualValuesAreNull_Engine_V2500(String accessToken) {
        //5100162 -> id=1 ,engine=V2500
        Response response = HandlingChargesUtil.getHandlingCharges(accessToken, ENGINE_V2500_QUOTATION_ID);

        HandlingChargesData handlingChargesData = response.as(ResultDto.class).getData();
        HandlingChargesUtil.updateClustersAndPartsValues(handlingChargesData, "LPC Blades");
        String handlingChargesDataJson = new Gson().toJson(handlingChargesData);

        PutHandlingChargesRequest putHandlingChargesRequest = new PutHandlingChargesRequest(ENGINE_V2500_QUOTATION_ID, handlingChargesDataJson)
                .withBearerToken(accessToken);
        Response responsePut = putHandlingChargesRequest.callAPI();
        Assert.assertEquals(responsePut.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
        Assert.assertNull((responsePut.getBody().jsonPath().get("data.clusters[1].z1")));
        Assert.assertNull((responsePut.getBody().jsonPath().get("data.clusters[1].z2")));
        Assert.assertNull((responsePut.getBody().jsonPath().get("data.clusters[1].pma")));
        Assert.assertNull((responsePut.getBody().jsonPath().get("data.clusters[1].csm")));
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "adminAccessTokenProvider")
    public void verifyHandlingCharges_LPC_Blade_GlobalIndividualValuesA_Engine_V2500(String accessToken) {
        //5100162 -> id=1 ,engine=V2500
        Response response = HandlingChargesUtil.getHandlingCharges(accessToken, ENGINE_V2500_QUOTATION_ID);

        HandlingChargesData handlingChargesData = response.as(ResultDto.class).getData();
        HandlingChargesUtil.updateClustersAndParts(handlingChargesData, 99.9, 99.9, 99.9, 99.9, 666, 666666);
        HandlingChargesUtil.updateClustersAndParts(handlingChargesData, 11.9, 22.9, 33.9, 44.9, "LPC Blades", "M2L - Blade LPC Stg.  1.5");
        String handlingChargesDataJson = new Gson().toJson(handlingChargesData);

        PutHandlingChargesRequest putHandlingChargesRequest = new PutHandlingChargesRequest(ENGINE_V2500_QUOTATION_ID, handlingChargesDataJson)
                .withBearerToken(accessToken);
        Response responsePut = putHandlingChargesRequest.callAPI();
        Assert.assertEquals(responsePut.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
        Assert.assertNull((responsePut.getBody().jsonPath().get("data.clusters[1].z1")));
        Assert.assertNull((responsePut.getBody().jsonPath().get("data.clusters[1].z2")));
        Assert.assertNull((responsePut.getBody().jsonPath().get("data.clusters[1].pma")));
        Assert.assertNull((responsePut.getBody().jsonPath().get("data.clusters[1].csm")));

        Assert.assertEquals(responsePut.getBody().jsonPath().get("data.clusters[1].parts[0].z1").toString(), "11.9");
        Assert.assertEquals(responsePut.getBody().jsonPath().get("data.clusters[1].parts[0].z2").toString(), "22.9");
        Assert.assertEquals(responsePut.getBody().jsonPath().get("data.clusters[1].parts[0].pma").toString(), "33.9");
        Assert.assertEquals(responsePut.getBody().jsonPath().get("data.clusters[1].parts[0].csm").toString(), "44.9");
    }

    @Test(groups = "workscopeFixedPriceAndNte")
    public void verifyHandlingCharges_ViewOnlyUser_CannotEditValue_Engine_V2500() {
        //5100162 -> id=1 ,engine=V2500
        String viewOnlyToken = utils.context.getContextItem(VIEW_ONLY_TOKEN);
        Response response = HandlingChargesUtil.getHandlingCharges(viewOnlyToken, ENGINE_V2500_QUOTATION_ID);

        HandlingChargesData handlingChargesData = response.as(ResultDto.class).getData();
        HandlingChargesUtil.updateClustersAndParts(handlingChargesData, 99.9, 99.9, 99.9, 99.9, 666, 666666);
        HandlingChargesUtil.updateClustersAndPartsValues(handlingChargesData, OTHER);
        String handlingChargesDataJson = new Gson().toJson(handlingChargesData);

        PutHandlingChargesRequest putHandlingChargesRequest = new PutHandlingChargesRequest(ENGINE_V2500_QUOTATION_ID, handlingChargesDataJson)
                .withBearerToken(viewOnlyToken);
        Response responsePut = putHandlingChargesRequest.callAPI();
        Assert.assertEquals(responsePut.statusCode(), RESPONSE_STATUS_CODE_400, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
        Assert.assertTrue(responsePut.getBody().jsonPath().get(ERROR_DETAIL).toString().contains(MessageFormater.viewOnlyUserCannotModifyData(TestUser.VIEW_ONLY,ENGINE_V2500_QUOTATION_ID)), GENERIC_ERROR_MESSAGE_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = "workscopeFixedPriceAndNte")
    public void verifyHandlingCharges_TestUserNotOwnerOfQuotation_CannotEditValue_Engine_V2500() {
        //5100162 -> id=1 ,engine=V2500
        String user1Token = utils.context.getContextItem(USER_1_TOKEN);
        Response response = HandlingChargesUtil.getHandlingCharges(user1Token, ENGINE_V2500_QUOTATION_ID);

        HandlingChargesData handlingChargesData = response.as(ResultDto.class).getData();
        HandlingChargesUtil.updateClustersAndParts(handlingChargesData, 99.9, 99.9, 99.9, 99.9, 666, 666666);
        HandlingChargesUtil.updateClustersAndPartsValues(handlingChargesData, OTHER);
        String handlingChargesDataJson = new Gson().toJson(handlingChargesData);

        PutHandlingChargesRequest putHandlingChargesRequest = new PutHandlingChargesRequest(ENGINE_V2500_QUOTATION_ID, handlingChargesDataJson)
                .withBearerToken(user1Token);
        Response responsePut = putHandlingChargesRequest.callAPI();
        Assert.assertEquals(responsePut.statusCode(), RESPONSE_STATUS_CODE_403, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
        Assert.assertTrue(responsePut.getBody().jsonPath().get(ERROR_DETAIL).toString().contains(QUOTATION_OWNERSHIP_CHANGED), GENERIC_ERROR_MESSAGE_MISMATCH_AS_MESSAGE);
    }
}
