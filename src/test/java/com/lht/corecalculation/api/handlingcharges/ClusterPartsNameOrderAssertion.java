package com.lht.corecalculation.api.handlingcharges;

import com.lht.corecalculation.api.pojo.dto.handlingcharges.Cluster;
import com.lht.corecalculation.api.pojo.dto.handlingcharges.Part;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Stream;
import org.testng.Assert;


import static com.lht.corecalculation.api.constants.GlobalConstants.CLUSTER_PART_ORDER_MISMATCH_AS_MESSAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.COMPONENTS;
import static com.lht.corecalculation.api.constants.GlobalConstants.KITS;
import static com.lht.corecalculation.api.constants.GlobalConstants.PARTS_PACKAGES;
import static com.lht.corecalculation.api.constants.GlobalConstants.PART_TYPE_COMPONENT;
import static com.lht.corecalculation.api.constants.GlobalConstants.PART_TYPE_KIT;
import static com.lht.corecalculation.api.constants.GlobalConstants.PART_TYPE_NON_ROUTINE_MATERIAL;
import static com.lht.corecalculation.api.constants.GlobalConstants.PART_TYPE_PARTS_PACKAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.PART_TYPE_ROUTINE_MATERIAL;

public class ClusterPartsNameOrderAssertion {

    private static final String[] CLUSTER_PARTS_ORDER_V2500 = {
            "M1 - Blade LPC Stg.  1 (Fan Blade)",
            "M2L - Blade LPC Stg.  1.5",
            "M2L - Blade LPC Stg.  2.0",
            "M2L - Blade LPC Stg.  2.3",
            "M2L - Blade LPC Stg.  2.5",
            "M2L - Stator LPC Stg.  1.5",
            "M2L - Stator LPC Stg.  2.0",
            "M2L - Stator LPC Stg.  2.3",
            "M2L - Stator LPC Stg.  2.5",
            "M2S - Fan Exit Guide Vane",
            "M3R - Blade HPC Stg.  3",
            "M3R - Blade HPC Stg.  4",
            "M3R - Blade HPC Stg.  5",
            "M3R - Blade HPC Stg.  6",
            "M3R - Blade HPC Stg.  7",
            "M3R - Blade HPC Stg.  8",
            "M3R - Blade HPC Stg.  9",
            "M3R - Blade HPC Stg. 10",
            "M3R - Blade HPC Stg. 11",
            "M3R - Blade HPC Stg. 12",
            "M3S - Vane HPC IGV",
            "M3S - Vane HPC Stg.  3",
            "M3S - Vane HPC Stg.  4",
            "M3S - Vane HPC Stg.  5",
            "M3S - Vane HPC Stg.  6",
            "M3S - Vane HPC Stg.  7",
            "M3S - Vane HPC Stg.  8",
            "M3S - Vane HPC Stg.  9",
            "M3S - Vane HPC Stg. 10",
            "M3S - Vane HPC Stg. 11",
            "M6 - Vane HPT Stg.  1",
            "M7S - Vane HPT Stg.  2 Cluster",
            "M7R - Blade HPT Stg.  1",
            "M7R - Blade HPT Stg.  2",
            "M7S - Duct Seg. HPT Stg.  1",
            "M7S - Duct Seg. HPT Stg.  2",
            "M8R - Blade LPT Stg.  3",
            "M8R - Blade LPT Stg.  4",
            "M8R - Blade LPT Stg.  5",
            "M8R - Blade LPT Stg.  6",
            "M8R - Blade LPT Stg.  7",
            "M8S - Vane LPT Stg.  3",
            "M8S - Vane LPT Stg.  4",
            "M8S - Vane LPT Stg.  5",
            "M8S - Vane LPT Stg.  6",
            "M8S - Vane LPT Stg.  7",
            "M5 - Comb. Chamber Inner Liner",
            "M5 - Comb. Chamber Outer Liner",
            "M2I - Bearing No.  1",
            "M2I - Bearing No.  2R Outer",
            "M2I - Bearing No.  3B",
            "M4B - Bearing No.  4R",
            "M9 - Bearing No.  5",
            "M2L - Front Case LPC",
            "M2L - Mid Case LPC",
            "M2L - Rear Case LPC",
            "M2S - Fan Case",
            "M2S - Fan Frame",
            "M3S - Front Case HPC",
            "M3S - Rear Case HPC",
            "M4 - Diffusor Case",
            "M7S - Case HPT",
            "M8S - Case LPT",
            "M9 - Turbine Exhaust Case",
            "M1 - Fan Disc Stg.  1",
            "M2I - Stub Shaft",
            "M2L - Disc LPC Stg.  1.5-2.5",
            "M3R - Disc HPC Stg.  3-8",
            "M3R - Disc HPC Stg.  9-12",
            "M3R - Rear Shaft HPC",
            "M3R - Rot. Airseal HPC",
            "M7R - Blade Ret. Plate HPT Stg.  2",
            "M7R - Hub HPT Stg.  1",
            "M7R - Hub HPT Stg.  2",
            "M7R - Inner Airseal HPT Stg.  1",
            "M7R - Outer Airseal HPT Stg.  1",
            "M7R - Rot. Airseal HPT Stg.  2",
            "M8R - Disc Stg.  3",
            "M8R - Disc Stg.  4",
            "M8R - Disc Stg.  5",
            "M8R - Disc Stg.  6",
            "M8R - Disc Stg.  7",
            "M8R - Inner Airseal LPT Stg.  6",
            "M8R - Rot. Airseal LPT Stg.  3",
            "M8R - Rot. Airseal LPT Stg.  4",
            "M8R - Rot. Airseal LPT Stg.  5",
            "M8R - Rot. Airseal LPT Stg.  6",
            "M8R - Rot. Airseal LPT Stg.  7",
            "M8R - Shaft LPT",
            "QEC - Engine Aft. Mount",
            "QEC - Engine Fwd. Mount",
            "M2S - Acoustic Liner",
            "M2S - Anti Ice Impact Panel",
            "M2S - Anti Ice Impact Panel (adj. pc)",
            "M2S - Attrition Liner",
            "M2S - Front Liner Panel",
            "M2S - Rear Liner Panel",
            "M2S - Seal Rubber Panel",
            "M4 - Bearing No.  4 Scavenge Tube Assy",
            "M4 - Vane HPC Stg. 12 (OGV)",
            "M4B - Bearing No.  4 Face Seal",
            "M5 - Comb. Chamber Inner Liner Seg. Stg.  1",
            "M5 - Comb. Chamber Inner Liner Seg. Stg.  2",
            "M5 - Comb. Chamber Inner Liner Seg. Stg.  3",
            "M5 - Comb. Chamber Inner Liner Seg. Stg.  4",
            "M5 - Comb. Chamber Inner Liner Seg. Stg.  5",
            "M5 - Comb. Chamber Outer Liner Bulkhead Seg.",
            "M5 - Comb. Chamber Outer Liner Seg. Stg.  1",
            "M5 - Comb. Chamber Outer Liner Seg. Stg.  2",
            "M5 - Comb. Chamber Outer Liner Seg. Stg.  3",
            "M5 - Comb. Chamber Outer Liner Seg. Stg.  4",
            "M5 - Comb. Chamber Outer Liner Seg. Stg.  5",
            "M5 - Fuel Nozzle Guide",
            "M6 - Cooling Duct HPT Stg.  1",
            "M6 - Support Assy HPT Stg.  1",
            "M8S - Inner Duct Seg. Stg.  3",
            "M8S - Support Ring Assy"
    };

    private static final String[] CLUSTER_PARTS_ORDER_CFM56_5B = {
            "M1 - Blade LPC Stg.  1 (Fan Blade)",
            "M1 - Blade LPC Stg.  2",
            "M1 - Blade LPC Stg.  3",
            "M1 - Blade LPC Stg.  4",
            "M1 - Blade LPC Stg.  5",
            "M1 - Vane LPC Stg.  1",
            "M1 - Vane LPC Stg.  2",
            "M1 - Vane LPC Stg.  3",
            "M1 - Vane LPC Stg.  4",
            "M1 - Vane LPC Stg.  5",
            "M2S - Vane LPC OGV",
            "M3R - Blade HPC Stg.  1",
            "M3R - Blade HPC Stg.  2",
            "M3R - Blade HPC Stg.  3",
            "M3R - Blade HPC Stg.  4",
            "M3R - Blade HPC Stg.  5",
            "M3R - Blade HPC Stg.  6",
            "M3R - Blade HPC Stg.  7",
            "M3R - Blade HPC Stg.  8",
            "M3R - Blade HPC Stg.  9",
            "M3R - Blade HPC Stg.  1 (TI/PIP/EVO)",
            "M3R - Blade HPC Stg.  2 (TI/PIP/EVO)",
            "M3R - Blade HPC Stg.  3 (TI/PIP/EVO)",
            "M3R - Blade HPC Stg.  4 (TI/PIP/EVO)",
            "M3R - Blade HPC Stg.  5 (TI/PIP/EVO)",
            "M3R - Blade HPC Stg.  6 (TI/PIP/EVO)",
            "M3R - Blade HPC Stg.  7 (TI/PIP/EVO)",
            "M3R - Blade HPC Stg.  8 (TI/PIP/EVO)",
            "M3R - Blade HPC Stg.  9 (TI/PIP/EVO)",
            "M3S - Vane HPC IGV",
            "M3S - Vane HPC Stg.  1",
            "M3S - Vane HPC Stg.  2",
            "M3S - Vane HPC Stg.  3",
            "M3S - Vane HPC Stg.  4",
            "M3S - Vane HPC Stg.  5",
            "M3S - Vane HPC Stg.  6",
            "M3S - Vane HPC Stg.  7",
            "M3S - Vane HPC Stg.  8",
            "M3S - Vane HPC Stg.  4 (TI/PIP/EVO)",
            "M3S - Vane HPC Stg.  5 (TI/PIP/EVO)",
            "M3S - Vane HPC Stg.  6 (TI/PIP/EVO)",
            "M3S - Vane HPC Stg.  7 (TI/PIP/EVO)",
            "M3S - Vane HPC Stg.  8 (TI/PIP/EVO)",
            "M6 - Nozzle HPT",
            "M7 - Blade HPT Stg.  1",
            "M7 - Blade HPT Stg.  1 (-7B Evo / -5B PIP)",
            "M8 - Shroud HPT",
            "M8 - Shroud HPT (TI/PIP/EVO)",
            "M9R - Blade LPT Stg.  1",
            "M9R - Blade LPT Stg.  2",
            "M9R - Blade LPT Stg.  3",
            "M9R - Blade LPT Stg.  4",
            "M8 - Nozzle Seg. LPT Stg.  1",
            "M8 - Nozzle Seg. LPT Stg.  1 (TI/PIP/EVO)",
            "M9S - Nozzle Seg. LPT Stg.  2",
            "M9S - Nozzle Seg. LPT Stg.  3",
            "M9S - Nozzle Seg. LPT Stg.  4",
            "M9S - Outer Airseal Seg. Stg.  1",
            "M9S - Outer Airseal Seg. Stg.  2",
            "M9S - Outer Airseal Seg. Stg.  3",
            "M9S - Outer Airseal Seg. Stg.  4",
            "M5 - Comb. Chamber Inner Liner",
            "M5 - Comb. Chamber Outer Liner",
            "M2B - Bearing No.  1",
            "M2B - Bearing No.  2R",
            "M2I - Bearing No.  3B",
            "M2I - Bearing No.  3R",
            "M9B - Bearing No.  4R",
            "M9B - Bearing No.  5R",
            "QEC - Engine Aft. Mount",
            "QEC - Engine Fwd. Mount",
            "M1 - Booster Spool",
            "M1 - Fan Disc Stg.  1",
            "M2B - Fan Shaft",
            "M3R - Disc HPC Stg.  3",
            "M3R - Fwd. Shaft HPC",
            "M3R - Rot. Air Seal (CDP)",
            "M3R - Spool HPC Stg.  1-2",
            "M3R - Spool HPC Stg.  4-9",
            "M7 - Disc HPT",
            "M7 - Front Rot. Airseal HPT",
            "M7 - Front Shaft HPT",
            "M7 - Rear Shaft HPT",
            "M9B - Shaft LPT",
            "M9R - Conical Support",
            "M9R - Disc LPT Stg.  1",
            "M9R - Disc LPT Stg.  2",
            "M9R - Disc LPT Stg.  3",
            "M9R - Disc LPT Stg.  4",
            "M9S - Case LPT",
            "M2S - Downstream Fan Case",
            "M2S - Fan Frame",
            "M2S - Upstream Fan Case",
            "M3S - Front Case HPC",
            "M3S - Rear Case HPC",
            "M4 - Combustion Case",
            "M6 - Fwd. Inner Nozzle Support HPT",
            "M9T - Turbine Rear Frame",
            "M2S - Fan Frame (TI/PIP/EVO)",
            "M3S - Front Case HPC (TI/PIP/EVO)",
            "M2I - Horizontal Gearbevel",
            "M2S - Abradable Lining Repair",
            "M2S - Bearing No.  3 Stat. Seal",
            "M4 - Stat. Inner Seal HPT",
            "M5 - Comb. Chamber Dome Assy",
            "M5 - Comb. Chamber Inner Cowl",
            "M5 - Comb. Chamber Outer Cowl",
            "M6 - Stat. Outer Seal HPT",
            "M7 - Blade Retainer HPT",
            "M7 - Rear Rot. Airseal HPT",
            "M8 - Air Manifold HPT",
            "M8 - Shroud Hanger HPT",
            "M8 - Shroud/Nozzle Support",
            "M9B - Center Vent Tube",
            "M9B - Oil/Air Separator",
            "M9R - Fwd. Rot. Airseal LPT",
            "M9S - Cooling Manifold",
            "M9T - Bearing No.  5 Support",
            "AGB - Drive Seals",
            "AGB - Drive Pads",
            "AGB - Bearings",
            "TGB - Radial Drive Shaft",
            "M9R - Rot. Airseal LPT Stg.  2",
            "M9R - Rot. Airseal LPT Stg.  3",
            "M9R - Rot. Airseal LPT Stg.  4",
            "M1 - Spinner Rear Cone",
            "M2S - Fan Frame Stat. Inner Shroud",
            "M2B - Bearing No. 1 Stat. Seal",
            "M2I - Locking Nut Bearing No. 3",
            "TGB - Main Housing",
            "AGB - Main Housing",
            "M6 - After Inner Nozzle Support",
            "M7 - Blade Retainer HPT (PIP/EVO)",
            "M8 - Front Flange",
            "M9T - Oil Inlet Cover"
    };

    private static final String[] CLUSTER_PARTS_ORDER_LEAP_1B = {
            "SM21 - Blade LPC Stg 1 (Fan Blade)",
            "SM21 - Blade LPC Stg. 2",
            "SM21 - Blade LPC Stg. 3",
            "SM21 - Blade LPC Stg. 4",
            "SM21 - Vane LPC Stg. 1",
            "SM21 - Vane LPC Stg. 2",
            "SM21 - Vane LPC Stg. 3",
            "SM24 - Vane LPC Stg. 4",
            "SM24 - Vane LPC OGV",
            "SM24 - Vane HPC IGV",
            "SM30 - Vane HPC Stg. 5",
            "SM30 - Vane HPC Stg. 6",
            "SM30 - Vane HPC Stg. 7",
            "SM30 - Vane HPC Stg. 8",
            "SM30 - Vane HPC Stg. 9",
            "SM32 - Vane HPC Stg. 1",
            "SM32 - Vane HPC Stg. 2",
            "SM32 - Vane HPC Stg. 3",
            "SM32 - Vane HPC Stg. 4",
            "SM31 - Blade HPC Stg. 10",
            "SM31 - Blade HPC Stg. 6",
            "SM31 - Blade HPC Stg. 7",
            "SM31 - Blade HPC Stg. 8",
            "SM31 - Blade HPC Stg. 9",
            "SM42 - Comb. Chamber Inner Liner",
            "SM42 - Comb. Chamber Outer Liner",
            "SM42 - Comb. Chamber Dome Assy",
            "SM51 - Nozzle HPT Stg. 1",
            "SM53 - Nozzle HPT Stg. 2",
            "SM52 - Blade HPT Stg. 1",
            "SM52 - Blade HPT Stg. 2",
            "SM53 - Shroud HPT Stg. 1",
            "SM53 - Shroud HPT Stg. 2",
            "SM56 - Nozzle Seg. LPT Stg. 1",
            "SM58 - Blade LPT Stg. 1",
            "SM58 - Blade LPT Stg. 2",
            "SM58 - Blade LPT Stg. 3",
            "SM58 - Blade LPT Stg. 4",
            "SM58 - Blade LPT Stg. 5",
            "SM58 - Nozzle Seg. LPT Stg. 2",
            "SM58 - Nozzle Seg. LPT Stg. 3",
            "SM58 - Nozzle Seg. LPT Stg. 4",
            "SM58 - Nozzle Seg. LPT Stg. 5",
            "SM58 - Shroud Seg. LPT Stg. 1",
            "SM58 - Shroud Seg. LPT Stg. 2",
            "SM58 - Shroud Seg. LPT Stg. 3",
            "SM58 - Shroud Seg. LPT Stg. 4",
            "SM58 - Shroud Seg. LPT Stg. 5",
            "SM22 - Bearing No. 1R",
            "SM22 - Bearing No. 2B",
            "SM54 - Bearing No. 4R",
            "SM61 - Bearing No. 3B",
            "SM61 - Bearing No. 3R",
            "SM23 - Fan Case",
            "SM30 - HPC Aft. Extension Case",
            "SM30 - HPC Aft. Inner Case Stg. 5",
            "SM30 - HPC Aft. Inner Case Stg. 5-6",
            "SM30 - HPC Aft. Inner Case Stg. 6-7",
            "SM30 - HPC Aft. Inner Case Stg. 7",
            "SM30 - HPC Aft. Inner Case Stg. 8",
            "SM30 - HPC Aft. Inner Case Stg. 8-9",
            "SM30 - HPC Aft. Inner Wishbone Case",
            "SM32 - HPC Forward Case",
            "SM54 - Turbine Center Frame",
            "SM58 - LPT Case",
            "SM59 - Turbine Rear Frame",
            "SM21 - Booster Spool",
            "SM21 - Disk LPC Stg. 1 (Fan Disk)",
            "SM22 - LPC Shaft",
            "SM31 - Blisk HPC Stg. 1",
            "SM31 - Blisk HPC Stg. 2",
            "SM31 - Blisk HPC Stg. 3-4",
            "SM31 - Blisk HPC Stg. 5",
            "SM31 - Impeller Tube Support",
            "SM31 - Rot. Airseal (CDP)",
            "SM31 - Spool HPC Stg. 6-10",
            "SM41 - Combustor Case",
            "SM52 - Aft. Rot. Airseal HPT",
            "SM52 - Disk HPT Stg. 1",
            "SM52 - Disk HPT Stg. 2",
            "SM52 - Fwd. Outer Seal",
            "SM52 - Mid Seal HPT Rotor",
            "SM53 - HPT Case",
            "SM57 - LPT Shaft",
            "SM58 - Disk LPT Stg. 1",
            "SM58 - Disk LPT Stg. 2",
            "SM58 - Disk LPT Stg. 3",
            "SM58 - Disk LPT Stg. 4",
            "SM58 - Disk LPT Stg. 5",
            "SM22 - Bearing No. 1R Support",
            "SM24 - Fan Frame Hub",
            "SM24 - Fan Frame Shroud",
            "SM24 - Shroud HPC IGV",
            "SM24 - Vane LPC OGV Strut",
            "SM41 - Fwd. Combustor Inner Manifold",
            "SM41 - Stat. Airseal (CDP)",
            "SM42 - Inner Support",
            "SM42 - Outer Support",
            "SM42 - Radial Mixer",
            "SM51 - Front In. Nozzle Sup. HPT",
            "SM51 - Outer Stat. Seal HPT",
            "SM52 - Air Duct HPT",
            "SM52 - Blade Retainer HPT Stg. 1",
            "SM52 - Blade Retainer HPT Stg. 2",
            "SM52 - Coupling Nut HPT Rotor",
            "SM53 - Nozzle HPT Stg. 2 Impingement Ring",
            "SM53 - Shroud Hanger HPT Stg. 1",
            "SM53 - Shroud Hanger HPT Stg. 2",
            "SM54 - Bearing No. 4R Support",
            "SM54 - Stat. Seal HPT Stator",
            "SM57 - Front Vent. Tube",
            "SM58 - Cooling Manifold",
            "SM58 - Rear Vent Tube",
            "SM58 - Rot. Ring LPT Stg. 2",
            "SM58 - Rot. Ring LPT Stg. 3",
            "SM58 - Rot. Ring LPT Stg. 4",
            "SM58 - Rot. Ring LPT Stg. 5",
//            "SM55 - Bearing No. 5R Support",
//            "SM61 - Horizontal Bevel Gear",
            "SM62 - Inner Radial Drive Shaft",
            "SM62 - Outer Radial Drive Shaft",
            "SM62 - Transfer Shaft",
            "SM55 - Bearing No. 5R Outer Ring",
            "SM57 - Bearing No. 5R Inner Ring",
//            "SM59 - Bearing No. 5R Support", // new part
            "SM62 - Horizontal Bevel Gear", // new part
    };

    private static final String[] CLUSTER_PARTS_ORDER_LEAP_1A = {
            "SM21 - Blade LPC Stg 1 (Fan Blade)",
            "SM21 - Blade LPC Stg. 2",
            "SM21 - Blade LPC Stg. 3",
            "SM21 - Blade LPC Stg. 4",
            "SM21 - Vane LPC Stg. 1",
            "SM21 - Vane LPC Stg. 2",
            "SM21 - Vane LPC Stg. 3",
            "SM24 - Vane LPC Stg. 4",
            "SM24 - Vane LPC OGV",
            "SM24 - Vane HPC IGV",
            "SM30 - Vane HPC Stg. 5",
            "SM30 - Vane HPC Stg. 6",
            "SM30 - Vane HPC Stg. 7",
            "SM30 - Vane HPC Stg. 8",
            "SM30 - Vane HPC Stg. 9",
            "SM32 - Vane HPC Stg. 1",
            "SM32 - Vane HPC Stg. 2",
            "SM32 - Vane HPC Stg. 3",
            "SM32 - Vane HPC Stg. 4",
            "SM31 - Blade HPC Stg. 10",
            "SM31 - Blade HPC Stg. 6",
            "SM31 - Blade HPC Stg. 7",
            "SM31 - Blade HPC Stg. 8",
            "SM31 - Blade HPC Stg. 9",
            "SM32 - Shroud HPC Stg. 1",
            "SM32 - Shroud HPC Stg. 2",
            "SM32 - Shroud HPC Stg. 3",
            "SM32 - Shroud HPC Stg. 4",
            "SM42 - Comb. Chamber Inner Liner",
            "SM42 - Comb. Chamber Outer Liner",
            "SM42 - Comb. Chamber Dome Assy",
            "SM51 - Nozzle HPT Stg. 1",
            "SM53 - Nozzle HPT Stg. 2",
            "SM52 - Blade HPT Stg. 1",
            "SM52 - Blade HPT Stg. 2",
            "SM53 - Shroud HPT Stg. 1",
            "SM53 - Shroud HPT Stg. 2",
            "SM56 - Nozzle Seg. LPT Stg. 1",
            "SM58 - Blade LPT Stg. 1",
            "SM58 - Blade LPT Stg. 2",
            "SM58 - Blade LPT Stg. 3",
            "SM58 - Blade LPT Stg. 4",
            "SM58 - Blade LPT Stg. 5",
            "SM58 - Blade LPT Stg. 6",
            "SM58 - Blade LPT Stg. 7",
            "SM58 - Nozzle Seg. LPT Stg. 2",
            "SM58 - Nozzle Seg. LPT Stg. 3",
            "SM58 - Nozzle Seg. LPT Stg. 4",
            "SM58 - Nozzle Seg. LPT Stg. 5",
            "SM58 - Nozzle Seg. LPT Stg. 6",
            "SM58 - Nozzle Seg. LPT Stg. 7",
            "SM58 - Shroud Seg. LPT Stg. 1",
            "SM58 - Shroud Seg. LPT Stg. 2",
            "SM58 - Shroud Seg. LPT Stg. 3",
            "SM58 - Shroud Seg. LPT Stg. 4",
            "SM58 - Shroud Seg. LPT Stg. 5",
            "SM58 - Shroud Seg. LPT Stg. 6",
            "SM22 - Bearing No. 1R",
            "SM22 - Bearing No. 2B",
            "SM54 - Bearing No. 4R",
            "SM58 - Bearing No. 5R",
            "SM61 - Bearing No. 3B",
            "SM61 - Bearing No. 3R",
            "SM23 - Fan Case",
            "SM30 - HPC Aft. Extension Case",
            "SM30 - HPC Aft. Inner Case Stg. 5",
            "SM30 - HPC Aft. Inner Case Stg. 5-6",
            "SM30 - HPC Aft. Inner Case Stg. 6-7",
            "SM30 - HPC Aft. Inner Case Stg. 7",
            "SM30 - HPC Aft. Inner Case Stg. 8",
            "SM30 - HPC Aft. Inner Case Stg. 8-9",
            "SM30 - HPC Aft. Inner Wishbone Case",
            "SM32 - HPC Fwd. Extension Case",
            "SM54 - Turbine Center Frame",
            "SM58 - LPT Case",
            "SM59 - Turbine Rear Frame",
            "SM21 - Booster Spool",
            "SM21 - Disk LPC Stg. 1 (Fan Disk)",
            "SM22 - LPC Shaft",
            "SM31 - Blisk HPC Stg. 1",
            "SM31 - Blisk HPC Stg. 2",
            "SM31 - Blisk HPC Stg. 3-4",
            "SM31 - Blisk HPC Stg. 5",
            "SM31 - Impeller Tube Support",
            "SM31 - Rot. Airseal (CDP)",
            "SM31 - Spool HPC Stg. 6-10",
            "SM41 - Combustor Case",
            "SM52 - Aft. Rot. Airseal HPT",
            "SM52 - Disk HPT Stg. 1",
            "SM52 - Disk HPT Stg. 2",
            "SM52 - Fwd. Outer Seal",
            "SM52 - Interstage Seal HPT Rotor",
            "SM53 - HPT Case",
            "SM57 - LPT Shaft",
            "SM58 - Disk LPT Stg. 1",
            "SM58 - Disk LPT Stg. 2",
            "SM58 - Disk LPT Stg. 3",
            "SM58 - Disk LPT Stg. 4",
            "SM58 - Disk LPT Stg. 5",
            "SM58 - Spool LPT Stg. 6-7",
            "SM58 - Torque Cone LPT",
            "SM22 - Bearing No. 1R Support",
            "SM24 - Fan Frame Hub",
            "SM24 - Fan Frame Shroud",
            "SM24 - Shroud HPC IGV",
            "SM24 - Vane LPC OGV Strut",
            "SM41 - Fwd. Combustor Inner Manifold",
            "SM41 - Stat. Airseal (CDP)",
            "SM42 - Inner Support",
            "SM42 - Outer Support",
            "SM42 - Radial Mixer",
            "SM51 - Front In. Nozzle Sup. HPT",
            "SM51 - Outer Stat. Seal HPT",
            "SM52 - Air Duct HPT",
            "SM52 - Blade Retainer HPT Stg. 1",
            "SM52 - Blade Retainer HPT Stg. 2",
            "SM52 - Coupling Nut HPT Rotor",
            "SM53 - Nozzle HPT Stg. 2 Impingement Ring",
            "SM53 - Shroud Hanger HPT Stg. 1",
            "SM53 - Shroud Hanger HPT Stg. 2",
            "SM54 - Bearing No. 4R Support",
            "SM54 - Nozzle Retainer LPT Stg. 1",
            "SM54 - Stat. Seal HPT Stator",
            "SM57 - Front Vent. Tube",
            "SM58 - Nozzle Sup. LTP Stg. 7",
            "SM58 - Rear Vent. Tube",
            "SM58 - Rot. Ring LPT Stg. 2",
            "SM58 - Rot. Ring LPT Stg. 3",
            "SM58 - Rot. Ring LPT Stg. 4",
            "SM58 - Rot. Ring LPT Stg. 5",
            "SM58 - Rot. Ring LPT Stg. 6",
//            "SM55 - Bearing No. 5R Support",
            "SM59 - Bearing No. 5R Support",
            "SM61 - Horizontal Bevel Gear",
            "SM62 - Inner Radial Drive Shaft",
            "SM62 - Outer Radial Drive Shaft",
            "SM62 - Transfer Shaft",
    };

    public static void verifyPartOrder(List<Cluster> clusters, String[] clusterPartsOrder, String errorMessage) {
        int partIndex = 0;
        for (Cluster cluster : clusters) {
            List<Part> parts = cluster.getParts();
            for (Part part : parts) {
                // Skip validation for parts of excluded types
                if (!isExcludedPartType(String.valueOf(part.getType()))) {
                    if (partIndex >= clusterPartsOrder.length) {
                        Assert.fail(CLUSTER_PART_ORDER_MISMATCH_AS_MESSAGE);
                    }
                    Assert.assertEquals(part.getName(), clusterPartsOrder[partIndex], errorMessage + part.getName());
                    partIndex++;
                }
            }
        }
        // Ensure all parts were checked
        Assert.assertEquals(partIndex, clusterPartsOrder.length);
    }

    private static boolean isExcludedPartType(String partType) {
        return PART_TYPE_KIT.equals(partType) ||
                PART_TYPE_COMPONENT.equals(partType) ||
                PART_TYPE_PARTS_PACKAGE.equals(partType) ||
                PART_TYPE_ROUTINE_MATERIAL.equals(partType) ||
                PART_TYPE_NON_ROUTINE_MATERIAL.equals(partType);
    }

    public static void verifyPartOrder_Engine_V2500(List<Cluster> clusters) {
        verifyPartOrder(clusters, CLUSTER_PARTS_ORDER_V2500, CLUSTER_PART_ORDER_MISMATCH_AS_MESSAGE);
    }

    public static void verifyPartOrder_Engine_CFM56_5B(List<Cluster> clusters) {
        verifyPartOrder(clusters, CLUSTER_PARTS_ORDER_CFM56_5B, CLUSTER_PART_ORDER_MISMATCH_AS_MESSAGE);
    }

    public static void verifyPartOrder_Engine_LEAP_1B(List<Cluster> clusters) {
        verifyPartOrder(clusters, CLUSTER_PARTS_ORDER_LEAP_1B, CLUSTER_PART_ORDER_MISMATCH_AS_MESSAGE);
    }

    public static void verifyPartOrder_Engine_LEAP_1A(List<Cluster> clusters) {
        verifyPartOrder(clusters, CLUSTER_PARTS_ORDER_LEAP_1A, CLUSTER_PART_ORDER_MISMATCH_AS_MESSAGE);
    }

    public static String[] getZ2ClusterOrder_LEAP_1A() {
        return addStandardCategories(Arrays.stream(CLUSTER_PARTS_ORDER_LEAP_1A).toArray(String[]::new));
    }

    public static String[] getZ2ClusterOrder_LEAP_1B() {
        return addStandardCategories(Arrays.stream(CLUSTER_PARTS_ORDER_LEAP_1B).toArray(String[]::new));
    }

    public static String[] getZ2ClusterOrder_V2500() {
        return addStandardCategories(Arrays.stream(CLUSTER_PARTS_ORDER_V2500).toArray(String[]::new));
    }

    public static String[] getZ2ClusterOrder_CFM56_5B() {
        return addStandardCategories(Arrays.stream(CLUSTER_PARTS_ORDER_CFM56_5B).toArray(String[]::new));
    }

    public static String[] getPmaClusterOrder_CFM56_5B() {
        List<String> clusters = new ArrayList<>(Arrays.asList(CLUSTER_PARTS_ORDER_CFM56_5B));

        clusters.add("M9B - Rear Rot. Air/Oil Seal");
        clusters.add("M9B - Rear Vent Tube");

        return addStandardCategories(clusters.toArray(new String[0]));
    }

    private static String[] addStandardCategories(String[] originalArray) {
        String[] standardCategories = {KITS, COMPONENTS, PARTS_PACKAGES};
        return Stream.concat(Arrays.stream(originalArray), Arrays.stream(standardCategories))
                .toArray(String[]::new);
    }

    public static String[] getScrapCapsClusterPartsOrderLeap1a() {
        return Arrays.stream(CLUSTER_PARTS_ORDER_LEAP_1A).toArray(String[]::new);
    }

    public static String[] getScrapCapsClusterPartsOrderLeap1b() {
        return Arrays.stream(CLUSTER_PARTS_ORDER_LEAP_1B).toArray(String[]::new);
    }

    public static String[] getScrapCapsClusterPartsOrderV2500() {
        return Arrays.stream(CLUSTER_PARTS_ORDER_V2500).toArray(String[]::new);
    }

    public static String[] getScrapCapsClusterPartsOrderCfm565B() {
        return Arrays.stream(CLUSTER_PARTS_ORDER_CFM56_5B).toArray(String[]::new);
    }
}
