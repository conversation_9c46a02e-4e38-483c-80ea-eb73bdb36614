package com.lht.corecalculation.api.handlingcharges;

import com.lht.corecalculation.api.pojo.dto.handlingcharges.Cluster;
import java.util.Arrays;
import java.util.List;
import org.testng.Assert;


import static com.lht.corecalculation.api.constants.GlobalConstants.CLUSTER_ORDER_MISMATCH_AS_MESSAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.COMPONENTS;
import static com.lht.corecalculation.api.constants.GlobalConstants.FAN_BLADE;
import static com.lht.corecalculation.api.constants.GlobalConstants.HPC_VANE;
import static com.lht.corecalculation.api.constants.GlobalConstants.HPC_VANES;
import static com.lht.corecalculation.api.constants.GlobalConstants.KITS;
import static com.lht.corecalculation.api.constants.GlobalConstants.LPT_BLADES;
import static com.lht.corecalculation.api.constants.GlobalConstants.LPT_NOZZLES_STG_2_4;
import static com.lht.corecalculation.api.constants.GlobalConstants.MAIN_BEARINGS;
import static com.lht.corecalculation.api.constants.GlobalConstants.OTHER;
import static com.lht.corecalculation.api.constants.GlobalConstants.PARTS_PACKAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.REPAIR_MATERIAL;
import static com.lht.corecalculation.api.constants.GlobalConstants.ROUTINE_MATERIAL;

public class ClusterOrderAssertion {

    private static final Integer EXCLUDE_ROUTINE_AND_REPAIR_MATERIAL = 2;
    private static final Integer EXCLUDE_REPAIR_MATERIAL = 1;
    private static final String[] CLUSTER_ORDER_V2500 = {
            FAN_BLADE,
            "LPC Blades",
            "LPC Stator",
            "Fan Exit Guide Vane",
            "HPC Blades",
            HPC_VANES,
            "HPT Vane Stg. 1",
            "HPT Vane Stg. 2",
            "HPT Blade Stg. 1",
            "HPT Blade Stg. 2",
            "HPT Duct Segments",
            LPT_BLADES,
            "LPT Vanes",
            "CC Liner without Segments",
            MAIN_BEARINGS,
            "Major Case / Frame",
            "LLP",
            "Fwd & Aft Mount",
            "Other",
            KITS,
            COMPONENTS,
            PARTS_PACKAGE,
            ROUTINE_MATERIAL,
            REPAIR_MATERIAL
    };

    private static final String[] CLUSTER_ORDER_CFM56_5B = {
            FAN_BLADE,
            "LPC Blades",
            "LPC Vanes",
            "HPC Blades",
            HPC_VANES,
            "HPT Nozzle",
            "HPT Blade",
            "HPT Shroud",
            LPT_BLADES,
            "LPT Nozzle Stg. 1",
            LPT_NOZZLES_STG_2_4,
            "LPT Outer Airseal Segments",
            "Comb. Chamber Liner",
            MAIN_BEARINGS,
            "Fwd & Aft Mount",
            "LLP",
            "Case/Frame",
            "other A-Parts",
            KITS,
            COMPONENTS,
            PARTS_PACKAGE,
            ROUTINE_MATERIAL,
            REPAIR_MATERIAL
    };

    private static final String[] CLUSTER_ORDER_LEAP_1A = {
            "Fan Blade",
            "LPC Blade",
            "LPC Vane",
            "LPC OGV",
            HPC_VANE,
            "HPC Blade",
            "HPC Shroud",
            "CC Liner",
            "CC Dome",
            "HPT Nozzle Stg. 1",
            "HPT Nozzle Stg. 2",
            "HPT Blade Stg. 1",
            "HPT Blade Stg. 2",
            "HPT Shroud",
            "LPT Nozzle Stg. 1",
            "LPT Blade",
            "LPT Nozzle Stg. 2 - 7",
            "LPT Shroud Segments",
            "Main Bearing",
            "Case/Frame",
            "LLP",
            "Other",
            KITS,
            COMPONENTS,
            PARTS_PACKAGE,
            ROUTINE_MATERIAL,
            REPAIR_MATERIAL
    };

    private static final String[] CLUSTER_ORDER_LEAP_1B = {
            FAN_BLADE,
            "LPC Blade",
            "LPC Vane",
            "LPC OGV",
            HPC_VANE,
            "HPC Blade",
            "CC Liner",
            "CC Dome",
            "HPT Nozzle Stg. 1",
            "HPT Nozzle Stg. 2",
            "HPT Blade Stg. 1",
            "HPT Blade Stg. 2",
            "HPT Shroud",
            "LPT Nozzle Stg. 1",
            "LPT Blade",
            "LPT Nozzle Stg. 2 - 7",
            "LPT Shroud Segments",
            "Main Bearing",
            "Case/Frame",
            "LLP",
            OTHER,
            KITS,
            COMPONENTS,
            PARTS_PACKAGE,
            ROUTINE_MATERIAL,
            REPAIR_MATERIAL
    };

    public static void verifyClusterOrderMatchesV2500(List<Cluster> clusters) {
        Assert.assertEquals(CLUSTER_ORDER_V2500, getClusterNames(clusters), CLUSTER_ORDER_MISMATCH_AS_MESSAGE);
    }

    public static void verifyClusterOrderMatchesCfm565b(List<Cluster> clusters) {
        Assert.assertEquals(CLUSTER_ORDER_CFM56_5B, getClusterNames(clusters), CLUSTER_ORDER_MISMATCH_AS_MESSAGE);
    }

    public static void verifyClusterOrderMatchesLeap1b(List<Cluster> clusters) {
        Assert.assertEquals(CLUSTER_ORDER_LEAP_1B, getClusterNames(clusters), CLUSTER_ORDER_MISMATCH_AS_MESSAGE);
    }

    public static void verifyClusterOrderMatchesLeap1a(List<Cluster> clusters) {
        Assert.assertEquals(CLUSTER_ORDER_LEAP_1A, getClusterNames(clusters), CLUSTER_ORDER_MISMATCH_AS_MESSAGE);
    }

    private static String[] getClusterNames(List<Cluster> clusters) {
        return clusters.stream()
                .map(Cluster::getName)
                .toArray(String[]::new);
    }

    public static String[] getClusterOrderLeap1a() {
        return CLUSTER_ORDER_LEAP_1A.clone();
    }

    public static String[] getClusterOrderLeap1b() {
        return CLUSTER_ORDER_LEAP_1B.clone();
    }

    public static String[] getClusterOrderV2500() {
        return CLUSTER_ORDER_V2500.clone();
    }

    public static String[] getClusterOrderCfm565b() {
        return CLUSTER_ORDER_CFM56_5B.clone();
    }

    public static String[] getZ2ClusterOrderLeap1a() {
        return Arrays.copyOf(CLUSTER_ORDER_LEAP_1A, CLUSTER_ORDER_LEAP_1A.length - EXCLUDE_ROUTINE_AND_REPAIR_MATERIAL);
    }

    public static String[] getZ2ClusterOrderLeap1b() {
        return Arrays.copyOf(CLUSTER_ORDER_LEAP_1B, CLUSTER_ORDER_LEAP_1B.length - EXCLUDE_ROUTINE_AND_REPAIR_MATERIAL);
    }

    public static String[] getZ2ClusterOrderV2500() {
        return Arrays.copyOf(CLUSTER_ORDER_V2500, CLUSTER_ORDER_V2500.length - EXCLUDE_ROUTINE_AND_REPAIR_MATERIAL);
    }

    public static String[] getz2ClusterOrderCfm565B() {
        return Arrays.copyOf(CLUSTER_ORDER_CFM56_5B, CLUSTER_ORDER_CFM56_5B.length - EXCLUDE_ROUTINE_AND_REPAIR_MATERIAL);
    }

    public static String[] getScrapCapsClusterOrderLeap1a() {
        return Arrays.copyOf(CLUSTER_ORDER_LEAP_1A, CLUSTER_ORDER_LEAP_1A.length - EXCLUDE_REPAIR_MATERIAL);
    }

    public static String[] getScrapCapsClusterOrderLeap1b() {
        return Arrays.copyOf(CLUSTER_ORDER_LEAP_1B, CLUSTER_ORDER_LEAP_1B.length - EXCLUDE_REPAIR_MATERIAL);
    }

    public static String[] getScrapCapsClusterOrderV2500() {
        return Arrays.copyOf(CLUSTER_ORDER_V2500, CLUSTER_ORDER_V2500.length - EXCLUDE_REPAIR_MATERIAL);
    }

    public static String[] getScrapCapsClusterOrderCfm565B() {
        return Arrays.copyOf(CLUSTER_ORDER_CFM56_5B, CLUSTER_ORDER_CFM56_5B.length - EXCLUDE_REPAIR_MATERIAL);
    }

    public static String[] getSubcontractPriceClusterOrderLeap1a() {
        return getFilteredClusterOrder(CLUSTER_ORDER_LEAP_1A);
    }

    public static String[] getSubcontractPriceClusterOrderV2500() {
        return getFilteredClusterOrder(CLUSTER_ORDER_V2500);
    }

    public static String[] getSubcontractPriceClusterOrderCfm565B() {
        return getFilteredClusterOrder(CLUSTER_ORDER_CFM56_5B);
    }

    public static String[] getFilteredClusterOrder(String[] clusterOrder) {
        return Arrays.stream(clusterOrder)
                .filter(cluster -> !isExcludedPartType(cluster))
                .toArray(String[]::new);
    }

    private static boolean isExcludedPartType(String partType) {
        return KITS.equals(partType) ||
                ROUTINE_MATERIAL.equals(partType) ||
                REPAIR_MATERIAL.equals(partType);
    }

    public static String[] getRepairExclusionsClusterOrderLeap1a() {
        return getFilteredClusterOrder(CLUSTER_ORDER_LEAP_1A);
    }

    public static String[] getRepairExclusionsClusterOrderV2500() {
        return getFilteredClusterOrder(CLUSTER_ORDER_V2500);
    }

    public static String[] getRepairExclusionsClusterOrderCfm565B() {
        return getFilteredClusterOrder(CLUSTER_ORDER_CFM56_5B);
    }
}
