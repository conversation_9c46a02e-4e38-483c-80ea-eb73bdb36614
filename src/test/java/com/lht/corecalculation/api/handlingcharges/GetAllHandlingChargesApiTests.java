package com.lht.corecalculation.api.handlingcharges;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.lht.corecalculation.api.enums.QuotationStatus;
import com.lht.corecalculation.api.pojo.dto.handlingcharges.Cluster;
import com.lht.corecalculation.api.pojo.dto.handlingcharges.ResultDto;
import com.lht.corecalculation.api.pojo.dto.quotation.BeginQuotationProgressDto;
import com.lht.corecalculation.api.request.handlingcharges.GetHandlingChargesRequest;
import com.lht.corecalculation.api.request.quotation.GetQuotationsRequest;
import com.lht.corecalculation.api.request.quotation.PutQuotationByIdRequest;
import com.lht.corecalculation.api.utils.HandlingChargesUtil;
import com.lht.corecalculation.base.BaseCocaApiTest;
import io.restassured.response.Response;
import java.io.IOException;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;


import static com.lht.corecalculation.api.constants.GlobalConstants.ADMIN_TOKEN;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_CFM56_5B_PMA_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_CFM56_5B_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_LEAP_1A_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_LEAP_1B_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_V2500_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ERROR_DETAIL;
import static com.lht.corecalculation.api.constants.GlobalConstants.GENERIC_ERROR_MESSAGE_MISMATCH_AS_MESSAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_DATA_NULL_AS_MESSAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_200;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_401;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_404;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE;

public class GetAllHandlingChargesApiTests extends BaseCocaApiTest {

    private static final Logger LOGGER = LoggerFactory.getLogger(GetAllHandlingChargesApiTests.class);

    @BeforeClass(alwaysRun = true)
    public void beginAllQuotations() throws JsonProcessingException {
        String adminToken = utils.context.getContextItem(ADMIN_TOKEN);
        GetQuotationsRequest getQuotationsRequest =
                new GetQuotationsRequest()
                        .withAccessToken(adminToken);
        Response response = getQuotationsRequest.callAPI();

        String statuses = response.jsonPath().get("data.quotations.status").toString();
        Integer totalQuotations = response.jsonPath().get("data.totalItems");

        if (statuses.contains(QuotationStatus.ANKA_VALIDATED.name())) {
            for (int i = 1; i <= totalQuotations; i++) {
                BeginQuotationProgressDto beginQuotationProgressDto = new BeginQuotationProgressDto()
                        .withRoutineFixedPrice(true);

                PutQuotationByIdRequest putQuotationByIdRequest =
                        new PutQuotationByIdRequest(String.valueOf(i), utils.convert.dtoToJsonString(beginQuotationProgressDto))
                                .withBearerToken(adminToken);

                putQuotationByIdRequest.callAPI();
            }
        }
    }

    @Test(groups = {"smoke", "workscopeFixedPriceAndNte"}, dataProvider = "adminAccessTokenProvider")
    public void verifyHandlingCharges_ClustersOrder_Engine_V2500(String accessToken) throws IOException {
        GetHandlingChargesRequest getHandlingChargesRequest =
                new GetHandlingChargesRequest(ENGINE_V2500_QUOTATION_ID)
                        .withBearerToken(accessToken);

        Response response = getHandlingChargesRequest.callAPI();
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        ResultDto resultDto = utils.convert.jsonToDto(response, ResultDto.class);

        if (resultDto.getData() != null) {
            List<Cluster> clusters = resultDto.getData().getClusters();
            ClusterOrderAssertion.verifyClusterOrderMatchesV2500(clusters);
        } else {
            LOGGER.error(RESPONSE_DATA_NULL_AS_MESSAGE);
        }

    }

    @Test(groups = "workscopeFixedPriceAndNte", dataProvider = "adminAccessTokenProvider")
    public void verifyHandlingCharges_ClustersOrder_Engine_CFM56_5B(String accessToken) throws IOException {
        //5100207 -> id=2 ,engine=CFM56-5B
        GetHandlingChargesRequest getHandlingChargesRequest =
                new GetHandlingChargesRequest(ENGINE_CFM56_5B_QUOTATION_ID)
                        .withBearerToken(accessToken);

        Response response = getHandlingChargesRequest.callAPI();
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        ResultDto resultDto = utils.convert.jsonToDto(response, ResultDto.class);

        if (resultDto.getData() != null) {
            List<Cluster> clusters = resultDto.getData().getClusters();
            ClusterOrderAssertion.verifyClusterOrderMatchesCfm565b(clusters);
        } else {
            LOGGER.error(RESPONSE_DATA_NULL_AS_MESSAGE);
        }
    }

    @Test(groups = {"smoke", "workscopeFixedPriceAndNte"}, dataProvider = "adminAccessTokenProvider")
    public void verifyHandlingCharges_ClustersOrder_Engine_LEAP_1B(String accessToken) throws IOException {
        //5100219 -> id=3 ,engine=LEAP-1B
        GetHandlingChargesRequest getHandlingChargesRequest =
                new GetHandlingChargesRequest(ENGINE_LEAP_1B_QUOTATION_ID)
                        .withBearerToken(accessToken);

        Response response = getHandlingChargesRequest.callAPI();
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        ResultDto resultDto = utils.convert.jsonToDto(response, ResultDto.class);

        if (resultDto.getData() != null) {
            List<Cluster> clusters = resultDto.getData().getClusters();
            ClusterOrderAssertion.verifyClusterOrderMatchesLeap1b(clusters);
        } else {
            LOGGER.error(RESPONSE_DATA_NULL_AS_MESSAGE);
        }
    }

    @Test(groups = {"smoke", "workscopeFixedPriceAndNte"}, dataProvider = "adminAccessTokenProvider")
    public void verifyHandlingCharges_ClustersOrder_Engine_LEAP_1A(String accessToken) throws IOException {
        //5100255 -> id=4 ,engine=LEAP-1A
        GetHandlingChargesRequest getHandlingChargesRequest =
                new GetHandlingChargesRequest(ENGINE_LEAP_1A_QUOTATION_ID)
                        .withBearerToken(accessToken);

        Response response = getHandlingChargesRequest.callAPI();
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        ResultDto resultDto = utils.convert.jsonToDto(response, ResultDto.class);

        if (resultDto.getData() != null) {
            List<Cluster> clusters = resultDto.getData().getClusters();
            ClusterOrderAssertion.verifyClusterOrderMatchesLeap1a(clusters);
        } else {
            LOGGER.error(RESPONSE_DATA_NULL_AS_MESSAGE);
        }
    }

    @Test(groups = {"smoke", "workscopeFixedPriceAndNte"}, dataProvider = "adminAccessTokenProvider")
    public void verifyHandlingCharges_ClustersPartsOrder_Engine_V2500(String accessToken) throws IOException {
        GetHandlingChargesRequest getHandlingChargesRequest =
                new GetHandlingChargesRequest(ENGINE_V2500_QUOTATION_ID)
                        .withBearerToken(accessToken);

        Response response = getHandlingChargesRequest.callAPI();
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        ResultDto resultDto = utils.convert.jsonToDto(response, ResultDto.class);

        List<Cluster> clusters = resultDto.getData().getClusters();

        if (resultDto.getData() != null) {
            ClusterPartsNameOrderAssertion.verifyPartOrder_Engine_V2500(clusters);
        }
    }

    @Test(groups = "workscopeFixedPriceAndNte", dataProvider = "adminAccessTokenProvider")
    public void verifyHandlingCharges_ClustersPartsOrder_Engine_CFM56_5B(String accessToken) throws IOException {
        //5100207 -> id=2 ,engine=CFM56-5B
        GetHandlingChargesRequest getHandlingChargesRequest =
                new GetHandlingChargesRequest(ENGINE_CFM56_5B_QUOTATION_ID)
                        .withBearerToken(accessToken);

        Response response = getHandlingChargesRequest.callAPI();
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        ResultDto resultDto = utils.convert.jsonToDto(response, ResultDto.class);

        List<Cluster> clusters = resultDto.getData().getClusters();

        if (resultDto.getData() != null) {
            ClusterPartsNameOrderAssertion.verifyPartOrder_Engine_CFM56_5B(clusters);
        }
    }

    @Test(groups = {"smoke", "workscopeFixedPriceAndNte"}, dataProvider = "adminAccessTokenProvider")
    public void verifyHandlingCharges_ClustersPartsOrder_Engine_LEAP_1B(String accessToken) throws IOException {
        //5100219 -> id=3 ,engine=LEAP-1B
        GetHandlingChargesRequest getHandlingChargesRequest =
                new GetHandlingChargesRequest(ENGINE_LEAP_1B_QUOTATION_ID)
                        .withBearerToken(accessToken);

        Response response = getHandlingChargesRequest.callAPI();
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        ResultDto resultDto = utils.convert.jsonToDto(response, ResultDto.class);

        List<Cluster> clusters = resultDto.getData().getClusters();

        if (resultDto.getData() != null) {
            ClusterPartsNameOrderAssertion.verifyPartOrder_Engine_LEAP_1B(clusters);
        }
    }

    @Test(groups = {"smoke", "workscopeFixedPriceAndNte"}, dataProvider = "adminAccessTokenProvider")
    public void verifyHandlingCharges_ClustersPartsOrder_Engine_LEAP_1A(String accessToken) throws IOException {
        //5100255 -> id=4 ,engine=LEAP-1A
        GetHandlingChargesRequest getHandlingChargesRequest =
                new GetHandlingChargesRequest(ENGINE_LEAP_1A_QUOTATION_ID)
                        .withBearerToken(accessToken);

        Response response = getHandlingChargesRequest.callAPI();
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        ResultDto resultDto = utils.convert.jsonToDto(response, ResultDto.class);

        List<Cluster> clusters = resultDto.getData().getClusters();

        if (resultDto.getData() != null) {
            ClusterPartsNameOrderAssertion.verifyPartOrder_Engine_LEAP_1A(clusters);
        }
    }

    @Test(groups = "workscopeFixedPriceAndNte")
    public void verifyHandlingCharges_EndpointDontReturnResultWithoutToken_Engine_V2500() {
        String errorToken = utils.context.getContextItem("Error");
        GetHandlingChargesRequest getHandlingChargesRequest =
                new GetHandlingChargesRequest(ENGINE_V2500_QUOTATION_ID)
                        .withBearerToken(errorToken);

        Response response = getHandlingChargesRequest.callAPI();
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_401, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = "workscopeFixedPriceAndNte")
    public void verifyHandlingCharges_EndpointReturnNotFoundForNotExistingQuotation_WithId_100() {
        //5100162 -> id=1 ,engine=V2500
        String adminToken = utils.context.getContextItem(ADMIN_TOKEN);
        GetHandlingChargesRequest getHandlingChargesRequest =
                new GetHandlingChargesRequest("100")
                        .withBearerToken(adminToken);

        Response response = getHandlingChargesRequest.callAPI();
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_404, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
        Assert.assertTrue(response.getBody().jsonPath().get(ERROR_DETAIL).toString().contains("Quotation with id: [100] is not found"), GENERIC_ERROR_MESSAGE_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "adminAccessTokenProvider")
    public void verifyHandlingCharges_SingleAndLineItemCapCannotBeAddedInSameCluster_QuotationId_1(String accessToken) throws IOException {
        GetHandlingChargesRequest getHandlingChargesRequest =
                new GetHandlingChargesRequest(ENGINE_V2500_QUOTATION_ID)
                        .withBearerToken(accessToken);

        Response response = getHandlingChargesRequest.callAPI();
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        ResultDto resultDto = utils.convert.jsonToDto(response, ResultDto.class);

        HandlingChargesUtil.Assertion.validateCapConfiguration(resultDto);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "adminAccessTokenProvider")
    public void verifyHandlingCharges_SingleAndLineItemCapCannotBeAddedInSameCluster_QuotationId_2(String accessToken) throws IOException {
        //5100207 -> id=2 ,engine=CFM56-5B
        GetHandlingChargesRequest getHandlingChargesRequest =
                new GetHandlingChargesRequest(ENGINE_CFM56_5B_QUOTATION_ID)
                        .withBearerToken(accessToken);

        Response response = getHandlingChargesRequest.callAPI();
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        ResultDto resultDto = utils.convert.jsonToDto(response, ResultDto.class);

        HandlingChargesUtil.Assertion.validateCapConfiguration(resultDto);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "adminAccessTokenProvider")
    public void verifyHandlingCharges_SingleAndLineItemCapCannotBeAddedInSameCluster_QuotationId_3(String accessToken) throws IOException {
        //5100219 -> id=3 ,engine=LEAP-1B
        GetHandlingChargesRequest getHandlingChargesRequest =
                new GetHandlingChargesRequest(ENGINE_LEAP_1B_QUOTATION_ID)
                        .withBearerToken(accessToken);

        Response response = getHandlingChargesRequest.callAPI();
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        ResultDto resultDto = utils.convert.jsonToDto(response, ResultDto.class);

        HandlingChargesUtil.Assertion.validateCapConfiguration(resultDto);
    }

    @Test(groups = "workscopeFixedPriceAndNte", dataProvider = "adminAccessTokenProvider")
    public void verifyHandlingCharges_SingleAndLineItemCapCannotBeAddedInSameCluster_QuotationId_4(String accessToken) throws IOException {
        //5100255 -> id=4 ,engine=LEAP-1A
        GetHandlingChargesRequest getHandlingChargesRequest =
                new GetHandlingChargesRequest(ENGINE_LEAP_1A_QUOTATION_ID)
                        .withBearerToken(accessToken);

        Response response = getHandlingChargesRequest.callAPI();
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        ResultDto resultDto = utils.convert.jsonToDto(response, ResultDto.class);

        HandlingChargesUtil.Assertion.validateCapConfiguration(resultDto);
    }

    @Test(dataProvider = "adminAccessTokenProvider")
    public void verifyHandlingCharges_SingleAndLineItemCapCannotBeAddedInSameCluster_QuotationId_6(String accessToken) throws IOException {
        //5100456 -> id=6 ,engine=CFM56_5B
        GetHandlingChargesRequest getHandlingChargesRequest =
                new GetHandlingChargesRequest(ENGINE_CFM56_5B_PMA_QUOTATION_ID)
                        .withBearerToken(accessToken);

        Response response = getHandlingChargesRequest.callAPI();
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        ResultDto resultDto = utils.convert.jsonToDto(response, ResultDto.class);

        HandlingChargesUtil.Assertion.validateCapConfiguration(resultDto);
    }
}
