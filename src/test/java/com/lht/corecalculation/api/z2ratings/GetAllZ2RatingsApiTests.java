package com.lht.corecalculation.api.z2ratings;

import com.lht.corecalculation.api.handlingcharges.ClusterOrderAssertion;
import com.lht.corecalculation.api.handlingcharges.ClusterPartsNameOrderAssertion;
import com.lht.corecalculation.api.pojo.dto.z2rating.ClusterDto;
import com.lht.corecalculation.api.pojo.dto.z2rating.PartDto;
import com.lht.corecalculation.api.pojo.dto.z2rating.ResponseDto;
import com.lht.corecalculation.api.pojo.dto.z2rating.YearDto;
import com.lht.corecalculation.api.request.z2ratings.GetZ2RatingsRequest;
import com.lht.corecalculation.api.utils.Z2RatingsUtil;
import com.lht.corecalculation.base.BaseCocaApiTest;
import com.zebrunner.carina.core.registrar.ownership.MethodOwner;
import io.restassured.response.Response;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import org.testng.annotations.Test;


import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_CFM56_5B_PMA_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_CFM56_5B_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_LEAP_1A_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_LEAP_1B_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_V2500_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ERROR_DETAILS_MISMATCH_AS_MESSAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.NO_ROLE_TOKEN;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_200;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_401;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_403;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_404;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.VIEW_ONLY_TOKEN;
import static org.testng.Assert.assertEquals;

public class GetAllZ2RatingsApiTests extends BaseCocaApiTest {

    public static final List<String> VALID_CURRENCY_VALUES = Arrays.asList("USD", null);

    @Test(groups = {"smoke", "workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "adminAccessTokenProvider")
    public void verifyZ2Rating_ClusterOrder_Engine_V2500(String accessToken) throws IOException {
        ResponseDto responseDto = Z2RatingsUtil.fetchZ2RatingsResponseDto(accessToken, ENGINE_V2500_QUOTATION_ID);

        List<String> actualClustersNames = Z2RatingsUtil.extractFromClusters(responseDto, ClusterDto::getName);
        List<String> expectedClusterOrderList = Arrays.asList(ClusterOrderAssertion.getZ2ClusterOrderV2500());

        assertEquals(actualClustersNames, expectedClusterOrderList, ERROR_DETAILS_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = {"smoke", "workscopeFixedPriceAndNte" , "modularFixedPriceAndNte"}, dataProvider = "adminAccessTokenProvider")
    public void verifyZ2Rating_ClusterOrder_Engine_LEAP_1A(String accessToken) throws IOException {
        ResponseDto responseDto = Z2RatingsUtil.fetchZ2RatingsResponseDto(accessToken, ENGINE_LEAP_1A_QUOTATION_ID);

        List<String> actualClustersNames = Z2RatingsUtil.extractFromClusters(responseDto, ClusterDto::getName);
        List<String> expectedClusterOrderList = Arrays.asList(ClusterOrderAssertion.getZ2ClusterOrderLeap1a());

        assertEquals(actualClustersNames, expectedClusterOrderList, ERROR_DETAILS_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = "workscopeFixedPriceAndNte", dataProvider = "adminAccessTokenProvider")
    public void verifyZ2Rating_ClusterOrder_Engine_LEAP_1B(String accessToken) throws IOException {
        ResponseDto responseDto = Z2RatingsUtil.fetchZ2RatingsResponseDto(accessToken, ENGINE_LEAP_1B_QUOTATION_ID);

        List<String> actualClustersNames = Z2RatingsUtil.extractFromClusters(responseDto, ClusterDto::getName);
        List<String> expectedClusterOrderList = Arrays.asList(ClusterOrderAssertion.getZ2ClusterOrderLeap1b());

        assertEquals(actualClustersNames, expectedClusterOrderList, ERROR_DETAILS_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = "workscopeFixedPriceAndNte", dataProvider = "adminAccessTokenProvider")
    public void verifyZ2Rating_ClusterOrder_Engine_CFM56_5B(String accessToken) throws IOException {
        ResponseDto responseDto = Z2RatingsUtil.fetchZ2RatingsResponseDto(accessToken, ENGINE_CFM56_5B_QUOTATION_ID);

        List<String> actualClustersNames = Z2RatingsUtil.extractFromClusters(responseDto, ClusterDto::getName);
        List<String> expectedClusterOrderList = Arrays.asList(ClusterOrderAssertion.getz2ClusterOrderCfm565B());

        assertEquals(actualClustersNames, expectedClusterOrderList, ERROR_DETAILS_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = "workscopeFixedPriceAndNte", dataProvider = "adminAccessTokenProvider")
    public void verifyZ2Rating_PartsOrder_Engine_CFM56_5B(String accessToken) throws IOException {
        ResponseDto responseDto = Z2RatingsUtil.fetchZ2RatingsResponseDto(accessToken, ENGINE_CFM56_5B_QUOTATION_ID);

        List<String> actualClustersNames = Z2RatingsUtil.extractFromParts(responseDto, PartDto::getName);
        List<String> expectedClusterOrderList = Arrays.asList(ClusterPartsNameOrderAssertion.getZ2ClusterOrder_CFM56_5B());

        assertEquals(actualClustersNames, expectedClusterOrderList, ERROR_DETAILS_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = {"smoke", "workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "adminAccessTokenProvider")
    public void verifyZ2Rating_PartsOrder_Engine_V2500(String accessToken) throws IOException {
        ResponseDto responseDto = Z2RatingsUtil.fetchZ2RatingsResponseDto(accessToken, ENGINE_V2500_QUOTATION_ID);

        List<String> actualClustersNames = Z2RatingsUtil.extractFromParts(responseDto, PartDto::getName);
        List<String> expectedClusterOrderList = Arrays.asList(ClusterPartsNameOrderAssertion.getZ2ClusterOrder_V2500());

        assertEquals(actualClustersNames, expectedClusterOrderList, ERROR_DETAILS_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "adminAccessTokenProvider" )
    public void verifyZ2Rating_PartsOrder_Engine_LEAP_1A(String accessToken) throws IOException {
        ResponseDto responseDto = Z2RatingsUtil.fetchZ2RatingsResponseDto(accessToken, ENGINE_LEAP_1A_QUOTATION_ID);

        List<String> actualClustersNames = Z2RatingsUtil.extractFromParts(responseDto, PartDto::getName);
        List<String> expectedClusterOrderList = Arrays.asList(ClusterPartsNameOrderAssertion.getZ2ClusterOrder_LEAP_1A());

        assertEquals(actualClustersNames, expectedClusterOrderList, ERROR_DETAILS_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = "workscopeFixedPriceAndNte", dataProvider = "adminAccessTokenProvider")
    @MethodOwner(owner = "nino.simeonov")
    public void verifyZ2Rating_PartsOrder_Engine_LEAP_1B(String accessToken) throws IOException {
        ResponseDto responseDto = Z2RatingsUtil.fetchZ2RatingsResponseDto(accessToken, ENGINE_LEAP_1B_QUOTATION_ID);

        List<String> actualClustersNames = Z2RatingsUtil.extractFromParts(responseDto, PartDto::getName);
        List<String> expectedClusterOrderList = Arrays.asList(ClusterPartsNameOrderAssertion.getZ2ClusterOrder_LEAP_1B());

        assertEquals(actualClustersNames, expectedClusterOrderList, ERROR_DETAILS_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = "workscopeFixedPriceAndNte", dataProvider = "adminAccessTokenProvider")
    public void verifyZ2Rating_QuotationYears_Engine_LEAP_1B(String accessToken) throws IOException {
        ResponseDto responseDto = Z2RatingsUtil.fetchZ2RatingsResponseDto(accessToken, ENGINE_LEAP_1B_QUOTATION_ID);

        List<String> actualYearsInQuotation = Z2RatingsUtil.extractFromYears(responseDto, YearDto::getYear);
        List<String> expectedYearsInQuotation = Z2RatingsUtil.fetchQuotationYears(accessToken, ENGINE_LEAP_1B_QUOTATION_ID);

        assertEquals(actualYearsInQuotation, expectedYearsInQuotation, ERROR_DETAILS_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "adminAccessTokenProvider")
    public void verifyZ2Rating_QuotationYears_Engine_LEAP_1A_255(String accessToken) throws IOException {
        ResponseDto responseDto = Z2RatingsUtil.fetchZ2RatingsResponseDto(accessToken, ENGINE_LEAP_1A_QUOTATION_ID);

        List<String> actualYearsInQuotation = Z2RatingsUtil.extractFromYears(responseDto, YearDto::getYear);
        List<String> expectedYearsInQuotation = Z2RatingsUtil.fetchQuotationYears(accessToken, ENGINE_LEAP_1A_QUOTATION_ID);

        assertEquals(actualYearsInQuotation, expectedYearsInQuotation, ERROR_DETAILS_MISMATCH_AS_MESSAGE);
    }

    @Test(dataProvider = "adminAccessTokenProvider")
    public void verifyZ2Rating_QuotationYears_Engine_CFM56_5B_PMA(String accessToken) throws IOException {
        ResponseDto responseDto = Z2RatingsUtil.fetchZ2RatingsResponseDto(accessToken, ENGINE_CFM56_5B_PMA_QUOTATION_ID);

        List<String> actualYearsInQuotation = Z2RatingsUtil.extractFromYears(responseDto, YearDto::getYear);
        List<String> expectedYearsInQuotation = Z2RatingsUtil.fetchQuotationYears(accessToken, ENGINE_CFM56_5B_PMA_QUOTATION_ID);

        assertEquals(actualYearsInQuotation, expectedYearsInQuotation, ERROR_DETAILS_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = {"smoke", "workscopeFixedPriceAndNte"}, dataProvider = "adminAccessTokenProvider")
    public void verifyZ2Rating_QuotationYears_Engine_V2500(String accessToken) throws IOException {
        ResponseDto responseDto = Z2RatingsUtil.fetchZ2RatingsResponseDto(accessToken, ENGINE_V2500_QUOTATION_ID);

        List<String> actualYearsInQuotation = Z2RatingsUtil.extractFromYears(responseDto, YearDto::getYear);
        List<String> expectedYearsInQuotation = Z2RatingsUtil.fetchQuotationYears(accessToken, ENGINE_V2500_QUOTATION_ID);

        assertEquals(actualYearsInQuotation, expectedYearsInQuotation, ERROR_DETAILS_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = "workscopeFixedPriceAndNte", dataProvider = "adminAccessTokenProvider")
    public void verifyZ2Rating_QuotationYears_Engine_CFM56_5B(String accessToken) throws IOException {
        ResponseDto responseDto = Z2RatingsUtil.fetchZ2RatingsResponseDto(accessToken, ENGINE_CFM56_5B_QUOTATION_ID);

        List<String> actualYearsInQuotation = Z2RatingsUtil.extractFromYears(responseDto, YearDto::getYear);
        List<String> expectedYearsInQuotation = Z2RatingsUtil.fetchQuotationYears(accessToken, ENGINE_CFM56_5B_QUOTATION_ID);

        assertEquals(actualYearsInQuotation, expectedYearsInQuotation, ERROR_DETAILS_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = "workscopeFixedPriceAndNte", dataProvider = "adminAccessTokenProvider")
    public void verifyZ2Rating_CurrencyOnlyUsdAndNull_Engine_CFM56_5B(String accessToken) throws IOException {
        ResponseDto responseDto = Z2RatingsUtil.fetchZ2RatingsResponseDto(accessToken, ENGINE_CFM56_5B_QUOTATION_ID);

        List<String> actualCurrency = Z2RatingsUtil.extractFromParts(responseDto, PartDto::getCurrency);

        assertEquals(actualCurrency, VALID_CURRENCY_VALUES, ERROR_DETAILS_MISMATCH_AS_MESSAGE);
    }

    @Test(dataProvider = "adminAccessTokenProvider")
    public void verifyZ2Rating_CurrencyOnlyUsdAndNull_CFM56_5B_PMA(String accessToken) throws IOException {
        ResponseDto responseDto = Z2RatingsUtil.fetchZ2RatingsResponseDto(accessToken, ENGINE_CFM56_5B_PMA_QUOTATION_ID);

        List<String> actualCurrency = Z2RatingsUtil.extractFromParts(responseDto, PartDto::getCurrency);

        assertEquals(actualCurrency, VALID_CURRENCY_VALUES, ERROR_DETAILS_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "adminAccessTokenProvider")
    public void verifyZ2Rating_CurrencyOnlyUsdAndNull_Engine_LEAP_1A_255(String accessToken) throws IOException {
        ResponseDto responseDto = Z2RatingsUtil.fetchZ2RatingsResponseDto(accessToken, ENGINE_LEAP_1A_QUOTATION_ID);

        List<String> actualCurrency = Z2RatingsUtil.extractFromParts(responseDto, PartDto::getCurrency);

        assertEquals(actualCurrency, VALID_CURRENCY_VALUES, ERROR_DETAILS_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = "workscopeFixedPriceAndNte", dataProvider = "adminAccessTokenProvider")
    public void verifyZ2Rating_CurrencyOnlyUsdAndNull_Engine_LEAP_1B(String accessToken) throws IOException {
        ResponseDto responseDto = Z2RatingsUtil.fetchZ2RatingsResponseDto(accessToken, ENGINE_LEAP_1B_QUOTATION_ID);

        List<String> actualCurrency = Z2RatingsUtil.extractFromParts(responseDto, PartDto::getCurrency);

        assertEquals(actualCurrency, VALID_CURRENCY_VALUES, ERROR_DETAILS_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = "workscopeFixedPriceAndNte", dataProvider = "adminAccessTokenProvider")
    public void verifyZ2Rating_CurrencyOnlyUsdAndNull_Engine_V2500(String accessToken) throws IOException {
        ResponseDto responseDto = Z2RatingsUtil.fetchZ2RatingsResponseDto(accessToken, ENGINE_V2500_QUOTATION_ID);

        List<String> actualCurrency = Z2RatingsUtil.extractFromParts(responseDto, PartDto::getCurrency);

        assertEquals(actualCurrency, VALID_CURRENCY_VALUES, ERROR_DETAILS_MISMATCH_AS_MESSAGE);
    }

    @Test()
    public void verifyUnauthorizedUserCannotSeeTheZ2RatingPage_Engine_V2500() {
        String noRoleToken = utils.context.getContextItem(NO_ROLE_TOKEN);
        GetZ2RatingsRequest getZ2RatingsRequest = new GetZ2RatingsRequest(ENGINE_V2500_QUOTATION_ID)
                .withBearerToken(noRoleToken);
        Response response = getZ2RatingsRequest.callAPI();
        assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_401, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = "workscopeFixedPriceAndNte")
    public void verifyViewOnlyUserCanSeeTheZ2RatingPage_Engine_V2500() {
        String viewOnly = utils.context.getContextItem(VIEW_ONLY_TOKEN);
        GetZ2RatingsRequest getZ2RatingsRequest = new GetZ2RatingsRequest(ENGINE_V2500_QUOTATION_ID)
                .withBearerToken(viewOnly);
        Response response = getZ2RatingsRequest.callAPI();
        assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
    }

    @Test(dataProvider = "adminAccessTokenProvider")
    public void verifyZ2Rating_unexistingQuotationRerutnNotFoundStatusCode_Engine_V2500(String accessToken) throws IOException {
        GetZ2RatingsRequest getZ2RatingsRequest = new GetZ2RatingsRequest("100")
                .withBearerToken(accessToken);
        Response response = getZ2RatingsRequest.callAPI();
        assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_404, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
    }
}
