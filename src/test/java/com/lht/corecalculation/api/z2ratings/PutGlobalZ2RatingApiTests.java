package com.lht.corecalculation.api.z2ratings;

import com.lht.corecalculation.api.pojo.dto.z2rating.ResponseDto;
import com.lht.corecalculation.api.pojo.dto.z2rating.Z2RatingInputDto;
import com.lht.corecalculation.api.utils.Z2RatingsUtil;
import com.lht.corecalculation.base.BaseCocaApiTest;
import java.io.IOException;
import java.util.List;
import java.util.function.Function;
import org.testng.annotations.Test;


import static com.lht.corecalculation.api.constants.GlobalConstants.ADMIN_TOKEN;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_CFM56_5B_PMA_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_CFM56_5B_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_LEAP_1A_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_LEAP_1B_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_V2500_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_200;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_400;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_500;

public class PutGlobalZ2RatingApiTests extends BaseCocaApiTest {

    @Test(groups = {"smoke", "workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "doubleValues")
    public void verifyGlobalZ2Rating_CanUpdateAllPartsValueInAllYears_Engine_V2500(String accessToken, Double value) throws IOException {
        String adminToken = utils.context.getContextItem(ADMIN_TOKEN);

        ResponseDto initialRatingsResponse = Z2RatingsUtil.fetchZ2RatingsResponseDto(adminToken, ENGINE_V2500_QUOTATION_ID);

        List<Z2RatingInputDto> z2RatingInputs = Z2RatingsUtil.buildZ2RatingInputs(Z2RatingsUtil.extractFromParts(initialRatingsResponse, Function.identity()), value);
        String updateRequestBody = utils.convert.dtoToJsonString(Z2RatingsUtil.createRatingsUpdate(z2RatingInputs));
        Z2RatingsUtil.assertResponseStatusCode(Z2RatingsUtil.updateZ2Ratings(adminToken, updateRequestBody, ENGINE_V2500_QUOTATION_ID), RESPONSE_STATUS_CODE_200);

        Z2RatingsUtil.validateUpdatedRatings(Z2RatingsUtil.fetchZ2RatingsResponseDto(adminToken, ENGINE_V2500_QUOTATION_ID), value);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "doubleValues")
    public void verifyGlobalZ2Rating_CanUpdateAllPartsValueInAllYears_Engine_LEAP_1A_255(
            String accessToken,
            Double value
    ) throws IOException {
        String adminToken = utils.context.getContextItem(ADMIN_TOKEN);

        ResponseDto initialRatingsResponse = Z2RatingsUtil.fetchZ2RatingsResponseDto(adminToken, ENGINE_LEAP_1A_QUOTATION_ID);

        List<Z2RatingInputDto> z2RatingInputs = Z2RatingsUtil.buildZ2RatingInputs(Z2RatingsUtil.extractFromParts(initialRatingsResponse, Function.identity()), value);
        String updateRequestBody = utils.convert.dtoToJsonString(Z2RatingsUtil.createRatingsUpdate(z2RatingInputs));
        Z2RatingsUtil.assertResponseStatusCode(Z2RatingsUtil.updateZ2Ratings(adminToken, updateRequestBody, ENGINE_LEAP_1A_QUOTATION_ID), RESPONSE_STATUS_CODE_200);

        Z2RatingsUtil.validateUpdatedRatings(Z2RatingsUtil.fetchZ2RatingsResponseDto(adminToken, ENGINE_LEAP_1A_QUOTATION_ID), value);
    }

    @Test(dataProvider = "doubleValues")
    public void verifyGlobalZ2Rating_CanUpdateAllPartsValueInAllYears_Engine_CFM56_5B_PMA(
            String accessToken,
            Double value
    ) throws IOException {
        String adminToken = utils.context.getContextItem(ADMIN_TOKEN);

        ResponseDto initialRatingsResponse = Z2RatingsUtil.fetchZ2RatingsResponseDto(adminToken, ENGINE_CFM56_5B_PMA_QUOTATION_ID);

        List<Z2RatingInputDto> z2RatingInputs = Z2RatingsUtil.buildZ2RatingInputs(Z2RatingsUtil.extractFromParts(initialRatingsResponse, Function.identity()), value);
        String updateRequestBody = utils.convert.dtoToJsonString(Z2RatingsUtil.createRatingsUpdate(z2RatingInputs));
        Z2RatingsUtil.assertResponseStatusCode(Z2RatingsUtil.updateZ2Ratings(adminToken, updateRequestBody, ENGINE_CFM56_5B_PMA_QUOTATION_ID), RESPONSE_STATUS_CODE_200);

        Z2RatingsUtil.validateUpdatedRatings(Z2RatingsUtil.fetchZ2RatingsResponseDto(adminToken, ENGINE_CFM56_5B_PMA_QUOTATION_ID), value);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "doubleValues")
    public void verifyGlobalZ2Rating_CanUpdateAllPartsValueInAllYears_Engine_LEAP_1B(String accessToken, Double value) throws IOException {
        String adminToken = utils.context.getContextItem(ADMIN_TOKEN);

        ResponseDto initialRatingsResponse = Z2RatingsUtil.fetchZ2RatingsResponseDto(adminToken, ENGINE_LEAP_1B_QUOTATION_ID);

        List<Z2RatingInputDto> z2RatingInputs = Z2RatingsUtil.buildZ2RatingInputs(Z2RatingsUtil.extractFromParts(initialRatingsResponse, Function.identity()), value);
        String updateRequestBody = utils.convert.dtoToJsonString(Z2RatingsUtil.createRatingsUpdate(z2RatingInputs));
        Z2RatingsUtil.assertResponseStatusCode(Z2RatingsUtil.updateZ2Ratings(adminToken, updateRequestBody, ENGINE_LEAP_1B_QUOTATION_ID), RESPONSE_STATUS_CODE_200);

        Z2RatingsUtil.validateUpdatedRatings(Z2RatingsUtil.fetchZ2RatingsResponseDto(adminToken, ENGINE_LEAP_1B_QUOTATION_ID), value);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "doubleValues")
    public void verifyGlobalZ2Rating_CanUpdateAllPartsValueInAllYears_Engine_CFM56_5B(String accessToken, Double value) throws IOException {
        String adminToken = utils.context.getContextItem(ADMIN_TOKEN);

        ResponseDto initialRatingsResponse = Z2RatingsUtil.fetchZ2RatingsResponseDto(adminToken, ENGINE_CFM56_5B_QUOTATION_ID);

        List<Z2RatingInputDto> z2RatingInputs = Z2RatingsUtil.buildZ2RatingInputs(Z2RatingsUtil.extractFromParts(initialRatingsResponse, Function.identity()), value);
        String updateRequestBody = utils.convert.dtoToJsonString(Z2RatingsUtil.createRatingsUpdate(z2RatingInputs));
        Z2RatingsUtil.assertResponseStatusCode(Z2RatingsUtil.updateZ2Ratings(adminToken, updateRequestBody, ENGINE_CFM56_5B_QUOTATION_ID), RESPONSE_STATUS_CODE_200);

        Z2RatingsUtil.validateUpdatedRatings(Z2RatingsUtil.fetchZ2RatingsResponseDto(adminToken, ENGINE_CFM56_5B_QUOTATION_ID), value);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"})
    public void verifyGlobalZ2Rating_CannotUpdateAllPartsInAllYearsWithNegativeValue_Engine_LEAP_1B() throws IOException {
        String adminToken = utils.context.getContextItem(ADMIN_TOKEN);
        Double value = -33.0;

        ResponseDto initialRatingsResponse = Z2RatingsUtil.fetchZ2RatingsResponseDto(adminToken, ENGINE_LEAP_1B_QUOTATION_ID);

        List<Z2RatingInputDto> z2RatingInputs = Z2RatingsUtil.buildZ2RatingInputs(Z2RatingsUtil.extractFromParts(initialRatingsResponse, Function.identity()), value);
        String updateRequestBody = utils.convert.dtoToJsonString(Z2RatingsUtil.createRatingsUpdate(z2RatingInputs));
        Z2RatingsUtil.assertResponseStatusCode(Z2RatingsUtil.updateZ2Ratings(adminToken, updateRequestBody, ENGINE_LEAP_1B_QUOTATION_ID), RESPONSE_STATUS_CODE_400);
    }

    @Test()
    public void verifyGlobalZ2Rating_CannotUpdateAllPartsInAllYearsWithValueOver100_Engine_LEAP_1B() throws IOException {
        String adminToken = utils.context.getContextItem(ADMIN_TOKEN);
        Double value = 100.919;

        ResponseDto initialRatingsResponse = Z2RatingsUtil.fetchZ2RatingsResponseDto(adminToken, ENGINE_LEAP_1B_QUOTATION_ID);

        List<Z2RatingInputDto> z2RatingInputs = Z2RatingsUtil.buildZ2RatingInputs(Z2RatingsUtil.extractFromParts(initialRatingsResponse, Function.identity()), value);
        String updateRequestBody = utils.convert.dtoToJsonString(Z2RatingsUtil.createRatingsUpdate(z2RatingInputs));
        Z2RatingsUtil.assertResponseStatusCode(Z2RatingsUtil.updateZ2Ratings(adminToken, updateRequestBody, ENGINE_LEAP_1B_QUOTATION_ID), RESPONSE_STATUS_CODE_400);
    }

    @Test()
    public void verifyGlobalZ2Rating_CannotUpdateAllPartsInAllYearsWithMaxValue_Engine_LEAP_1B() throws IOException {
        String adminToken = utils.context.getContextItem(ADMIN_TOKEN);
        Double value = Double.MAX_VALUE;

        ResponseDto initialRatingsResponse = Z2RatingsUtil.fetchZ2RatingsResponseDto(adminToken, ENGINE_LEAP_1B_QUOTATION_ID);

        List<Z2RatingInputDto> z2RatingInputs = Z2RatingsUtil.buildZ2RatingInputs(Z2RatingsUtil.extractFromParts(initialRatingsResponse, Function.identity()), value);
        String updateRequestBody = utils.convert.dtoToJsonString(Z2RatingsUtil.createRatingsUpdate(z2RatingInputs));
        Z2RatingsUtil.assertResponseStatusCode(Z2RatingsUtil.updateZ2Ratings(adminToken, updateRequestBody, ENGINE_LEAP_1B_QUOTATION_ID), RESPONSE_STATUS_CODE_500);
    }
}
