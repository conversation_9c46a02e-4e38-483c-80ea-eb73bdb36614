package com.lht.corecalculation.api.z2ratings;

import com.lht.corecalculation.api.pojo.dto.z2rating.PartDto;
import com.lht.corecalculation.api.pojo.dto.z2rating.ResponseDto;
import com.lht.corecalculation.api.pojo.dto.z2rating.Z2RatingInputDto;
import com.lht.corecalculation.api.utils.Z2RatingsUtil;
import com.lht.corecalculation.base.BaseCocaApiTest;
import java.io.IOException;
import java.util.List;
import java.util.function.Function;
import org.testng.Assert;
import org.testng.annotations.Test;


import static com.lht.corecalculation.api.constants.GlobalConstants.ADMIN_TOKEN;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_CFM56_5B_PMA_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_CFM56_5B_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_LEAP_1A_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_LEAP_1B_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_V2500_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_DATA_NULL_AS_MESSAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_200;

public class PutIndividualZ2RatingApiTests extends BaseCocaApiTest {

    @Test(groups = {"smoke", "workscopeFixedPriceAndNte", "modularFixedPriceAndNte"})
    public void verifyZ2Rating_CanUpdateAllPartValueInYear_Engine_V2500() throws IOException {
        String adminToken = utils.context.getContextItem(ADMIN_TOKEN);
        Double value = 24.4;
        Double newValue = 12.3;
        String year = "2025";

        ResponseDto initialRatingsResponse = Z2RatingsUtil.fetchZ2RatingsResponseDto(adminToken, ENGINE_V2500_QUOTATION_ID);

        List<Z2RatingInputDto> z2RatingInputs = Z2RatingsUtil.buildZ2RatingInputsSpecificYearAndValue(
                Z2RatingsUtil.extractFromParts(initialRatingsResponse, Function.identity()), value, initialRatingsResponse, year, newValue);

        String updateRequestBody = utils.convert.dtoToJsonString(Z2RatingsUtil.createRatingsUpdate(z2RatingInputs));
        Z2RatingsUtil.assertResponseStatusCode(Z2RatingsUtil.updateZ2Ratings(adminToken, updateRequestBody, ENGINE_V2500_QUOTATION_ID), RESPONSE_STATUS_CODE_200);

        ResponseDto updatedRatingsResponse = Z2RatingsUtil.fetchZ2RatingsResponseDto(adminToken, ENGINE_V2500_QUOTATION_ID);
        List<Double> listActualZ2RatingInput = Z2RatingsUtil.getAttributeForSpecificYear(updatedRatingsResponse, year, PartDto::getZ2RatingInput);

        listActualZ2RatingInput.forEach(actualValue -> Assert.assertEquals(actualValue, newValue));
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"})
    public void verifyZ2Rating_CanUpdateAllPartValueInYear_Engine_LEAP_1A_255() throws IOException {
        String adminToken = utils.context.getContextItem(ADMIN_TOKEN);
        Double value = 0.9;
        Double newValue = 50.9;
        String year = "2025";

        ResponseDto initialRatingsResponse = Z2RatingsUtil.fetchZ2RatingsResponseDto(adminToken, ENGINE_LEAP_1A_QUOTATION_ID);

        List<Z2RatingInputDto> z2RatingInputs = Z2RatingsUtil.buildZ2RatingInputsSpecificYearAndValue(
                Z2RatingsUtil.extractFromParts(initialRatingsResponse, Function.identity()), value, initialRatingsResponse, year, newValue);

        String updateRequestBody = utils.convert.dtoToJsonString(Z2RatingsUtil.createRatingsUpdate(z2RatingInputs));
        Z2RatingsUtil.assertResponseStatusCode(Z2RatingsUtil.updateZ2Ratings(adminToken, updateRequestBody, ENGINE_LEAP_1A_QUOTATION_ID), RESPONSE_STATUS_CODE_200);

        ResponseDto updatedRatingsResponse = Z2RatingsUtil.fetchZ2RatingsResponseDto(adminToken, ENGINE_LEAP_1A_QUOTATION_ID);
        List<Double> listActualZ2RatingInput = Z2RatingsUtil.getAttributeForSpecificYear(updatedRatingsResponse, year, PartDto::getZ2RatingInput);

        listActualZ2RatingInput.forEach(actualValue -> Assert.assertEquals(actualValue, newValue));
    }

    @Test()
    public void verifyZ2Rating_CanUpdateAllPartValueInYear_Engine_CFM56_5B_PMA() throws IOException {
        String adminToken = utils.context.getContextItem(ADMIN_TOKEN);
        Double value = 0.1;
        Double newValue = 99.9;
        String year = "2025";

        ResponseDto initialRatingsResponse = Z2RatingsUtil.fetchZ2RatingsResponseDto(adminToken, ENGINE_CFM56_5B_PMA_QUOTATION_ID);

        List<Z2RatingInputDto> z2RatingInputs = Z2RatingsUtil.buildZ2RatingInputsSpecificYearAndValue(
                Z2RatingsUtil.extractFromParts(initialRatingsResponse, Function.identity()), value, initialRatingsResponse, year, newValue);

        String updateRequestBody = utils.convert.dtoToJsonString(Z2RatingsUtil.createRatingsUpdate(z2RatingInputs));
        Z2RatingsUtil.assertResponseStatusCode(Z2RatingsUtil.updateZ2Ratings(adminToken, updateRequestBody, ENGINE_CFM56_5B_PMA_QUOTATION_ID), RESPONSE_STATUS_CODE_200);

        ResponseDto updatedRatingsResponse = Z2RatingsUtil.fetchZ2RatingsResponseDto(adminToken, ENGINE_CFM56_5B_PMA_QUOTATION_ID);
        List<Double> listActualZ2RatingInput = Z2RatingsUtil.getAttributeForSpecificYear(updatedRatingsResponse, year, PartDto::getZ2RatingInput);

        listActualZ2RatingInput.forEach(actualValue -> Assert.assertEquals(actualValue, newValue));
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"})
    public void verifyZ2Rating_CanUpdateAllPartValueInYear_Engine_CFM56_5B() throws IOException {
        String adminToken = utils.context.getContextItem(ADMIN_TOKEN);
        Double value = 0.1;
        Double newValue = 99.9;
        String year = "2024";

        ResponseDto initialRatingsResponse = Z2RatingsUtil.fetchZ2RatingsResponseDto(adminToken, ENGINE_CFM56_5B_QUOTATION_ID);

        List<Z2RatingInputDto> z2RatingInputs = Z2RatingsUtil.buildZ2RatingInputsSpecificYearAndValue(
                Z2RatingsUtil.extractFromParts(initialRatingsResponse, Function.identity()), value, initialRatingsResponse, year, newValue);

        String updateRequestBody = utils.convert.dtoToJsonString(Z2RatingsUtil.createRatingsUpdate(z2RatingInputs));
        Z2RatingsUtil.assertResponseStatusCode(Z2RatingsUtil.updateZ2Ratings(adminToken, updateRequestBody, ENGINE_CFM56_5B_QUOTATION_ID), RESPONSE_STATUS_CODE_200);

        ResponseDto updatedRatingsResponse = Z2RatingsUtil.fetchZ2RatingsResponseDto(adminToken, ENGINE_CFM56_5B_QUOTATION_ID);
        List<Double> listActualZ2RatingInput = Z2RatingsUtil.getAttributeForSpecificYear(updatedRatingsResponse, year, PartDto::getZ2RatingInput);

        listActualZ2RatingInput.forEach(actualValue -> Assert.assertEquals(actualValue, newValue));
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"})
    public void verifyZ2Rating_CanUpdateAllPartValueInYear_Engine_LEAP1B() throws IOException {
        String adminToken = utils.context.getContextItem(ADMIN_TOKEN);
        Double value = 10.0;
        Double newValue = 1.0;
        String year = "2034";

        ResponseDto initialRatingsResponse = Z2RatingsUtil.fetchZ2RatingsResponseDto(adminToken, ENGINE_LEAP_1B_QUOTATION_ID);

        List<Z2RatingInputDto> z2RatingInputs = Z2RatingsUtil.buildZ2RatingInputsSpecificYearAndValue(
                Z2RatingsUtil.extractFromParts(initialRatingsResponse, Function.identity()), value, initialRatingsResponse, year, newValue);

        String updateRequestBody = utils.convert.dtoToJsonString(Z2RatingsUtil.createRatingsUpdate(z2RatingInputs));
        Z2RatingsUtil.assertResponseStatusCode(Z2RatingsUtil.updateZ2Ratings(adminToken, updateRequestBody, ENGINE_LEAP_1B_QUOTATION_ID), RESPONSE_STATUS_CODE_200);

        ResponseDto updatedRatingsResponse = Z2RatingsUtil.fetchZ2RatingsResponseDto(adminToken, ENGINE_LEAP_1B_QUOTATION_ID);
        List<Double> listActualZ2RatingInput = Z2RatingsUtil.getAttributeForSpecificYear(updatedRatingsResponse, year, PartDto::getZ2RatingInput);

        listActualZ2RatingInput.forEach(actualValue -> Assert.assertEquals(actualValue, newValue));
    }

    @Test(groups = {"smoke", "workscopeFixedPriceAndNte", "modularFixedPriceAndNte"})
    public void verifyZ2Rating_CanUpdateOnePartValueInYear_Engine_V2500() throws IOException {
        String adminToken = utils.context.getContextItem(ADMIN_TOKEN);
        Double value = 10.0;
        Double newValue = 1.0;
        String year = "2024";
        String targetPartName = "M2L - Blade LPC Stg.  2.0";

        ResponseDto initialRatingsResponse = Z2RatingsUtil.fetchZ2RatingsResponseDto(adminToken, ENGINE_V2500_QUOTATION_ID);

        List<Z2RatingInputDto> z2RatingInputs = Z2RatingsUtil.buildZ2RatingInputsSpecificYearValueAndPartName(
                Z2RatingsUtil.extractFromParts(initialRatingsResponse, Function.identity()),
                value, initialRatingsResponse, year, newValue, targetPartName);

        String updateRequestBody = utils.convert.dtoToJsonString(Z2RatingsUtil.createRatingsUpdate(z2RatingInputs));
        Z2RatingsUtil.assertResponseStatusCode(Z2RatingsUtil.updateZ2Ratings(adminToken, updateRequestBody, ENGINE_V2500_QUOTATION_ID), RESPONSE_STATUS_CODE_200);

        ResponseDto updatedRatingsResponse = Z2RatingsUtil.fetchZ2RatingsResponseDto(adminToken, ENGINE_V2500_QUOTATION_ID);
        List<Double> listActualZ2RatingInput = Z2RatingsUtil.getAttributeForSpecificYearForPartName(updatedRatingsResponse, year, targetPartName, PartDto::getZ2RatingInput);

        Assert.assertFalse(listActualZ2RatingInput.isEmpty(), RESPONSE_DATA_NULL_AS_MESSAGE);
        Assert.assertEquals(listActualZ2RatingInput.get(0), newValue);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"})
    public void verifyZ2Rating_CanUpdateOnePartValueInYear_Engine_LEAP_1A_255() throws IOException {
        String adminToken = utils.context.getContextItem(ADMIN_TOKEN);
        Double value = 10.0;
        Double newValue = 99.9;
        String year = "2026";
        String targetPartName = "SM30 - HPC Aft. Inner Case Stg. 5";

        ResponseDto initialRatingsResponse = Z2RatingsUtil.fetchZ2RatingsResponseDto(adminToken, ENGINE_LEAP_1A_QUOTATION_ID);

        List<Z2RatingInputDto> z2RatingInputs = Z2RatingsUtil.buildZ2RatingInputsSpecificYearValueAndPartName(
                Z2RatingsUtil.extractFromParts(initialRatingsResponse, Function.identity()),
                value, initialRatingsResponse, year, newValue, targetPartName);

        String updateRequestBody = utils.convert.dtoToJsonString(Z2RatingsUtil.createRatingsUpdate(z2RatingInputs));
        Z2RatingsUtil.assertResponseStatusCode(Z2RatingsUtil.updateZ2Ratings(adminToken, updateRequestBody, ENGINE_LEAP_1A_QUOTATION_ID), RESPONSE_STATUS_CODE_200);

        ResponseDto updatedRatingsResponse = Z2RatingsUtil.fetchZ2RatingsResponseDto(adminToken, ENGINE_LEAP_1A_QUOTATION_ID);
        List<Double> listActualZ2RatingInput = Z2RatingsUtil.getAttributeForSpecificYearForPartName(updatedRatingsResponse, year, targetPartName, PartDto::getZ2RatingInput);

        Assert.assertFalse(listActualZ2RatingInput.isEmpty(), RESPONSE_DATA_NULL_AS_MESSAGE);
        Assert.assertEquals(listActualZ2RatingInput.get(0), newValue);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"})
    public void verifyZ2Rating_CanUpdateOnePartValueInYear_Engine_LEAP_1B() throws IOException {
        String adminToken = utils.context.getContextItem(ADMIN_TOKEN);
        Double value = 99.9;
        Double newValue = 15.0;
        String year = "2026";

        String targetPartName = "SM30 - HPC Aft. Inner Case Stg. 5";

        ResponseDto initialRatingsResponse = Z2RatingsUtil.fetchZ2RatingsResponseDto(adminToken, ENGINE_LEAP_1B_QUOTATION_ID);

        List<Z2RatingInputDto> z2RatingInputs = Z2RatingsUtil.buildZ2RatingInputsSpecificYearValueAndPartName(
                Z2RatingsUtil.extractFromParts(initialRatingsResponse, Function.identity()),
                value, initialRatingsResponse, year, newValue, targetPartName);

        String updateRequestBody = utils.convert.dtoToJsonString(Z2RatingsUtil.createRatingsUpdate(z2RatingInputs));
        Z2RatingsUtil.assertResponseStatusCode(Z2RatingsUtil.updateZ2Ratings(adminToken, updateRequestBody, ENGINE_LEAP_1B_QUOTATION_ID), RESPONSE_STATUS_CODE_200);

        ResponseDto updatedRatingsResponse = Z2RatingsUtil.fetchZ2RatingsResponseDto(adminToken, ENGINE_LEAP_1B_QUOTATION_ID);
        List<Double> listActualZ2RatingInput = Z2RatingsUtil.getAttributeForSpecificYearForPartName(updatedRatingsResponse, year, targetPartName, PartDto::getZ2RatingInput);

        Assert.assertFalse(listActualZ2RatingInput.isEmpty(), RESPONSE_DATA_NULL_AS_MESSAGE);
        Assert.assertEquals(listActualZ2RatingInput.get(0), newValue);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"})
    public void verifyZ2Rating_CanUpdateOnePartValueInYear_Engine_CFM56_5B() throws IOException {
        String adminToken = utils.context.getContextItem(ADMIN_TOKEN);
        Double value = 99.9;
        Double newValue = 15.0;
        String year = "2024";

        String targetPartName = "M1 - Fan Disc Stg.  1";

        ResponseDto initialRatingsResponse = Z2RatingsUtil.fetchZ2RatingsResponseDto(adminToken, ENGINE_CFM56_5B_QUOTATION_ID);

        List<Z2RatingInputDto> z2RatingInputs = Z2RatingsUtil.buildZ2RatingInputsSpecificYearValueAndPartName(
                Z2RatingsUtil.extractFromParts(initialRatingsResponse, Function.identity()),
                value, initialRatingsResponse, year, newValue, targetPartName);

        String updateRequestBody = utils.convert.dtoToJsonString(Z2RatingsUtil.createRatingsUpdate(z2RatingInputs));
        Z2RatingsUtil.assertResponseStatusCode(Z2RatingsUtil.updateZ2Ratings(adminToken, updateRequestBody, ENGINE_CFM56_5B_QUOTATION_ID), RESPONSE_STATUS_CODE_200);

        ResponseDto updatedRatingsResponse = Z2RatingsUtil.fetchZ2RatingsResponseDto(adminToken, ENGINE_CFM56_5B_QUOTATION_ID);
        List<Double> listActualZ2RatingInput = Z2RatingsUtil.getAttributeForSpecificYearForPartName(updatedRatingsResponse, year, targetPartName, PartDto::getZ2RatingInput);

        Assert.assertFalse(listActualZ2RatingInput.isEmpty(), RESPONSE_DATA_NULL_AS_MESSAGE);
        Assert.assertEquals(listActualZ2RatingInput.get(0), newValue);
    }
}
