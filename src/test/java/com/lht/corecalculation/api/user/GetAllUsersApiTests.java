package com.lht.corecalculation.api.user;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.lht.corecalculation.api.pojo.dto.common.ErrorDto;
import com.lht.corecalculation.api.pojo.dto.common.GenericErrorDto;
import com.lht.corecalculation.api.pojo.dto.inner.User;
import com.lht.corecalculation.api.pojo.dto.user.UsersDto;
import com.lht.corecalculation.api.pojo.entity.user.UserEntity;
import com.lht.corecalculation.api.request.user.GetAllUsersRequest;
import com.lht.corecalculation.base.BaseCocaApiTest;
import io.restassured.response.Response;
import java.util.List;
import org.testng.Assert;
import org.testng.annotations.Test;
import org.testng.asserts.SoftAssert;


import static com.lht.corecalculation.api.constants.GlobalConstants.ERROR_DETAILS_MISMATCH_AS_MESSAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.ERROR_STATUS_MISMATCH_AS_MESSAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.ERROR_TYPE_MISMATCH_AS_MESSAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.FORBIDDEN;
import static com.lht.corecalculation.api.constants.GlobalConstants.GENERIC_ERROR_DESCRIPTION_MISMATCH_AS_MESSAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.GENERIC_ERROR_MESSAGE_MISMATCH_AS_MESSAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_200;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_401;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_403;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_USERS_COUNT_MISMATCH_AS_MESSAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_USER_IDS_NOT_UNIQUE_AS_MESSAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_USER_ID_NOT_FOUND_IN_DB_AS_MESSAGE_FORMAT;
import static com.lht.corecalculation.api.constants.GlobalConstants.UNAUTHORIZED;
import static com.lht.corecalculation.api.constants.GlobalConstants.USER_DOESNT_HAVE_REQUIRED_PERMISSIONS;

public class GetAllUsersApiTests extends BaseCocaApiTest {

    @Test(dataProvider = "adminAccessTokenProvider")
    public void getAllUsers_withValidTokenReturnsCorrectData_positive(String token) throws JsonProcessingException {
        GetAllUsersRequest getAllUsersRequest = new GetAllUsersRequest().withBearerToken(token);

        Response response = getAllUsersRequest.callAPI();

        // response status code correct
        var soft = new SoftAssert();
        soft.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200);

        UsersDto usersDto = utils.convert.jsonToDto(response, UsersDto.class);
        List<UserEntity> userResults = dbGetAllPersistedUsers();

        // returned results count correct
        soft.assertEquals(usersDto.getUsers().size(), userResults.size(),
                RESPONSE_USERS_COUNT_MISMATCH_AS_MESSAGE);

        // IDs unique
        soft.assertTrue(usersDto.getUsers().stream()
                        .map(User::getId)
                        .distinct()
                        .count() == usersDto.getUsers().size(),
                RESPONSE_USER_IDS_NOT_UNIQUE_AS_MESSAGE);

        // IDs match DB
        usersDto.getUsers().stream()
                .map(User::getId)
                .forEach(id -> soft.assertTrue(userResults.stream().anyMatch(ur -> ur.getId().equals(id)),
                        String.format(RESPONSE_USER_ID_NOT_FOUND_IN_DB_AS_MESSAGE_FORMAT, id)));

        soft.assertAll();
    }

    @Test(dataProvider = "viewOnlyAccessTokenProvider")
    public void getAllUsers_withViewOnlyRoleToken_ReturnsEmptyList_positive(String token) throws JsonProcessingException {
        GetAllUsersRequest getAllUsersRequest = new GetAllUsersRequest().withBearerToken(token);

        Response response = getAllUsersRequest.callAPI();
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200);

        UsersDto usersDto = utils.convert.jsonToDto(response, UsersDto.class);
        var soft = new SoftAssert();
        soft.assertTrue(usersDto.getUsers().isEmpty(), "View only user should receive an empty list of possible new owners");
        soft.assertNull(usersDto.getError(), "There's an error property present in a 200 response object");
        soft.assertAll();
    }

    @Test(dataProvider = "noRoleAccessTokenProvider")
    public void getAllUsers_withNoRoleTokenIsForbidden_negative(String token) throws JsonProcessingException {
        GetAllUsersRequest getAllUsersRequest = new GetAllUsersRequest().withBearerToken(token);

        Response response = getAllUsersRequest.callAPI();
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_401);

        ErrorDto errorDto = utils.convert.jsonToDto(response, ErrorDto.class);
        var soft = new SoftAssert();
        soft.assertEquals(errorDto.getError().getStatus(), RESPONSE_STATUS_CODE_401, ERROR_STATUS_MISMATCH_AS_MESSAGE);
        soft.assertEquals(errorDto.getError().getType(), UNAUTHORIZED, ERROR_TYPE_MISMATCH_AS_MESSAGE);
        soft.assertTrue(errorDto.getError().getDetail().startsWith(USER_DOESNT_HAVE_REQUIRED_PERMISSIONS), ERROR_DETAILS_MISMATCH_AS_MESSAGE);
        soft.assertAll();
    }

    @Test(dataProvider = "expiredAccessTokenProvider")
    public void getAllUsers_withExpiredTokenIsUnauthorized_negative(String token) throws JsonProcessingException {
        GetAllUsersRequest getAllUsersRequest = new GetAllUsersRequest().withBearerToken(token);

        Response response = getAllUsersRequest.callAPI();
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_401);
    }

    @Test()
    public void getAllUsers_withoutTokenIsUnauthorized_negative() throws JsonProcessingException {
        GetAllUsersRequest getAllUsersRequest = new GetAllUsersRequest();

        Response response = getAllUsersRequest.callAPI();
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_401);

        GenericErrorDto errorDto = utils.convert.jsonToDto(response, GenericErrorDto.class);
        var soft = new SoftAssert();
        soft.assertEquals(errorDto.getStatus(), "401", ERROR_STATUS_MISMATCH_AS_MESSAGE);
        soft.assertEquals(errorDto.getError(), "Unauthorized", GENERIC_ERROR_DESCRIPTION_MISMATCH_AS_MESSAGE);
        soft.assertEquals(errorDto.getMessage(), "Full authentication is required to access this resource", GENERIC_ERROR_MESSAGE_MISMATCH_AS_MESSAGE);
        soft.assertAll();
    }
}
