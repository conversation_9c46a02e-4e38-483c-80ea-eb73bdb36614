package com.lht.corecalculation.api.user;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.lht.corecalculation.api.enums.TestUser;
import com.lht.corecalculation.api.pojo.dto.common.ErrorDto;
import com.lht.corecalculation.api.pojo.dto.common.GenericErrorDto;
import com.lht.corecalculation.api.pojo.dto.common.PermissionsDto;
import com.lht.corecalculation.api.pojo.dto.user.UpdateUserInfoDto;
import com.lht.corecalculation.api.pojo.entity.user.UserEntity;
import com.lht.corecalculation.api.request.user.UpdateUserDetailsRequest;
import com.lht.corecalculation.base.BaseCocaApiTest;
import io.restassured.response.Response;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import org.testng.Assert;
import org.testng.annotations.Test;
import org.testng.asserts.SoftAssert;


import static com.lht.corecalculation.api.constants.GlobalConstants.ERROR_DETAILS_MISMATCH_AS_MESSAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.ERROR_STATUS_MISMATCH_AS_MESSAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.ERROR_TYPE_MISMATCH_AS_MESSAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.FORBIDDEN;
import static com.lht.corecalculation.api.constants.GlobalConstants.GENERIC_ERROR_DESCRIPTION_MISMATCH_AS_MESSAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.GENERIC_ERROR_MESSAGE_MISMATCH_AS_MESSAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.PERMISSIONS_FOR_RESOURCE_NOT_PRESENT_AS_MESSAGE_FORMAT;
import static com.lht.corecalculation.api.constants.GlobalConstants.PERMISSIONS_NOT_MATCHING_AS_MESSAGE_FORMAT;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_200;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_401;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_403;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.UNAUTHORIZED;
import static com.lht.corecalculation.api.constants.GlobalConstants.USER_DOESNT_HAVE_REQUIRED_PERMISSIONS;
import static com.lht.corecalculation.api.enums.CocaResource.FILTERS;
import static com.lht.corecalculation.api.enums.CocaResource.PROJECT;
import static com.lht.corecalculation.api.enums.CocaResource.QUOTATION;
import static com.lht.corecalculation.api.enums.CocaResource.USER;
import static com.lht.corecalculation.api.enums.TestUser.TEST_USER_1;
import static com.lht.corecalculation.api.enums.TestUser.TEST_USER_3;
import static com.lht.corecalculation.api.enums.TestUser.VIEW_ONLY;

public class PutUserDetailsApiTests extends BaseCocaApiTest {

    @Test(dataProvider = "adminAccessTokenProvider")
    public void putUserDetails_WithAdminUserToken_ReturnsCorrectPermissions_Positive(String token) throws JsonProcessingException {
        UpdateUserDetailsRequest updateUserDetailsRequest = new UpdateUserDetailsRequest().withBearerToken(token);

        Response response = updateUserDetailsRequest.callAPI();
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        UpdateUserInfoDto updateUserInfoDto = utils.convert.jsonToDto(response, UpdateUserInfoDto.class);
        var soft = new SoftAssert();

        assertPermissions(soft, updateUserInfoDto, PROJECT.name(), true, true, true, true);
        assertPermissions(soft, updateUserInfoDto, USER.name(), true, true, true, true);
        assertPermissions(soft, updateUserInfoDto, QUOTATION.name(), true, true, true, true);
        assertPermissions(soft, updateUserInfoDto, FILTERS.name(), false, true, false, false);

        soft.assertNull(updateUserInfoDto.getError());

        soft.assertAll();
    }

    @Test(dataProvider = "calcAccessTokenProvider")
    public void putUserDetails_WithCalculationUserToken_ReturnsCorrectPermissions_Positive(String token) throws JsonProcessingException {
        UpdateUserDetailsRequest updateUserDetailsRequest = new UpdateUserDetailsRequest().withBearerToken(token);

        Response response = updateUserDetailsRequest.callAPI();
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        UpdateUserInfoDto updateUserInfoDto = utils.convert.jsonToDto(response, UpdateUserInfoDto.class);
        var soft = new SoftAssert();

        assertPermissions(soft, updateUserInfoDto, PROJECT.name(), true, true, true, false);
        assertPermissions(soft, updateUserInfoDto, USER.name(), true, true, true, false);
        assertPermissions(soft, updateUserInfoDto, QUOTATION.name(), true, true, true, true);
        assertPermissions(soft, updateUserInfoDto, FILTERS.name(), false, true, false, false);

        soft.assertNull(updateUserInfoDto.getError());

        soft.assertAll();
    }

    @Test(dataProvider = "viewOnlyAccessTokenProvider")
    public void putUserDetails_WithViewOnlyToken_ReturnsCorrectPermissions_And_DoesNotPersistUserDetails_Positive(String token) throws JsonProcessingException {
        List<Long> dbUserIdsBeforeCall = dbGetAllPersistedUsers().stream()
                .map(UserEntity::getId)
                .collect(Collectors.toList());

        UpdateUserDetailsRequest updateUserDetailsRequest = new UpdateUserDetailsRequest().withBearerToken(token);

        Response response = updateUserDetailsRequest.callAPI();
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        UpdateUserInfoDto updateUserInfoDto = utils.convert.jsonToDto(response, UpdateUserInfoDto.class);

        List<Long> dbUserIdsAfterCall = dbGetAllPersistedUsers().stream()
                .map(UserEntity::getId)
                .collect(Collectors.toList());

        var soft = new SoftAssert();

        soft.assertEquals(dbUserIdsAfterCall, dbUserIdsBeforeCall);

        soft.assertNull(updateUserInfoDto.getData().getId());

        soft.assertNotNull(updateUserInfoDto.getData().getName());

        soft.assertNotNull(updateUserInfoDto.getData().getEmail());

        soft.assertNotNull(updateUserInfoDto.getData().getUsername());

        PermissionsDto projectPermissions = getPermissionsByResourceName(updateUserInfoDto, PROJECT.name(), Optional.of(soft));
        soft.assertTrue(permissionsMatch(projectPermissions, false, true, false, false),
                String.format(PERMISSIONS_NOT_MATCHING_AS_MESSAGE_FORMAT,
                        updateUserInfoDto.getData().getUsername(),
                        projectPermissions.getResource().getName()));

        PermissionsDto userPermissions = getPermissionsByResourceName(updateUserInfoDto, USER.name(), Optional.of(soft));
        soft.assertTrue(permissionsMatch(userPermissions, false, true, false, false),
                String.format(PERMISSIONS_NOT_MATCHING_AS_MESSAGE_FORMAT,
                        updateUserInfoDto.getData().getUsername(),
                        userPermissions.getResource().getName()));

        PermissionsDto quotationPermissions = getPermissionsByResourceName(updateUserInfoDto, QUOTATION.name(), Optional.of(soft));
        soft.assertTrue(permissionsMatch(quotationPermissions, false, true, false, false),
                String.format(PERMISSIONS_NOT_MATCHING_AS_MESSAGE_FORMAT,
                        updateUserInfoDto.getData().getUsername(),
                        quotationPermissions.getResource().getName()));

        PermissionsDto filtersPermissions = getPermissionsByResourceName(updateUserInfoDto, FILTERS.name(), Optional.of(soft));
        soft.assertTrue(permissionsMatch(filtersPermissions, false, true, false, false),
                String.format(PERMISSIONS_NOT_MATCHING_AS_MESSAGE_FORMAT,
                        updateUserInfoDto.getData().getUsername(),
                        filtersPermissions.getResource().getName()));

        soft.assertNull(updateUserInfoDto.getError());

        soft.assertAll();
    }

    @Test(dependsOnMethods = "putUserDetails_WithCalculationUserToken_ReturnsCorrectPermissions_Positive")
    public void putUserDetails_WithCalculationUserToken_PersistsUserDetailsWhenNotPresent_Positive() throws JsonProcessingException {
        long id = dbGetUserIdByName(TEST_USER_1.getName());
        dbTransferOwnershipToAdmin(String.valueOf(id));

        int entitiesDeleted = dbDeleteUserByUNumber(TEST_USER_1.getUNumber());
        Assert.assertEquals(entitiesDeleted, 1);

        String token = utils.context.getContextItem(TEST_USER_1.getContextKey());
        UpdateUserDetailsRequest updateUserDetailsRequest = new UpdateUserDetailsRequest().withBearerToken(token);

        Response response = updateUserDetailsRequest.callAPI();
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        UpdateUserInfoDto updateUserInfoDto = utils.convert.jsonToDto(response, UpdateUserInfoDto.class);

        var soft = new SoftAssert();
        UserEntity dbUser = dbGetUserById(updateUserInfoDto.getData().getId());

        // assert entry is persisted in DB
        soft.assertEquals(updateUserInfoDto.getData().getUsername().toLowerCase(), dbUser.getUNumber().toLowerCase());
        soft.assertEquals(updateUserInfoDto.getData().getId(), dbUser.getId());
        soft.assertEquals(updateUserInfoDto.getData().getName(), dbUser.getFullName());
        soft.assertEquals(updateUserInfoDto.getData().getEmail(), dbUser.getEmail());
        soft.assertEquals(updateUserInfoDto.getData().getUsername().toLowerCase(), dbUser.getUNumber().toLowerCase());
        soft.assertNull(updateUserInfoDto.getError());

        soft.assertAll();
    }

    @Test(enabled = false)
    public void putUserDetails_WithCalculationUserToken_UpdatesUserDetailsWhenPresent_Positive() throws JsonProcessingException {
        TestUser calcUser = TEST_USER_3;

        String token = utils.context.getContextItem(calcUser.getContextKey());
        int entitiesUpdated = dbSetDummyUserDetailsByUNumber(calcUser.getUNumber());
        Assert.assertEquals(entitiesUpdated, 1);

        UpdateUserDetailsRequest updateUserDetailsRequest = new UpdateUserDetailsRequest().withBearerToken(token);

        Response response = updateUserDetailsRequest.callAPI();
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        UpdateUserInfoDto updateUserInfoDto = utils.convert.jsonToDto(response, UpdateUserInfoDto.class);
        UserEntity userEntity = dbGetUserById(updateUserInfoDto.getData().getId());

        var soft = new SoftAssert();

        // assert entry is persisted in DB
        soft.assertNotNull(updateUserInfoDto.getData().getId());
        soft.assertEquals(updateUserInfoDto.getData().getId(), userEntity.getId());
        // assert name present
        soft.assertNotNull(updateUserInfoDto.getData().getName());
        // assert mail present
        soft.assertNotNull(updateUserInfoDto.getData().getEmail());
        // assert username present
        soft.assertNotNull(updateUserInfoDto.getData().getUsername());
        // assert no errors
        soft.assertNull(updateUserInfoDto.getError());

        soft.assertAll();
    }

    @Test()
    public void putUserDetails_WithExCalculationUserToken_DeletesUserDetailsWhenRoleNoLongerPresent_Positive() throws JsonProcessingException {
        dbInsertUser("Testuser 2 Core Calculation", "somemail.mail.com", "m002412");

        String token = utils.context.getContextItem(VIEW_ONLY.getContextKey());
        UpdateUserDetailsRequest updateUserDetailsRequest = new UpdateUserDetailsRequest().withBearerToken(token);

        Response response = updateUserDetailsRequest.callAPI();
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        UpdateUserInfoDto updateUserInfoDto = utils.convert.jsonToDto(response, UpdateUserInfoDto.class);
        var soft = new SoftAssert();

        // assert entry is persisted in DB // TODO: 21.04.23 direct db assertion via query
        soft.assertNull(updateUserInfoDto.getData().getId());
        // assert name present
        soft.assertNotNull(updateUserInfoDto.getData().getName());
        // assert mail present
        soft.assertNotNull(updateUserInfoDto.getData().getEmail());
        // assert username present
        soft.assertNotNull(updateUserInfoDto.getData().getUsername());
        // assert no errors
        soft.assertNull(updateUserInfoDto.getError());

        soft.assertAll();
    }

    @Test()
    public void putUserDetails_WithoutToken_IsUnauthorized_Negative() throws JsonProcessingException {
        UpdateUserDetailsRequest updateUserDetailsRequest = new UpdateUserDetailsRequest();

        Response response = updateUserDetailsRequest.callAPI();
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_401, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        GenericErrorDto errorDto = utils.convert.jsonToDto(response, GenericErrorDto.class);
        var soft = new SoftAssert();
        soft.assertEquals(errorDto.getStatus(), "401", ERROR_STATUS_MISMATCH_AS_MESSAGE);
        soft.assertEquals(errorDto.getError(), "Unauthorized", GENERIC_ERROR_DESCRIPTION_MISMATCH_AS_MESSAGE);
        soft.assertEquals(errorDto.getMessage(), "Full authentication is required to access this resource", GENERIC_ERROR_MESSAGE_MISMATCH_AS_MESSAGE);
        soft.assertAll();
    }

    @Test(dataProvider = "expiredAccessTokenProvider")
    public void putUserDetails_WithExpiredToken_IsUnauthorized_Negative(String token) throws JsonProcessingException {
        UpdateUserDetailsRequest updateUserDetailsRequest = new UpdateUserDetailsRequest().withBearerToken(token);

        Response response = updateUserDetailsRequest.callAPI();
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_401, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
    }

    @Test(dataProvider = "noRoleAccessTokenProvider")
    public void putUserDetails_WithNoRoleToken_IsForbidden_Negative(String token) throws JsonProcessingException {
        UpdateUserDetailsRequest updateUserDetailsRequest = new UpdateUserDetailsRequest().withBearerToken(token);

        Response response = updateUserDetailsRequest.callAPI();
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_401, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        ErrorDto errorDto = utils.convert.jsonToDto(response, ErrorDto.class);
        var soft = new SoftAssert();
        soft.assertEquals(errorDto.getError().getStatus(), RESPONSE_STATUS_CODE_401, ERROR_STATUS_MISMATCH_AS_MESSAGE);
        soft.assertEquals(errorDto.getError().getType(), UNAUTHORIZED, ERROR_TYPE_MISMATCH_AS_MESSAGE);
        soft.assertTrue(errorDto.getError().getDetail().startsWith(USER_DOESNT_HAVE_REQUIRED_PERMISSIONS), ERROR_DETAILS_MISMATCH_AS_MESSAGE);
        soft.assertAll();
    }

    private PermissionsDto getPermissionsByResourceName(
            UpdateUserInfoDto updateUserInfoDto,
            String resourceName,
            Optional<SoftAssert> softAssert
    ) {
        PermissionsDto permissions = updateUserInfoDto.getData().getPermissions().stream()
                .filter(p -> p.getResource().getName().equalsIgnoreCase(resourceName))
                .findAny()
                .orElse(null);

        if (permissions == null) {
            softAssert.ifPresent(SoftAssert::assertAll);
            Assert.fail(String.format(PERMISSIONS_FOR_RESOURCE_NOT_PRESENT_AS_MESSAGE_FORMAT, resourceName));
        }

        return permissions;
    }

    private boolean permissionsMatch(PermissionsDto permissions, boolean canCreate, boolean canRead, boolean canUpdate, boolean canDelete) {
        if (permissions == null || permissions.getScopeHolder() == null) {
            return false;
        }
        return permissions.getScopeHolder().isCreate() == canCreate
                && permissions.getScopeHolder().isRead() == canRead
                && permissions.getScopeHolder().isUpdate() == canUpdate
                && permissions.getScopeHolder().isDelete() == canDelete;
    }

    private void assertPermissions(
            SoftAssert soft,
            UpdateUserInfoDto updateUserInfoDto,
            String resourceName,
            boolean canCreate,
            boolean canRead,
            boolean canUpdate,
            boolean canDelete
    ) {
        PermissionsDto permissions = getPermissionsByResourceName(updateUserInfoDto, resourceName, Optional.of(soft));
        if (permissions == null || permissions.getScopeHolder() == null) {
            soft.fail(String.format("Permissions for %s are null.", resourceName));
        } else {
            boolean match = permissionsMatch(permissions, canCreate, canRead, canUpdate, canDelete);
            soft.assertTrue(match,
                    String.format(PERMISSIONS_NOT_MATCHING_AS_MESSAGE_FORMAT,
                            updateUserInfoDto.getData().getUsername(),
                            resourceName));
        }
    }
}
