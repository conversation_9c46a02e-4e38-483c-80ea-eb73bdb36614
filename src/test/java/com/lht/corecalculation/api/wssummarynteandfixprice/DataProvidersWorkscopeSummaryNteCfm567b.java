package com.lht.corecalculation.api.wssummarynteandfixprice;

import com.lht.corecalculation.base.BaseCocaApiTest;
import java.util.Arrays;
import org.testng.annotations.DataProvider;


import static com.lht.corecalculation.api.constants.GlobalConstants.WS1;
import static com.lht.corecalculation.api.constants.GlobalConstants.WS2;

public class DataProvidersWorkscopeSummaryNteCfm567b extends BaseCocaApiTest {

    @DataProvider(name = "summaryProductionCostNteCfm567b")
    public Object[][] workscopeSummaryProductionCostDataProviderNteCfm567b() {
        return new Object[][] {
                {
                        WS1,
                        Arrays.asList(2338151.0, 2452850.0, 2563360.0),
                        Arrays.asList(1069330.0, 1135615.0, 1204832.0),
                        Arrays.asList(3407481.0, 3588465.0, 3768192.0)
                },
                {
                        WS2,
                        Arrays.asList(2401434.0, 2519209.0, 2629703.0),
                        Arrays.asList(519470.0, 551774.0, 584295.0),
                        Arrays.asList(2920904.0, 3070984.0, 3213999.0)
                }
        };
    }

    @DataProvider(name = "summaryDiscountsNteCfm567b")
    public Object[][] workscopeSummaryDiscountsDataProviderNteCfm567b() {
        return new Object[][] {
                {
                        WS1,
                        Arrays.asList(206702.0, 227539.0, 249807.0),
                        Arrays.asList(110478.0, 128983.0, 149273.0),
                        Arrays.asList(317180.0, 356522.0, 399081.0)
                },
                {
                        WS2,
                        Arrays.asList(159657.0, 177601.0, 196582.0),
                        Arrays.asList(43942.0, 51607.0, 60036.0),
                        Arrays.asList(203600.0, 229209.0, 256618.0)
                }
        };
    }

    @DataProvider(name = "summaryRevenueNteCfm567b")
    public Object[][] workscopeSummaryRevenueDataProviderNteCfm567b() {
        return new Object[][] {
                {
                        WS1,
                        Arrays.asList(3401320.0, 3616149.0, 3844714.0),
                        Arrays.asList(1170673.0, 1243617.0, 1320420.0),
                        Arrays.asList(4571994.0, 4859766.0, 5165134.0)
                },
                {
                        WS2,
                        Arrays.asList(3505587.0, 3721187.0, 3949786.0),
                        Arrays.asList(597192.0, 634505.0, 673295.0),
                        Arrays.asList(4102780.0, 4355693.0, 4623081.0)
                }
        };
    }

    @DataProvider(name = "summarySurchargesNtePriceCfm567b")
    public Object[][] workscopeSummarySurchargesCostDataProviderNteCfm567b() {
        return new Object[][] {
                {
                        WS1,
                        Arrays.asList(240233.0, 223181.0, 213521.0),
                        Arrays.asList(109868.0, 103328.0, 100359.0),
                        Arrays.asList(350102.0, 326509.0, 313881.0)
                },
                {
                        WS2,
                        Arrays.asList(281755.0, 261392.0, 249920.0),
                        Arrays.asList(60948.0, 57251.0, 55530.0),
                        Arrays.asList(342703.0, 318644.0, 305450.0)
                }
        };
    }

    @DataProvider(name = "summaryProdCostInclDiscountsAndSurchargesNteCfm567b")
    public Object[][] workscopeSummaryProdCostInclDiscountsAndSurchargesDataProviderNteCfm567b() {
        return new Object[][] {
                {
                        WS1,
                        Arrays.asList(2371682.0, 2448491.0, 2527074.0),
                        Arrays.asList(1068720.0, 1109960.0, 1155919.0),
                        Arrays.asList(3440402.0, 3558452.0, 3682993.0)
                },
                {
                        WS2,
                        Arrays.asList(2523532.0, 2602999.0, 2683041.0),
                        Arrays.asList(536476.0, 557418.0, 579789.0),
                        Arrays.asList(3060008.0, 3160418.0, 3262830.0)
                }
        };
    }

    @DataProvider(name = "summaryDb2ValuesNteCfm567b")
    public Object[][] workscopeSummaryDb2ValuesDataProviderNteCfm567b() {
        return new Object[][] {
                {
                        WS1,
                        Arrays.asList(1029638.0, 1167657.0, 1317639.0),
                        Arrays.asList(101953.0, 133657.0, 164501.0),
                        Arrays.asList(1131591.0, 1301314.0, 1482141.0)
                },
                {
                        WS2,
                        Arrays.asList(982055.0, 1118188.0, 1266744.0),
                        Arrays.asList(60716.0, 77086.0, 93506.0),
                        Arrays.asList(1042771.0, 1195274.0, 1360250.0)
                }
        };
    }

    @DataProvider(name = "summaryDb2PercentageValuesNteCfm567b")
    public Object[][] workscopeDb2PercentageValuesDataProviderNteCfm567b() {
        return new Object[][] {
                {
                        WS1,
                        Arrays.asList(30.271713, 32.290077, 34.271465),
                        Arrays.asList(8.708955, 10.747466, 12.458260),
                        Arrays.asList(24.750502, 26.777308, 28.695114)
                },
                {
                        WS2,
                        Arrays.asList(28.014006, 30.049229, 32.071228),
                        Arrays.asList(10.166920, 12.149037, 13.887813),
                        Arrays.asList(25.41622, 27.441662, 29.423033)
                }
        };
    }

    @DataProvider(name = "summaryEbitValuesNteCfm567b")
    public Object[][] workscopeSummaryEbitValuesDataProviderNteCfm567b() {
        return new Object[][] {
                {
                        WS1,
                        Arrays.asList(691506.0, 778433.0, 1004772.0),
                        Arrays.asList(-52687.0, -46544.0, 17447.0),
                        Arrays.asList(638819.0, 731888.0, 1022220.0)
                },
                {
                        WS2,
                        Arrays.asList(576920.0, 651073.0, 890436.0),
                        Arrays.asList(-26921.0, -25224.0, 9893.0),
                        Arrays.asList(549999.0, 625848.0, 900329.0)
                }
        };
    }

    @DataProvider(name = "summaryEbitPercentageValuesNteCfm567b")
    public Object[][] workscopeEbitPercentageValuesDataProviderNteCfm567b() {
        return new Object[][] {
                {
                        WS1,
                        Arrays.asList(20.330532, 21.526587, 26.133877),
                        Arrays.asList(-4.500620, -3.742690, 1.321352),
                        Arrays.asList(13.972437, 15.060161, 19.790777)
                },
                {
                        WS2,
                        Arrays.asList(16.457169, 17.496378, 22.54391),
                        Arrays.asList(-4.507990, -3.975472, 1.469447),
                        Arrays.asList(13.405522, 14.368517, 19.474669)
                }
        };
    }

    @DataProvider(name = "summaryEatPercentageValuesNteCfm567b")
    public Object[][] workscopeEatPercentageValuesDataProviderNteCfm567b() {
        return new Object[][] {
                {
                        WS1,
                        Arrays.asList(15.247899, 16.14494, 19.600409),
                        Arrays.asList(-3.375465, -2.807017, 0.991014),
                        Arrays.asList(10.479328, 11.295121, 14.843083)
                },
                {
                        WS2,
                        Arrays.asList(12.342877, 13.122284, 16.907932),
                        Arrays.asList(-3.380993, -2.981604, 1.102085),
                        Arrays.asList(10.054142, 10.776388, 14.606001)
                }
        };
    }

    @DataProvider(name = "summaryNetMarginPercentageNteCfm567b")
    public Object[][] workscopeNetMarginPercentageDataProviderNteCfm567b() {
        return new Object[][] {
                {
                        WS1,
                        Arrays.asList(12.847899, 13.74494, 17.200409),
                        Arrays.asList(-5.775465, -5.207017, -1.408986),
                        Arrays.asList(8.079328, 8.895121, 12.443083)
                },
                {
                        WS2,
                        Arrays.asList(9.942877, 10.722284, 14.507933),
                        Arrays.asList(-5.780993, -5.381604, -1.297915),
                        Arrays.asList(7.654142, 8.376388, 12.206001)
                }
        };
    }
}