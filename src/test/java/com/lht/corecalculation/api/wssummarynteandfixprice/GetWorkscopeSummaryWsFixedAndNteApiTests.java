package com.lht.corecalculation.api.wssummarynteandfixprice;

import com.lht.corecalculation.api.enums.PartType;
import com.lht.corecalculation.api.request.wssummary.PostWorkscopeSummaryRequest;
import com.lht.corecalculation.api.utils.WorkscopeUtil;
import com.lht.corecalculation.base.BaseCocaApiTest;
import io.restassured.response.Response;
import java.io.IOException;
import org.testng.annotations.Test;


import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_CFM56_5B_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_CFM56_7B_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_V2500_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.FAN_BLADE;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_200;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_400;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE;
import static com.lht.corecalculation.api.utils.WorkscopeUtil.initializeWorkscopeAndAwaitCompletionThenFetchSummary;
import static org.testng.Assert.assertEquals;

public class GetWorkscopeSummaryWsFixedAndNteApiTests extends BaseCocaApiTest {

    @Test(groups = {"smoke", "workscopeFixedPriceAndNte"}, dataProvider = "adminAccessTokenProvider")
    public void verifyWorkscopeSummary_ReturnResults_AfterAllStepsAreValid_AdminUser(String accessToken) throws IOException, InterruptedException, WorkscopeUtil.ApiException {
        WorkscopeUtil.updateEngineValues(ENGINE_V2500_QUOTATION_ID, accessToken,
                3, 3, 3, 3, 2500, 4000,
                75.0,
                60, 60, 0.0,
                10.0, false,
                10.0, false,
                3.0, 2500,
                3.0, 5.0, 0.0, 6.28, 6.28, 33.33);

        Response response = initializeWorkscopeAndAwaitCompletionThenFetchSummary(ENGINE_V2500_QUOTATION_ID, accessToken);
        assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = "workscopeFixedPriceAndNte", dataProvider = "adminAccessTokenProvider")
    public void verifyWorkscopeSummary_CannotOpen_WithInvalidStep_InMaterialZ2Pricing(String accessToken) throws IOException {
        WorkscopeUtil.updateEngineValues(ENGINE_V2500_QUOTATION_ID, accessToken,
                3, 3, 3, 3, 2500, 4000,
                null,
                60, 60, 0.0,
                10.0, false,
                10.0, false,
                3.0, 2500,
                3.0, 5.0, 0.0, 6.28, 6.28, 33.33);

        PostWorkscopeSummaryRequest postWorkscopeSummaryRequest = new PostWorkscopeSummaryRequest(ENGINE_V2500_QUOTATION_ID)
                .withBearerToken(accessToken);
        Response response = postWorkscopeSummaryRequest.callAPI();
        assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_400, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = "workscopeFixedPriceAndNte", dataProvider = "adminAccessTokenProvider")
    public void verifyWorkscopeSummary_CannotOpen_WithInvalidStep_InLabourRate(String accessToken) throws IOException {
        WorkscopeUtil.updateEngineValues(ENGINE_V2500_QUOTATION_ID, accessToken,
                0, 3, 3, 3, 2500, 4000,
                80.0,
                null, 60, 0.0,
                10.0, false,
                10.0, false,
                3.0, 2500,
                3.0, 5.0, 0.0, 6.28, 6.28, 33.33);

        PostWorkscopeSummaryRequest postWorkscopeSummaryRequest = new PostWorkscopeSummaryRequest(ENGINE_V2500_QUOTATION_ID)
                .withBearerToken(accessToken);
        Response response = postWorkscopeSummaryRequest.callAPI();
        assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_400, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = "workscopeFixedPriceAndNte", dataProvider = "adminAccessTokenProvider")
    public void verifyWorkscopeSummary_CannotOpen_WithInvalidStep_InSubcontractPricingHC(String accessToken) throws IOException {
        WorkscopeUtil.updateEngineValues(ENGINE_V2500_QUOTATION_ID, accessToken,
                3.0, 3.0, 3.0, 3.0, 2500, 4000,
                80.0,
                60, 60, 0.0,
                10.0, false,
                10.0, false,
                null, 2500,
                3.0, 5.0, 0.0, 6.28, 6.28, 33.33);

        PostWorkscopeSummaryRequest postWorkscopeSummaryRequest = new PostWorkscopeSummaryRequest(ENGINE_V2500_QUOTATION_ID)
                .withBearerToken(accessToken);
        Response response = postWorkscopeSummaryRequest.callAPI();
        assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_400, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = "workscopeFixedPriceAndNte", dataProvider = "adminAccessTokenProvider")
    public void verifyWorkscopeSummary_ReturnResults_AfterAllStepsAreValid_AdminUser_Cfm56_5b(String accessToken) throws IOException, InterruptedException, WorkscopeUtil.ApiException {
        WorkscopeUtil.updateEngineValuesNteAndFixed(ENGINE_CFM56_5B_QUOTATION_ID, accessToken,
                3.3f, 123456,
                3, 3, 3, 3, 2500, 4000,
                75.0,
                PartType.LLP,
                60, 60, 5.0,
                10.0, true,
                10.0, false,
                3.0, 2500,
                FAN_BLADE, true,
                3.0, 5.0, 11.11, 6.28, 6.28, 33.33);

        Response response = initializeWorkscopeAndAwaitCompletionThenFetchSummary(ENGINE_CFM56_5B_QUOTATION_ID, accessToken);
        assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = "workscopeFixedPriceAndNte", dataProvider = "adminAccessTokenProvider")
    public void verifyWorkscopeSummary_ReturnResults_AfterAllStepsAreValid_AdminUser_Cfm56_7b(String accessToken) throws IOException, InterruptedException, WorkscopeUtil.ApiException {
        WorkscopeUtil.updateEngineValuesNteAndFixed(ENGINE_CFM56_7B_QUOTATION_ID, accessToken,
                3.3f, 123456,
                3, 3, 3, 3, 2500, 4000,
                75.0,
                PartType.CASE_AND_FRAME,
                60, 60, 5.0,
                10.0, true,
                10.0, false,
                3.0, 2500,
                FAN_BLADE, true,
                3.0, 5.0, 11.11, 6.28, 6.28, 33.33);

        Response response = initializeWorkscopeAndAwaitCompletionThenFetchSummary(ENGINE_CFM56_7B_QUOTATION_ID, accessToken);
        assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
    }
}
