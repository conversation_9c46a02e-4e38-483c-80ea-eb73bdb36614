package com.lht.corecalculation.api.wssummarynteandfixprice;

import com.lht.corecalculation.base.BaseCocaApiTest;
import java.util.Arrays;
import org.testng.annotations.DataProvider;


import static com.lht.corecalculation.api.constants.GlobalConstants.WS01;
import static com.lht.corecalculation.api.constants.GlobalConstants.WS02;
import static com.lht.corecalculation.api.constants.GlobalConstants.WS03;
import static com.lht.corecalculation.api.constants.GlobalConstants.WS03_1;
import static com.lht.corecalculation.api.constants.GlobalConstants.WS04;
import static com.lht.corecalculation.api.constants.GlobalConstants.WS05;
import static com.lht.corecalculation.api.constants.GlobalConstants.WS06;
import static com.lht.corecalculation.api.constants.GlobalConstants.WS07;
import static com.lht.corecalculation.api.constants.GlobalConstants.WS08;
import static com.lht.corecalculation.api.constants.GlobalConstants.WS09;
import static com.lht.corecalculation.api.constants.GlobalConstants.WS10;

public class DataProvidersWorkscopeSummaryNteLeap1B extends BaseCocaApiTest {

    @DataProvider(name = "summaryProductionCostNteLeap1b")
    public Object[][] workscopeSummaryProductionCostDataProviderNteLeap1b() {
        return new Object[][] {
                {
                        WS03,
                        Arrays.asList(1531210.0, 1560359.0, 1642340.0, 1723924.0),
                        Arrays.asList(7064688.0, 7426927.0, 7838676.0, 8077865.0),
                        Arrays.asList(8595898.0, 8987286.0, 9481016.0, 9801789.0)
                },
                {
                        WS05,
                        Arrays.asList(601285.0, 611223.0, 642344.0, 673038.0),
                        Arrays.asList(2058852.0, 2147091.0, 2279418.0, 2053365.0),
                        Arrays.asList(2660137.0, 2758314.0, 2921762.0, 2726403.0)
                },
                {
                        WS07,
                        Arrays.asList(530319.0, 538327.0, 565302.0, 591688.0),
                        Arrays.asList(671973.0, 701344.0, 744162.0, 720565.0),
                        Arrays.asList(1202292.0, 1239671.0, 1309464.0, 1312253.0)
                },
                {
                        WS08,
                        Arrays.asList(192299.0, 186291.0, 192483.0, 197212.0),
                        Arrays.asList(9956.0, 9758.0, 10072.0, 10307.0),
                        Arrays.asList(202255.0, 196049.0, 202555.0, 207519.0)
                },
                {
                        WS06,
                        Arrays.asList(352709.0, 352143.0, 368353.0, 383560.0),
                        Arrays.asList(571416.0, 611749.0, 648689.0, 662174.0),
                        Arrays.asList(924125.0, 963892.0, 1017042.0, 1045734.0)
                },
                {
                        WS10,
                        Arrays.asList(242372.0, 233331.0, 240608.0, 245858.0),
                        Arrays.asList(985740.0, 1047617.0, 1112560.0, 1181527.0),
                        Arrays.asList(1228112.0, 1280948.0, 1353168.0, 1427385.0)
                },
                {
                        WS01,
                        Arrays.asList(933997.0, 945582.0, 993470.0, 1040464.0),
                        Arrays.asList(4087638.0, 4279250.0, 4488982.0, 4754814.0),
                        Arrays.asList(5021635.0, 5224832.0, 5482452.0, 5795278.0)
                },
                {
                        WS03_1,
                        Arrays.asList(1419828.0, 1443447.0, 1518421.0, 1592645.0),
                        Arrays.asList(6825324.0, 7208530.0, 7636825.0, 7863769.0),
                        Arrays.asList(8245152.0, 8651977.0, 9155246.0, 9456414.0)
                },
                {
                        WS02,
                        Arrays.asList(991458.0, 1004596.0, 1055804.0, 1106204.0),
                        Arrays.asList(4667286.0, 4885441.0, 5132437.0, 5325921.0),
                        Arrays.asList(5658744.0, 5890037.0, 6188241.0, 6432125.0)
                },
                {
                        WS09,
                        Arrays.asList(149344.0, 142636.0, 146731.0, 149444.0),
                        Arrays.asList(9371.0, 9317.0, 9738.0, 10131.0),
                        Arrays.asList(158715.0, 151953.0, 156469.0, 159575.0)
                },
                {
                        WS04,
                        Arrays.asList(2359927.0, 2397523.0, 2520175.0, 2641466.0),
                        Arrays.asList(8179970.0, 8562345.0, 9056257.0, 9033031.0),
                        Arrays.asList(10539897.0, 10959868.0, 11576432.0, 11674497.0)
                }
        };
    }

    @DataProvider(name = "summaryDiscountsNteLeap1b")
    public Object[][] workscopeSummaryDiscountsDataProviderNteLeap1b() {
        return new Object[][] {
                {
                        WS03,
                        Arrays.asList(240266.0, 321974.0, 359253.0, 334036.0),
                        Arrays.asList(769436.0, 1167606.0, 1367578.0, 1016050.0),
                        Arrays.asList(1009702.0, 1489580.0, 1726831.0, 1350087.0)
                },
                {
                        WS05,
                        Arrays.asList(65463.0, 91236.0, 103916.0, 96217.0),
                        Arrays.asList(276098.0, 402189.0, 477546.0, 308357.0),
                        Arrays.asList(341561.0, 493426.0, 581462.0, 404574.0)
                },
                {
                        WS07,
                        Arrays.asList(50938.0, 72338.0, 81926.0, 70951.0),
                        Arrays.asList(83208.0, 124337.0, 145520.0, 100726.0),
                        Arrays.asList(134146.0, 196676.0, 227446.0, 171678.0)
                },
                {
                        WS08,
                        Arrays.asList(8451.0, 11836.0, 14007.0, 11709.0),
                        Arrays.asList(0.0, 0.0, 0.0, 0.0),
                        Arrays.asList(8451.0, 11836.0, 14007.0, 11709.0)
                },
                {
                        WS06,
                        Arrays.asList(99727.0, 45274.0, 53262.0, 43396.0),
                        Arrays.asList(105758.0, 131684.0, 146892.0, 133269.0),
                        Arrays.asList(205485.0, 176958.0, 200154.0, 176666.0)
                },
                {
                        WS10,
                        Arrays.asList(9089.0, 13228.0, 15727.0, 12709.0),
                        Arrays.asList(137974.0, 209484.0, 250281.0, 194919.0),
                        Arrays.asList(147064.0, 222712.0, 266008.0, 207628.0)
                },
                {
                        WS01,
                        Arrays.asList(157874.0, 209740.0, 233416.0, 213601.0),
                        Arrays.asList(494478.0, 740860.0, 863677.0, 667919.0),
                        Arrays.asList(652352.0, 950601.0, 1097093.0, 881521.0)
                },
                {
                        WS03_1,
                        Arrays.asList(212355.0, 285384.0, 316673.0, 294620.0),
                        Arrays.asList(742702.0, 1136508.0, 1337855.0, 992902.0),
                        Arrays.asList(955058.0, 1421893.0, 1654528.0, 1287523.0)
                },
                {
                        WS02,
                        Arrays.asList(176777.0, 233256.0, 259661.0, 244646.0),
                        Arrays.asList(568960.0, 848294.0, 992033.0, 746530.0),
                        Arrays.asList(745737.0, 1081550.0, 1251694.0, 991177.0)
                },
                {
                        WS09,
                        Arrays.asList(4494.0, 6079.0, 7160.0, 6195.0),
                        Arrays.asList(769.0, 1110.0, 1326.0, 1032.0),
                        Arrays.asList(5263.0, 7190.0, 8486.0, 7228.0)
                },
                {
                        WS04,
                        Arrays.asList(320783.0, 437132.0, 489762.0, 446310.0),
                        Arrays.asList(1009516.0, 1452172.0, 1706146.0, 1250290.0),
                        Arrays.asList(1330299.0, 1889304.0, 2195909.0, 1696600.0)
                }
        };
    }

    @DataProvider(name = "summaryRevenueNteLeap1b")
    public Object[][] workscopeSummaryRevenueDataProviderNteLeap1b() {
        return new Object[][] {
                {
                        WS03,
                        Arrays.asList(2742781.0, 2951185.0, 3154726.0, 3414707.0),
                        Arrays.asList(6161644.0, 6480539.0, 6829804.0, 6959874.0),
                        Arrays.asList(8904425.0, 9431725.0, 9984531.0, 1.0374581E7)
                },
                {
                        WS05,
                        Arrays.asList(715557.0, 764778.0, 817770.0, 912935.0),
                        Arrays.asList(2053304.0, 2162541.0, 2296855.0, 2036268.0),
                        Arrays.asList(2768861.0, 2927320.0, 3114625.0, 2949204.0)
                },
                {
                        WS07,
                        Arrays.asList(627825.0, 671562.0, 718698.0, 784185.0),
                        Arrays.asList(656249.0, 692289.0, 735389.0, 700977.0),
                        Arrays.asList(1284074.0, 1363851.0, 1454088.0, 1485162.0)
                },
                {
                        WS08,
                        Arrays.asList(243328.0, 263333.0, 284836.0, 308172.0),
                        Arrays.asList(0.0, 0.0, 0.0, 0.0),
                        Arrays.asList(243328.0, 263333.0, 284836.0, 308172.0)
                },
                {
                        WS06,
                        Arrays.asList(460665.0, 493739.0, 529900.0, 569316.0),
                        Arrays.asList(538544.0, 581327.0, 617362.0, 629450.0),
                        Arrays.asList(999210.0, 1075067.0, 1147263.0, 1198767.0)
                },
                {
                        WS10,
                        Arrays.asList(285830.0, 310569.0, 337455.0, 366724.0),
                        Arrays.asList(990280.0, 1052498.0, 1117787.0, 1187127.0),
                        Arrays.asList(1276110.0, 1363067.0, 1455242.0, 1553851.0)
                },
                {
                        WS01,
                        Arrays.asList(1370373.0, 1480164.0, 1588993.0, 1705199.0),
                        Arrays.asList(3814643.0, 3997177.0, 4183580.0, 4427178.0),
                        Arrays.asList(5185017.0, 5477341.0, 5772574.0, 6132377.0)
                },
                {
                        WS03_1,
                        Arrays.asList(2572692.0, 2763374.0, 2950780.0, 3198189.0),
                        Arrays.asList(5966378.0, 6314051.0, 6687576.0, 6808798.0),
                        Arrays.asList(8539071.0, 9077426.0, 9638357.0, 1.0006988E7)
                },
                {
                        WS02,
                        Arrays.asList(1481411.0, 1599727.0, 1715846.0, 1862374.0),
                        Arrays.asList(4356942.0, 4567931.0, 4789783.0, 4933702.0),
                        Arrays.asList(5838353.0, 6167659.0, 6505629.0, 6796077.0)
                },
                {
                        WS09,
                        Arrays.asList(187021.0, 203062.0, 220533.0, 239551.0),
                        Arrays.asList(5717.0, 6015.0, 6388.0, 6784.0),
                        Arrays.asList(192738.0, 209077.0, 226921.0, 246336.0)
                },
                {
                        WS04,
                        Arrays.asList(3790372.0, 4064453.0, 4337773.0, 4703310.0),
                        Arrays.asList(7230831.0, 7600927.0, 8037558.0, 7880565.0),
                        Arrays.asList(1.1021204E7, 1.1665381E7, 1.2375331E7, 1.2583876E7)
                }
        };
    }

    @DataProvider(name = "summarySurchargesCostNteLeap1b")
    public Object[][] workscopeSummarySurchargesCostDataProviderNteLeap1b() {
        return new Object[][] {
                {
                        WS03,
                        Arrays.asList(103790.0, 124122.0, 118614.0, 123116.0),
                        Arrays.asList(248845.0, 298227.0, 283961.0, 281260.0),
                        Arrays.asList(352636.0, 422349.0, 402576.0, 404376.0)
                },
                {
                        WS05,
                        Arrays.asList(64209.0, 69336.0, 70243.0, 81193.0),
                        Arrays.asList(197429.0, 218234.0, 223484.0, 209189.0),
                        Arrays.asList(261639.0, 287571.0, 293728.0, 290383.0)
                },
                {
                        WS07,
                        Arrays.asList(75188.0, 84110.0, 83358.0, 93261.0),
                        Arrays.asList(83786.0, 96466.0, 96753.0, 97248.0),
                        Arrays.asList(158975.0, 180577.0, 180112.0, 190510.0)
                },
                {
                        WS08,
                        Arrays.asList(93460.0, 112186.0, 107565.0, 118662.0),
                        Arrays.asList(0.0, 0.0, 0.0, 0.0),
                        Arrays.asList(93460.0, 112186.0, 107565.0, 118662.0)
                },
                {
                        WS06,
                        Arrays.asList(56258.0, 62561.0, 61081.0, 66661.0),
                        Arrays.asList(75029.0, 89706.0, 88965.0, 95153.0),
                        Arrays.asList(131287.0, 152267.0, 150047.0, 161815.0)
                },
                {
                        WS10,
                        Arrays.asList(32309.0, 33987.0, 33159.0, 34857.0),
                        Arrays.asList(131260.0, 152441.0, 153174.0, 167352.0),
                        Arrays.asList(163570.0, 186428.0, 186333.0, 202210.0)
                },
                {
                        WS01,
                        Arrays.asList(93027.0, 100021.0, 103573.0, 110363.0),
                        Arrays.asList(279605.0, 302600.0, 310054.0, 331899.0),
                        Arrays.asList(372633.0, 402621.0, 413628.0, 442262.0)
                },
                {
                        WS03_1,
                        Arrays.asList(99752.0, 118922.0, 113127.0, 117521.0),
                        Arrays.asList(247251.0, 298263.0, 284612.0, 281718.0),
                        Arrays.asList(347004.0, 417185.0, 397739.0, 399239.0)
                },
                {
                        WS02,
                        Arrays.asList(99774.0, 107010.0, 111024.0, 119880.0),
                        Arrays.asList(315755.0, 340377.0, 350112.0, 364664.0),
                        Arrays.asList(415529.0, 447388.0, 461136.0, 484544.0)
                },
                {
                        WS09,
                        Arrays.asList(87711.0, 105397.0, 100704.0, 111066.0),
                        Arrays.asList(3147.0, 4128.0, 4073.0, 4686.0),
                        Arrays.asList(90858.0, 109526.0, 104777.0, 115752.0)
                },
                {
                        WS04,
                        Arrays.asList(135486.0, 158240.0, 152806.0, 161063.0),
                        Arrays.asList(279405.0, 329085.0, 318774.0, 308471.0),
                        Arrays.asList(414892.0, 487325.0, 471580.0, 469535.0)
                }
        };
    }

    @DataProvider(name = "summaryProdCostInclDiscountsAndSurchargesNteLeap1b")
    public Object[][] workscopeSummaryProdCostInclDiscountsAndSurchargesDataProviderNteLeap1b() {
        return new Object[][] {
                {
                        WS03,
                        Arrays.asList(2392455.0, 2443373.0, 2552839.0, 2773339.0),
                        Arrays.asList(5545301.0, 5476682.0, 5603922.0, 6082739.0),
                        Arrays.asList(7937757.0, 7920055.0, 8156762.0, 8856079.0)
                },
                {
                        WS05,
                        Arrays.asList(651290.0, 643158.0, 665052.0, 747300.0),
                        Arrays.asList(1928635.0, 1909300.0, 1968976.0, 1864910.0),
                        Arrays.asList(2579926.0, 2552459.0, 2634029.0, 2612211.0)
                },
                {
                        WS07,
                        Arrays.asList(592597.0, 589194.0, 607475.0, 664702.0),
                        Arrays.asList(634233.0, 634379.0, 654655.0, 666383.0),
                        Arrays.asList(1226831.0, 1223573.0, 1262130.0, 1331085.0)
                },
                {
                        WS08,
                        Arrays.asList(287265.0, 296400.0, 296114.0, 314472.0),
                        Arrays.asList(0.0, 0.0, 0.0, 0.0),
                        Arrays.asList(287265.0, 296400.0, 296114.0, 314472.0)
                },
                {
                        WS06,
                        Arrays.asList(420170.0, 413316.0, 421837.0, 454068.0),
                        Arrays.asList(497395.0, 525886.0, 545097.0, 576815.0),
                        Arrays.asList(917565.0, 939202.0, 966934.0, 1030884.0)
                },
                {
                        WS10,
                        Arrays.asList(265802.0, 254286.0, 258240.0, 268207.0),
                        Arrays.asList(978816.0, 990378.0, 1015254.0, 1153761.0),
                        Arrays.asList(1244619.0, 1244665.0, 1273494.0, 1421969.0)
                },
                {
                        WS01,
                        Arrays.asList(1187773.0, 1188255.0, 1242976.0, 1342930.0),
                        Arrays.asList(3553112.0, 3488598.0, 3556010.0, 4013090.0),
                        Arrays.asList(4740885.0, 4676853.0, 4798987.0, 5356020.0)
                },
                {
                        WS03_1,
                        Arrays.asList(2256542.0, 2299856.0, 2400439.0, 2606512.0),
                        Arrays.asList(5379482.0, 5347413.0, 5498018.0, 5961619.0),
                        Arrays.asList(7636024.0, 7647269.0, 7898457.0, 8568132.0)
                },
                {
                        WS02,
                        Arrays.asList(1280454.0, 1282590.0, 1341257.0, 1466592.0),
                        Arrays.asList(4046795.0, 3973285.0, 4056425.0, 4458901.0),
                        Arrays.asList(5327250.0, 5255875.0, 5397683.0, 5925493.0)
                },
                {
                        WS09,
                        Arrays.asList(236398.0, 245543.0, 243930.0, 257986.0),
                        Arrays.asList(7874.0, 8746.0, 8830.0, 10113.0),
                        Arrays.asList(244273.0, 254289.0, 252760.0, 268099.0)
                },
                {
                        WS04,
                        Arrays.asList(3254630.0, 3279901.0, 3414161.0, 3719433.0),
                        Arrays.asList(6367893.0, 6277989.0, 6437943.0, 6727999.0),
                        Arrays.asList(9622523.0, 9557890.0, 9852104.0, 1.0447432E7)
                }
        };
    }

    @DataProvider(name = "summaryDb2ValuesNteLeap1b")
    public Object[][] workscopeSummaryDb2ValuesNteLeap1b() {
        return new Object[][] {
                {
                        WS03,
                        Arrays.asList(350325.0, 507812.0, 601886.0, 641368.0),
                        Arrays.asList(616342.0, 1003857.0, 1225881.0, 877134.0),
                        Arrays.asList(966668.0, 1511670.0, 1827768.0, 1518503.0)
                },
                {
                        WS05,
                        Arrays.asList(64266.0, 121620.0, 152717.0, 165635.0),
                        Arrays.asList(124669.0, 253240.0, 327879.0, 171357.0),
                        Arrays.asList(188935.0, 374860.0, 480596.0, 336992.0)
                },
                {
                        WS07,
                        Arrays.asList(35227.0, 82368.0, 111223.0, 119483.0),
                        Arrays.asList(22016.0, 57909.0, 80734.0, 34593.0),
                        Arrays.asList(57243.0, 140277.0, 191957.0, 154076.0)
                },
                {
                        WS08,
                        Arrays.asList(-43936.0, -33067.0, -11277.0, -6300.0),
                        Arrays.asList(0.0, 0.0, 0.0, 0.0),
                        Arrays.asList(-43936.0, -33067.0, -11277.0, -6300.0)
                },
                {
                        WS06,
                        Arrays.asList(40495.0, 80423.0, 108063.0, 115247.0),
                        Arrays.asList(41148.0, 55441.0, 72264.0, 52635.0),
                        Arrays.asList(81644.0, 135864.0, 180328.0, 167882.0)
                },
                {
                        WS10,
                        Arrays.asList(20027.0, 56283.0, 79215.0, 98517.0),
                        Arrays.asList(11463.0, 62119.0, 102533.0, 33365.0),
                        Arrays.asList(31491.0, 118402.0, 181748.0, 131882.0)
                },
                {
                        WS01,
                        Arrays.asList(182600.0, 291908.0, 346016.0, 362268.0),
                        Arrays.asList(261530.0, 508579.0, 627570.0, 414087.0),
                        Arrays.asList(444131.0, 800488.0, 973587.0, 776356.0)
                },
                {
                        WS03_1,
                        Arrays.asList(316150.0, 463518.0, 550341.0, 591677.0),
                        Arrays.asList(586896.0, 966638.0, 1189558.0, 847179.0),
                        Arrays.asList(903046.0, 1430157.0, 1739899.0, 1438856.0)
                },
                {
                        WS02,
                        Arrays.asList(200956.0, 317137.0, 374588.0, 395782.0),
                        Arrays.asList(310146.0, 594646.0, 733357.0, 474801.0),
                        Arrays.asList(511103.0, 911784.0, 1107945.0, 870583.0)
                },
                {
                        WS09,
                        Arrays.asList(-49377.0, -42481.0, -23397.0, -18434.0),
                        Arrays.asList(-2157.0, -2730.0, -2441.0, -3328.0),
                        Arrays.asList(-51535.0, -45212.0, -25838.0, -21762.0)
                },
                {
                        WS04,
                        Arrays.asList(535742.0, 784552.0, 923611.0, 983877.0),
                        Arrays.asList(862938.0, 1322937.0, 1599615.0, 1152566.0),
                        Arrays.asList(1398680.0, 2107490.0, 2523227.0, 2136444.0)
                }
        };
    }

    @DataProvider(name = "summaryDb2PercentageValuesNteLeap1b")
    public Object[][] workscopeSummaryDb2PercentageValuesNteLeap1b() {
        return new Object[][] {
                {
                        WS03,
                        Arrays.asList(12.977325, 17.420637, 19.29552, 18.733837),
                        Arrays.asList(10.002892, 15.490332, 17.949003, 12.287987),
                        Arrays.asList(10.91909, 16.094324, 18.374453, 14.409585)
                },
                {
                        WS05,
                        Arrays.asList(9.0179205, 15.940694, 18.712599, 17.603544),
                        Arrays.asList(6.071643, 11.710315, 14.275127, 7.718878),
                        Arrays.asList(6.8330493, 12.815525, 15.440221, 10.77871)
                },
                {
                        WS07,
                        Arrays.asList(5.652806, 12.308488, 15.518556, 15.310323),
                        Arrays.asList(3.3548205, 8.364911, 10.978438, 4.9350324),
                        Arrays.asList(4.478379, 10.306734, 13.222439, 10.413325)
                },
                {
                        WS08,
                        Arrays.asList(-18.056349, -12.557145, -3.9592, -2.044422),
                        Arrays.asList(0.0, 0.0, 0.0, 0.0),
                        Arrays.asList(-18.056349, -12.557145, -3.9592, -2.044422)
                },
                {
                        WS06,
                        Arrays.asList(9.203751, 16.71636, 20.815681, 20.659975),
                        Arrays.asList(8.304298, 10.21944, 12.386519, 9.082911),
                        Arrays.asList(8.718973, 13.203242, 16.279802, 14.581069)
                },
                {
                        WS10,
                        Arrays.asList(7.0069275, 18.12258, 23.474232, 26.864046),
                        Arrays.asList(1.1575819, 5.9021106, 9.172867, 2.8106244),
                        Arrays.asList(2.467752, 8.686499, 12.489201, 8.487473)
                },
                {
                        WS01,
                        Arrays.asList(13.556443, 19.964195, 22.025244, 21.220219),
                        Arrays.asList(6.855972, 12.723467, 15.000789, 9.019645),
                        Arrays.asList(8.626873, 14.680158, 16.934383, 12.412197)
                },
                {
                        WS03_1,
                        Arrays.asList(12.480253, 16.971601, 18.850271, 18.427008),
                        Arrays.asList(9.83673, 15.30932, 17.78758, 12.116014),
                        Arrays.asList(10.633183, 15.815357, 18.112925, 14.13298)
                },
                {
                        WS02,
                        Arrays.asList(13.801887, 20.072205, 22.084864, 21.256023),
                        Arrays.asList(7.1184506, 13.017851, 15.310874, 9.323376),
                        Arrays.asList(8.814291, 14.847564, 17.0975, 12.59336)
                },
                {
                        WS09,
                        Arrays.asList(-26.402246, -20.920412, -10.609517, -7.695417),
                        Arrays.asList(-37.736557, -45.3917, -38.21469, -49.057987),
                        Arrays.asList(-26.738466, -21.624514, -11.386701, -8.83465)
                },
                {
                        WS04,
                        Arrays.asList(14.320858, 19.497665, 21.487318, 20.899002),
                        Arrays.asList(11.983577, 17.457142, 19.954075, 14.420439),
                        Arrays.asList(12.787406, 18.1681, 20.491503, 16.841846)
                }
        };
    }

    @DataProvider(name = "summaryEbitValuesNteLeap1b")
    public Object[][] workscopeSummaryEbitValuesNteLeap1b() {
        return new Object[][] {
                {
                        WS03,
                        Arrays.asList(205289.0, 362994.0, 434111.0, 501340.0),
                        Arrays.asList(268606.0, 655903.0, 824230.0, 557241.0),
                        Arrays.asList(473896.0, 1018897.0, 1258342.0, 1058582.0)
                },
                {
                        WS05,
                        Arrays.asList(40079.0, 97857.0, 125482.0, 139915.0),
                        Arrays.asList(50301.0, 178448.0, 241228.0, 105092.0),
                        Arrays.asList(90380.0, 276305.0, 366711.0, 245008.0)
                },
                {
                        WS07,
                        Arrays.asList(-11384.0, 36462.0, 58515.0, 74454.0),
                        Arrays.asList(-29926.0, 5260.0, 19557.0, -12361.0),
                        Arrays.asList(-41310.0, 41722.0, 78072.0, 62092.0)
                },
                {
                        WS08,
                        Arrays.asList(-142490.0, -131621.0, -125162.0, -98284.0),
                        Arrays.asList(0.0, 0.0, 0.0, 0.0),
                        Arrays.asList(-142490.0, -131621.0, -125162.0, -98284.0)
                },
                {
                        WS06,
                        Arrays.asList(-1736.0, 39930.0, 61703.0, 77353.0),
                        Arrays.asList(-15173.0, -2620.0, 4740.0, -1454.0),
                        Arrays.asList(-16910.0, 37310.0, 66443.0, 75898.0)
                },
                {
                        WS10,
                        Arrays.asList(560.0, 38315.0, 58948.0, 82660.0),
                        Arrays.asList(-67624.0, -18467.0, 8914.0, -42761.0),
                        Arrays.asList(-67063.0, 19848.0, 67862.0, 39898.0)
                },
                {
                        WS01,
                        Arrays.asList(157996.0, 267425.0, 317499.0, 339314.0),
                        Arrays.asList(187580.0, 434508.0, 542202.0, 345057.0),
                        Arrays.asList(345576.0, 701933.0, 859701.0, 684372.0)
                },
                {
                        WS03_1,
                        Arrays.asList(174493.0, 323049.0, 388382.0, 456294.0),
                        Arrays.asList(235780.0, 614334.0, 782091.0, 522641.0),
                        Arrays.asList(410274.0, 937384.0, 1170473.0, 978935.0)
                },
                {
                        WS02,
                        Arrays.asList(177292.0, 293564.0, 347168.0, 373024.0),
                        Arrays.asList(235256.0, 519665.0, 646891.0, 405575.0),
                        Arrays.asList(412548.0, 813229.0, 994060.0, 778600.0)
                },
                {
                        WS09,
                        Arrays.asList(-144518.0, -137320.0, -132855.0, -106694.0),
                        Arrays.asList(-5571.0, -6445.0, -6868.0, -7052.0),
                        Arrays.asList(-150089.0, -143766.0, -139724.0, -113747.0)
                },
                {
                        WS04,
                        Arrays.asList(374823.0, 624543.0, 739100.0, 826111.0),
                        Arrays.asList(531085.0, 990174.0, 1214701.0, 850411.0),
                        Arrays.asList(905908.0, 1614717.0, 1953801.0, 1676523.0)
                },
        };
    }

    @DataProvider(name = "summaryEbitPercentageValuesNteLeap1b")
    public Object[][] workscopeSummaryEbitPercentageValuesNteLeap1b() {
        return new Object[][] {
                {
                        WS03,
                        Arrays.asList(7.484715, 12.299955, 13.760672, 14.681800),
                        Arrays.asList(4.3593335, 10.121118, 12.068148, 8.006485),
                        Arrays.asList(5.322028, 10.802874, 12.602922, 10.203611)
                },
                {
                        WS05,
                        Arrays.asList(5.601165, 12.795528, 15.344462, 15.325883),
                        Arrays.asList(2.4497805, 8.251793, 10.502573, 5.1610394),
                        Arrays.asList(3.273663, 9.4488125, 11.783756, 8.307600)
                },
                {
                        WS07,
                        Arrays.asList(-1.813383, 5.429551, 8.141812, 9.494449),
                        Arrays.asList(-4.560169, 0.7598213, 2.659419, -1.7634448),
                        Arrays.asList(-3.217179, 3.059202, 5.369149, 4.180870)
                },
                {
                        WS08,
                        Arrays.asList(-58.558952, -49.982887, -43.94179, -31.892723),
                        Arrays.asList(0.0, 0.0, 0.0, 0.0),
                        Arrays.asList(-58.558952, -49.982887, -43.94179, -31.892723)
                },
                {
                        WS06,
                        Arrays.asList(-0.376916, 8.087363, 11.644265, 13.587052),
                        Arrays.asList(-2.8175774, -0.45074362, 0.7678176, -0.23113729),
                        Arrays.asList(-1.6923603, 3.470501, 5.7914586, 6.331372)
                },
                {
                        WS10,
                        Arrays.asList(0.19624902, 12.337314, 17.468445, 22.540197),
                        Arrays.asList(-6.8287954, -1.7546456, 0.79753953, -3.6021323),
                        Arrays.asList(-5.2552857, 1.456155, 4.663344, 2.5677185)
                },
                {
                        WS01,
                        Arrays.asList(11.52943, 18.06729, 19.981182, 19.89883),
                        Arrays.asList(4.9173794, 10.87038, 12.960241, 7.7940736),
                        Arrays.asList(6.6649113, 12.81523, 14.892868, 11.159982)
                },
                {
                        WS03_1,
                        Arrays.asList(6.782538, 11.69041, 13.162018, 14.267261),
                        Arrays.asList(3.95182, 9.729647, 11.694691, 7.6759715),
                        Arrays.asList(4.8046722, 10.326548, 12.143913, 9.782519)
                },
                {
                        WS02,
                        Arrays.asList(11.967795, 18.35089, 20.2331, 20.029528),
                        Arrays.asList(5.3995805, 11.376382, 13.505659, 8.220502),
                        Arrays.asList(7.066185, 13.185385, 15.280008, 11.456608)
                },
                {
                        WS09,
                        Arrays.asList(-77.27395, -67.6251, -60.242752, -44.539356),
                        Arrays.asList(-97.442924, -107.14676, -107.51646, -103.94301),
                        Arrays.asList(-77.87224, -68.76223, -61.573677, -46.175488)
                },
                {
                        WS04,
                        Arrays.asList(9.888818, 15.365995, 17.038708, 17.564472),
                        Arrays.asList(7.344732, 13.027017, 15.11281, 10.791247),
                        Arrays.asList(8.219685, 13.841964, 15.787871, 13.322787)
                },
        };
    }

    @DataProvider(name = "summaryEatPercentageValuesNteLeap1b")
    public Object[][] workscopeSummaryEatPercentageValuesNteLeap1b() {
        return new Object[][] {
                {
                        WS03,
                        Arrays.asList(5.613536, 9.224966, 10.320504, 11.011350),
                        Arrays.asList(3.2695, 7.5908384, 9.051111, 6.0048637),
                        Arrays.asList(3.991521, 8.102156, 9.452191, 7.652709)
                },
                {
                        WS05,
                        Arrays.asList(4.200874, 9.596646, 11.508347, 11.494412),
                        Arrays.asList(1.8373353, 6.1888447, 7.8769298, 3.8707795),
                        Arrays.asList(2.4552474, 7.0866094, 8.837817, 6.230701)
                },
                {
                        WS07,
                        Arrays.asList(-1.360037, 4.072163, 6.101262, 7.120836),
                        Arrays.asList(-3.4201272, 0.56986594, 1.9945643, -1.3225836),
                        Arrays.asList(-2.412884, 2.294401, 4.026862, 3.135652)
                },
                {
                        WS08,
                        Arrays.asList(-43.919216, -37.487167, -32.956345, -23.919542),
                        Arrays.asList(0.0, 0.0, 0.0, 0.0),
                        Arrays.asList(-43.919216, -37.487167, -32.956345, -23.919542)
                },
                {
                        WS06,
                        Arrays.asList(-0.282687, 6.065522, 8.733199, 10.190286),
                        Arrays.asList(-2.113183, -0.33805773, 0.5758632, -0.17335297),
                        Arrays.asList(-1.2692702, 2.6028757, 4.343594, 4.7485285)
                },
                {
                        WS10,
                        Arrays.asList(0.14718677, 9.252986, 13.101333, 16.905148),
                        Arrays.asList(-5.121597, -1.3159842, 0.5981546, -2.7015991),
                        Arrays.asList(-3.9414642, 1.0921162, 3.497508, 1.925789)
                },
                {
                        WS01,
                        Arrays.asList(8.647073, 13.550467, 14.985887, 14.924123),
                        Arrays.asList(3.6880345, 8.152785, 9.7201805, 5.8455553),
                        Arrays.asList(4.9986835, 9.611423, 11.169651, 8.369987)
                },
                {
                        WS03_1,
                        Arrays.asList(5.086903, 8.767808, 9.871513, 10.700446),
                        Arrays.asList(2.9638648, 7.297235, 8.771018, 5.7569785),
                        Arrays.asList(3.6035042, 7.744911, 9.107935, 7.3368893)
                },
                {
                        WS02,
                        Arrays.asList(8.975846, 13.763167, 15.174825, 15.022146),
                        Arrays.asList(4.0496855, 8.532287, 10.129244, 6.165376),
                        Arrays.asList(5.2996387, 9.889039, 11.460006, 8.592456)
                },
                {
                        WS09,
                        Arrays.asList(-57.95546, -50.718822, -45.182064, -33.40452),
                        Arrays.asList(-73.08219, -80.36007, -80.637344, -77.95726),
                        Arrays.asList(-58.40418, -51.571674, -46.180256, -34.63162)
                },
                {
                        WS04,
                        Arrays.asList(7.416613, 11.524496, 12.77903, 13.173355),
                        Arrays.asList(5.5085487, 9.770263, 11.334608, 8.093435),
                        Arrays.asList(6.164763, 10.381473, 11.840903, 9.99209)
                },
        };
    }

    @DataProvider(name = "summaryNetMarginPercentageNteLeap1b")
    public Object[][] workscopeSummaryNetMarginPercentageNteLeap1b() {
        return new Object[][] {
                {
                        WS03,
                        Arrays.asList(3.213536, 6.824966, 7.920504, 8.611350),
                        Arrays.asList(0.8695001, 5.1908383, 6.651111, 3.604864),
                        Arrays.asList(1.591521, 5.702156, 7.052191, 5.252707)
                },
                {
                        WS05,
                        Arrays.asList(1.800874, 7.196646, 9.108347, 9.094412),
                        Arrays.asList(-0.5626647, 3.7888446, 5.4769297, 1.4707794),
                        Arrays.asList(0.0552473, 4.6866093, 6.437817, 3.830700)
                },
                {
                        WS07,
                        Arrays.asList(-3.760037, 1.672163, 3.706359, 4.720833),
                        Arrays.asList(-5.820127, -1.8301342, -0.4054357, -3.7225835),
                        Arrays.asList(-4.812883, -0.105599, 1.626862, 0.735652)
                },
                {
                        WS08,
                        Arrays.asList(-46.319218, -39.887165, -35.356342, -26.319544),
                        Arrays.asList(-2.4, -2.4, -2.4, -2.4),
                        Arrays.asList(-46.319218, -39.887165, -35.356342, -26.319544)
                },
                {
                        WS06,
                        Arrays.asList(-2.682687, 3.665522, 6.333199, 7.790286),
                        Arrays.asList(-4.513183, -2.738058, -1.824137, -2.573354),
                        Arrays.asList(-3.669270, 0.202876, 1.943594, 2.348529)
                },
                {
                        WS10,
                        Arrays.asList(-2.252813, 6.8529854, 10.701333, 14.505148),
                        Arrays.asList(-7.5215964, -3.715984, -1.8018454, -5.101599),
                        Arrays.asList(-6.341464, -1.3078839, 1.0975081, -0.474211)
                },
                {
                        WS01,
                        Arrays.asList(6.247073, 11.150467, 12.585887, 12.524118),
                        Arrays.asList(1.2880346, 5.752785, 7.320181, 3.4455552),
                        Arrays.asList(2.598683, 7.211422, 8.769651, 5.969985)
                },
                {
                        WS03_1,
                        Arrays.asList(2.686903, 6.367807, 7.471513, 8.300448),
                        Arrays.asList(0.5638649, 4.897235, 6.3710184, 3.3569787),
                        Arrays.asList(1.203504, 5.344911, 6.707935, 4.936890)
                },
                {
                        WS02,
                        Arrays.asList(6.575851, 11.363167, 12.774825, 12.622151),
                        Arrays.asList(1.6496855, 6.1322865, 7.729244, 3.765376),
                        Arrays.asList(2.899639, 7.489039, 9.060006, 6.192458)
                },
                {
                        WS09,
                        Arrays.asList(-60.35546, -53.118824, -47.582066, -35.80452),
                        Arrays.asList(-75.48219, -82.76007, -83.037346, -80.35726),
                        Arrays.asList(-60.80418, -53.971676, -48.580257, -37.031616)
                },
                {
                        WS04,
                        Arrays.asList(5.016613, 9.124496, 10.379032, 10.773353),
                        Arrays.asList(3.108549, 7.370263, 8.934608, 5.693435),
                        Arrays.asList(3.764763, 7.981473, 9.440903, 7.592090)
                },
        };
    }
}
