package com.lht.corecalculation.api.wssummarynteandfixprice;

import com.lht.corecalculation.base.BaseCocaApiTest;
import java.util.Arrays;
import org.testng.annotations.DataProvider;


import static com.lht.corecalculation.api.constants.GlobalConstants.C_N_A;
import static com.lht.corecalculation.api.constants.GlobalConstants.C_N_A_K;
import static com.lht.corecalculation.api.constants.GlobalConstants.C_N_B;
import static com.lht.corecalculation.api.constants.GlobalConstants.C_N_B_K;

public class DataProvidersWorkscopeSummaryFixedPriceV2500 extends BaseCocaApiTest {

    @DataProvider(name = "summaryProductionCostFixedPriceV2500")
    public Object[][] workscopeSummaryProductionCostDataProviderFixedPriceV2500() {
        return new Object[][] {
                {
                        C_N_A_K,
                        Arrays.asList(1656671.0, 1742354.0, 1833075.0, 1923057.0, 2014712.0, 2132638.0),
                        Arrays.asList(1289308.0, 1412995.0, 1505070.0, 1602606.0, 1706087.0, 1816231.0),
                        Arrays.asList(2945980.0, 3155349.0, 3338145.0, 3525664.0, 3720799.0, 3948869.0)
                },
                {
                        C_N_A,
                        Arrays.asList(1986802.0, 2092940.0, 2203326.0, 2313273.0, 2425437.0, 2568465.0),
                        Arrays.asList(1735414.0, 1886992.0, 2006533.0, 2132103.0, 2264164.0, 2407722.0),
                        Arrays.asList(3722216.0, 3979933.0, 4209860.0, 4445377.0, 4689602.0, 4976188.0)
                },
                {
                        C_N_B_K,
                        Arrays.asList(1637530.0, 1724364.0, 1815056.0, 1905243.0, 1997274.0, 2115145.0),
                        Arrays.asList(955249.0, 1047287.0, 1118362.0, 1193701.0, 1273719.0, 1359065.0),
                        Arrays.asList(2592780.0, 2771651.0, 2933418.0, 3098944.0, 3270994.0, 3474211.0)
                },
                {
                        C_N_B,
                        Arrays.asList(2263249.0, 2395633.0, 2525427.0, 2655934.0, 2789928.0, 2957387.0),
                        Arrays.asList(1682362.0, 1815931.0, 1931482.0, 2051953.0, 2178405.0, 2319168.0),
                        Arrays.asList(3945611.0, 4211564.0, 4456909.0, 4707887.0, 4968333.0, 5276556.0)
                }
        };
    }

    @DataProvider(name = "summaryDiscountsFixedPriceV2500")
    public Object[][] workscopeSummaryDiscountsDataProviderFixedPriceV2500() {
        return new Object[][] {
                {
                        C_N_A_K,
                        Arrays.asList(13315.0, 14780.0, 15667.0, 16607.0, 17603.0, 18659.0),
                        Arrays.asList(0.0, 0.0, 0.0, 0.0, 0.0, 0.0),
                        Arrays.asList(13315.0, 14780.0, 15667.0, 16607.0, 17603.0, 18659.0)
                },
                {
                        C_N_A,
                        Arrays.asList(15349.0, 17038.0, 18060.0, 19144.0, 20292.0, 21510.0),
                        Arrays.asList(0.0, 0.0, 0.0, 0.0, 0.0, 0.0),
                        Arrays.asList(15349.0, 17038.0, 18060.0, 19144.0, 20292.0, 21510.0)
                },
                {
                        C_N_B_K,
                        Arrays.asList(15345.0, 17033.0, 18055.0, 19138.0, 20287.0, 21504.0),
                        Arrays.asList(0.0, 0.0, 0.0, 0.0, 0.0, 0.0),
                        Arrays.asList(15345.0, 17033.0, 18055.0, 19138.0, 20287.0, 21504.0)
                },
                {
                        C_N_B,
                        Arrays.asList(16565.0, 18387.0, 19491.0, 20660.0, 21900.0, 23214.0),
                        Arrays.asList(0.0, 0.0, 0.0, 0.0, 0.0, 0.0),
                        Arrays.asList(16565.0, 18387.0, 19491.0, 20660.0, 21900.0, 23214.0)
                }
        };
    }

    @DataProvider(name = "summaryRevenueFixedPriceV2500")
    public Object[][] workscopeSummaryRevenueDataProviderFixedPriceV2500() {
        return new Object[][] {
                {
                        C_N_A_K,
                        Arrays.asList(2277046.0, 2478103.0, 2640448.0, 2813808.0, 2998971.0, 3197845.0),
                        Arrays.asList(1535084.0, 1682085.0, 1792469.0, 1909926.0, 2034965.0, 2168202.0),
                        Arrays.asList(3812131.0, 4160188.0, 4432918.0, 4723734.0, 5033936.0, 5366048.0)
                },
                {
                        C_N_A,
                        Arrays.asList(2831410.0, 3079217.0, 3278702.0, 3491527.0, 3718554.0, 3962160.0),
                        Arrays.asList(2143604.0, 2343307.0, 2497067.0, 2660813.0, 2834991.0, 3020468.0),
                        Arrays.asList(4975015.0, 5422524.0, 5775769.0, 6152340.0, 6553546.0, 6982629.0)
                },
                {
                        C_N_B_K,
                        Arrays.asList(2218705.0, 2416683.0, 2576264.0, 2746750.0, 2928924.0, 3124729.0),
                        Arrays.asList(1247756.0, 1367759.0, 1460627.0, 1559606.0, 1665155.0, 1777836.0),
                        Arrays.asList(3466462.0, 3784442.0, 4036891.0, 4306356.0, 4594080.0, 4902565.0)
                },
                {
                        C_N_B,
                        Arrays.asList(3245187.0, 3533914.0, 3763757.0, 4008976.0, 4270562.0, 4551394.0),
                        Arrays.asList(2316011.0, 2528463.0, 2699075.0, 2881143.0, 3075352.0, 3282470.0),
                        Arrays.asList(5561198.0, 6062378.0, 6462833.0, 6890120.0, 7345915.0, 7833864.0)
                }
        };
    }

    @DataProvider(name = "summarySurchargesCostFixedPriceV2500")
    public Object[][] workscopeSummarySurchargesCostDataProviderFixedPriceV2500() {
        return new Object[][] {
                {
                        C_N_A_K,
                        Arrays.asList(161891.0, 196834.0, 182978.0, 175079.0, 178911.0, 184629.0),
                        Arrays.asList(125992.0, 159626.0, 150236.0, 145904.0, 151505.0, 157237.0),
                        Arrays.asList(287884.0, 356460.0, 333214.0, 320984.0, 330416.0, 341867.0)
                },
                {
                        C_N_A,
                        Arrays.asList(162708.0, 197029.0, 184492.0, 177669.0, 182084.0, 188285.0),
                        Arrays.asList(142120.0, 177641.0, 168014.0, 163754.0, 169976.0, 176502.0),
                        Arrays.asList(304828.0, 374670.0, 352507.0, 341424.0, 352060.0, 364788.0)
                },
                {
                        C_N_B_K,
                        Arrays.asList(185579.0, 225741.0, 210359.0, 201742.0, 206380.0, 213019.0),
                        Arrays.asList(108257.0, 137103.0, 129614.0, 126398.0, 131614.0, 136873.0),
                        Arrays.asList(293837.0, 362844.0, 339973.0, 328140.0, 337994.0, 349893.0)
                },
                {
                        C_N_B,
                        Arrays.asList(181678.0, 220534.0, 207620.0, 200981.0, 206587.0, 213931.0),
                        Arrays.asList(135048.0, 167169.0, 158791.0, 155277.0, 161305.0, 167763.0),
                        Arrays.asList(316726.0, 387703.0, 366411.0, 356259.0, 367892.0, 381694.0)
                }
        };
    }

    @DataProvider(name = "summaryProdCostInclDiscountsAndSurchargesFixedPriceV2500")
    public Object[][] workscopeSummaryProdCostInclDiscountsAndSurchargesDataProviderFixedPriceV2500() {
        return new Object[][] {
                {
                        C_N_A_K,
                        Arrays.asList(1805247.0, 1924408.0, 2000386.0, 2081529.0, 2176020.0, 2298608.0),
                        Arrays.asList(1415301.0, 1572621.0, 1655306.0, 1748511.0, 1857592.0, 1973468.0),
                        Arrays.asList(3220549.0, 3497030.0, 3655693.0, 3830041.0, 4033613.0, 4272077.0)
                },
                {
                        C_N_A,
                        Arrays.asList(2134160.0, 2272931.0, 2369759.0, 2471799.0, 2587228.0, 2735241.0),
                        Arrays.asList(1877535.0, 2064633.0, 2174547.0, 2295858.0, 2434141.0, 2584224.0),
                        Arrays.asList(4011695.0, 4337565.0, 4544307.0, 4767657.0, 5021370.0, 5319465.0)
                },
                {
                        C_N_B_K,
                        Arrays.asList(1807765.0, 1933072.0, 2007359.0, 2087846.0, 2183367.0, 2306661.0),
                        Arrays.asList(1063507.0, 1184390.0, 1247976.0, 1320099.0, 1405334.0, 1495939.0),
                        Arrays.asList(2871272.0, 3117462.0, 3255336.0, 3407946.0, 3588701.0, 3802600.0)
                },
                {
                        C_N_B,
                        Arrays.asList(2428361.0, 2597779.0, 2713556.0, 2836255.0, 2974615.0, 3148104.0),
                        Arrays.asList(1817410.0, 1983100.0, 2090273.0, 2207230.0, 2339710.0, 2486932.0),
                        Arrays.asList(4245772.0, 4580880.0, 4803830.0, 5043486.0, 5314326.0, 5635036.0)
                }
        };
    }

    @DataProvider(name = "summaryDb2ValuesFixedPriceV2500")
    public Object[][] workscopeSummaryDb2ValuesFixedPriceV2500() {
        return new Object[][] {
                {
                        C_N_A_K,
                        Arrays.asList(471799.0, 553694.0, 640061.0, 732279.0, 822950.0, 899237.0),
                        Arrays.asList(119782.0, 109463.0, 137162.0, 161414.0, 177373.0, 194733.0),
                        Arrays.asList(591581.0, 663157.0, 777224.0, 893693.0, 1000323.0, 1093971.0)
                },
                {
                        C_N_A,
                        Arrays.asList(697250.0, 806285.0, 908942.0, 1019727.0, 1131326.0, 1226919.0),
                        Arrays.asList(266069.0, 278673.0, 322519.0, 364955.0, 400850.0, 436243.0),
                        Arrays.asList(963319.0, 1084959.0, 1231462.0, 1384682.0, 1532176.0, 1663163.0)
                },
                {
                        C_N_B_K,
                        Arrays.asList(410940.0, 483610.0, 568904.0, 658904.0, 745557.0, 818067.0),
                        Arrays.asList(184249.0, 183368.0, 212650.0, 239506.0, 259821.0, 281897.0),
                        Arrays.asList(595190.0, 666979.0, 781555.0, 898410.0, 1005378.0, 1099965.0)
                },
                {
                        C_N_B,
                        Arrays.asList(816825.0, 936134.0, 1050201.0, 1172720.0, 1295947.0, 1403289.0),
                        Arrays.asList(498600.0, 545363.0, 608801.0, 673913.0, 735641.0, 795538.0),
                        Arrays.asList(1315426.0, 1481498.0, 1659003.0, 1846633.0, 2031589.0, 2198828.0)
                }
        };
    }

    @DataProvider(name = "summaryDb2PercentageValuesFixedPriceV2500")
    public Object[][] workscopeSummaryDb2PercentageValuesFixedPriceV2500() {
        return new Object[][] {
                {
                        C_N_A_K,
                        Arrays.asList(20.719776, 22.343472, 24.240652, 26.024477, 27.441092, 28.120094),
                        Arrays.asList(7.803019, 6.5076146, 7.652168, 8.451343, 8.716279, 8.981352),
                        Arrays.asList(15.518404, 15.940574, 17.533028, 18.919212, 19.8716, 20.386904)
                },
                {
                        C_N_A,
                        Arrays.asList(24.625538, 26.184763, 27.72264, 29.205784, 30.42381, 30.965914),
                        Arrays.asList(12.41225, 11.892309, 12.915928, 13.715923, 14.139373, 14.442924),
                        Arrays.asList(19.363148, 20.008375, 21.32118, 22.506603, 23.379343, 23.81858)
                },
                {
                        C_N_B_K,
                        Arrays.asList(18.521645, 20.01135, 22.082537, 23.988493, 25.454979, 26.180443),
                        Arrays.asList(14.766464, 13.406515, 14.55885, 15.356863, 15.603447, 15.85619),
                        Arrays.asList(17.169964, 17.624254, 19.360317, 20.862429, 21.884224, 22.43652)
                },
                {
                        C_N_B,
                        Arrays.asList(25.170364, 26.490019, 27.903, 29.252377, 30.346064, 30.832088),
                        Arrays.asList(21.52842, 21.56896, 22.555937, 23.390467, 23.920563, 24.235964),
                        Arrays.asList(23.653645, 24.43757, 25.669905, 26.801184, 27.65604, 28.068241)
                }
        };
    }

    @DataProvider(name = "summaryEbitValuesFixedPriceV2500")
    public Object[][] workscopeSummaryEbitValuesFixedPriceV2500() {
        return new Object[][] {
                {
                        C_N_A_K,
                        Arrays.asList(194688.0, 281589.0, 327373.0, 481417.0, 591703.0, 677463.0),
                        Arrays.asList(-95879.0, -111204.0, -119574.0, -47644.0, -18449.0, 5863.0),
                        Arrays.asList(98809.0, 170385.0, 207798.0, 433772.0, 573254.0, 683327.0)
                },
                {
                        C_N_A,
                        Arrays.asList(434223.0, 547149.0, 610920.0, 780395.0, 910447.0, 1014964.0),
                        Arrays.asList(36323.0, 45036.0, 51115.0, 144366.0, 194658.0, 237554.0),
                        Arrays.asList(470547.0, 592186.0, 662036.0, 924761.0, 1105106.0, 1252519.0)
                },
                {
                        C_N_B_K,
                        Arrays.asList(99718.0, 177035.0, 216571.0, 376142.0, 484787.0, 568062.0),
                        Arrays.asList(2698.0, -2828.0, -4442.0, 62346.0, 93521.0, 121258.0),
                        Arrays.asList(102417.0, 174207.0, 212129.0, 438489.0, 578309.0, 689321.0)
                },
                {
                        C_N_B,
                        Arrays.asList(534165.0, 655834.0, 727546.0, 913258.0, 1056130.0, 1173133.0),
                        Arrays.asList(288488.0, 332891.0, 362030.0, 473454.0, 548389.0, 615051.0),
                        Arrays.asList(822653.0, 988725.0, 1089577.0, 1386712.0, 1604519.0, 1788184.0)
                }
        };
    }

    @DataProvider(name = "summaryEbitPercentageValuesFixedPriceV2500")
    public Object[][] workscopeSummaryEbitPercentageValuesFixedPriceV2500() {
        return new Object[][] {
                {
                        C_N_A_K,
                        Arrays.asList(8.550043, 11.363122, 12.398391, 17.109095, 19.730228, 21.185003),
                        Arrays.asList(-6.2458467, -6.6111093, -6.670931, -2.4945884, -0.9066308, 0.27043784),
                        Arrays.asList(2.591974, 4.0956173, 4.6876287, 9.182828, 11.387793, 12.734274)
                },
                {
                        C_N_A,
                        Arrays.asList(15.335945, 17.769121, 18.633001, 22.351116, 24.48392, 25.61645),
                        Arrays.asList(1.6945074, 1.9219316, 2.0470245, 5.4256573, 6.866286, 7.864824),
                        Arrays.asList(9.458204, 10.920866, 11.462303, 15.031058, 16.862726, 17.937647)
                },
                {
                        C_N_B_K,
                        Arrays.asList(4.494463, 7.325572, 8.406416, 13.694102, 16.551733, 18.179579),
                        Arrays.asList(0.21630692, -0.20680171, -0.30414498, 3.9976008, 5.616385, 6.820577),
                        Arrays.asList(2.9545362, 4.6032486, 5.2547626, 10.182381, 12.588142, 14.060421)
                },
                {
                        C_N_B,
                        Arrays.asList(16.460232, 18.5583, 19.330318, 22.78034, 24.730469, 25.775257),
                        Arrays.asList(12.456253, 13.165741, 13.413137, 16.432861, 17.831764, 18.73744),
                        Arrays.asList(14.792739, 16.309202, 16.859123, 20.126104, 21.842339, 22.826338)
                }
        };
    }

    @DataProvider(name = "summaryEatPercentageValuesFixedPriceV2500")
    public Object[][] workscopeSummaryEatPercentageValuesFixedPriceV2500() {
        return new Object[][] {
                {
                        C_N_A_K,
                        Arrays.asList(6.412533, 8.522342, 9.298793, 12.831821, 14.797671, 15.888753),
                        Arrays.asList(-4.6843853, -4.958332, -5.003198, -1.8709413, -0.6799731, 0.20282838),
                        Arrays.asList(1.9439806, 3.0717132, 3.5157216, 6.8871207, 8.540845, 9.550706)
                },
                {
                        C_N_A,
                        Arrays.asList(11.501959, 13.326841, 13.974751, 16.763338, 18.36294, 19.212337),
                        Arrays.asList(1.2708806, 1.4414487, 1.5352684, 4.069243, 5.1497145, 5.8986177),
                        Arrays.asList(7.0936527, 8.190649, 8.596727, 11.273294, 12.647045, 13.453235)
                },
                {
                        C_N_B_K,
                        Arrays.asList(3.3708472, 5.494179, 6.304812, 10.270577, 12.4138, 13.634684),
                        Arrays.asList(0.1622302, -0.15510128, -0.22810873, 2.9982007, 4.212289, 5.115433),
                        Arrays.asList(2.215902, 3.4524367, 3.941072, 7.636786, 9.441107, 10.545316)
                },
                {
                        C_N_B,
                        Arrays.asList(12.345174, 13.918725, 14.497739, 17.085255, 18.547852, 19.331444),
                        Arrays.asList(9.34219, 9.874306, 10.059853, 12.324647, 13.373823, 14.05308),
                        Arrays.asList(11.094554, 12.231902, 12.644343, 15.094579, 16.381754, 17.119753)
                }
        };
    }

    @DataProvider(name = "summaryNetMarginePercentageFixedPriceV2500")
    public Object[][] workscopeSummaryNetMarginPercentageFixedPriceV2500() {
        return new Object[][] {
                {
                        C_N_A_K,
                        Arrays.asList(4.0125327, 6.1223416, 6.898793, 10.431821, 12.397671, 13.488753),
                        Arrays.asList(-7.084385, -7.358332, -7.4031982, -4.2709413, -3.079973, -2.1971717),
                        Arrays.asList(-0.4560194, 0.6717131, 1.1157215, 4.487121, 6.140845, 7.1507053)
                },
                {
                        C_N_A,
                        Arrays.asList(9.101959, 10.926841, 11.574751, 14.363338, 15.962939, 16.812338),
                        Arrays.asList(-1.1291194, -0.9585513, -0.8647316, 1.6692431, 2.7497144, 3.498618),
                        Arrays.asList(4.693653, 5.7906494, 6.1967273, 8.873294, 10.247045, 11.053235)
                },
                {
                        C_N_B_K,
                        Arrays.asList(0.9708473, 3.094179, 3.9048119, 7.870577, 10.0138, 11.234684),
                        Arrays.asList(-2.2377698, -2.5551014, -2.6281087, 0.5982006, 1.8122889, 2.715433),
                        Arrays.asList(-0.1840978, 1.0524366, 1.541072, 5.236786, 7.0411067, 8.145316)
                },
                {
                        C_N_B,
                        Arrays.asList(9.945174, 11.518725, 12.097739, 14.685255, 16.147852, 16.931442),
                        Arrays.asList(6.9421897, 7.4743056, 7.659853, 9.924647, 10.973823, 11.65308),
                        Arrays.asList(8.694554, 9.831902, 10.244343, 12.694579, 13.981754, 14.719753)
                }
        };
    }
}
