package com.lht.corecalculation.api.wssummarynteandfixprice;

import com.lht.corecalculation.base.BaseCocaApiTest;
import java.util.Arrays;
import org.testng.annotations.DataProvider;


import static com.lht.corecalculation.api.constants.GlobalConstants.WS01;

public class DataProvidersWorkscopeSummaryNteCfm565bPma extends BaseCocaApiTest {

    @DataProvider(name = "summaryProductionCostNteCfm565B_PMA")
    public Object[][] workscopeSummaryProductionCostDataProviderNteCfm565B_PMA() {
        return new Object[][] {
                {
                        WS01,
                        Arrays.asList(3068374.0, 3178659.0, 3313422.0, 3494396.0), // included production costs
                        Arrays.asList(1603241.0, 1701890.0, 1810611.0, 1922627.0), // excluded production costs
                        Arrays.asList(4671616.0, 4880550.0, 5124033.0, 5417023.0)  // total production costs
                }
        };
    }

    @DataProvider(name = "summaryDiscountsNteCfm565B_PMA")
    public Object[][] workscopeSummaryDiscountsDataProviderNteCfm565B_PMA() {
        return new Object[][] {
                {
                        WS01,
                        Arrays.asList(299505.0, 332264.0, 364750.0, 389431.0), // included
                        Arrays.asList(175990.0, 203507.0, 231401.0, 246842.0), // excluded
                        Arrays.asList(475495.0, 535772.0, 596152.0, 636273.0)  // total
                }
        };
    }

    @DataProvider(name = "summaryRevenueNteCfm565B_PMA")
    public Object[][] workscopeSummaryRevenueDataProviderNteCfm565B_PMA() {
        return new Object[][] {
                {
                        WS01,
                        Arrays.asList(4331981.0, 4604129.0, 4894616.0, 5204221.0), // included
                        Arrays.asList(1985120.0, 2111875.0, 2247959.0, 2390160.0), // excluded
                        Arrays.asList(6317102.0, 6716005.0, 7142576.0, 7594381.0)  // total
                }
        };
    }

    @DataProvider(name = "summarySurchargesNtePriceCfm565B_PMA")
    public Object[][] workscopeSummarySurchargesCostDataProviderNteCfm565B_PMA() {
        return new Object[][] {
                {
                        WS01,
                        Arrays.asList(217765.0, 207982.0, 212659.0, 219490.0), // included
                        Arrays.asList(113783.0, 111355.0, 116207.0, 120764.0), // excluded
                        Arrays.asList(331549.0, 319338.0, 328866.0, 340255.0)  // total
                }
        };
    }

    @DataProvider(name = "summaryProdCostInclDiscountsAndSurchargesFixedPriceCfm565B_PMA")
    public Object[][] workscopeSummaryProdCostInclDiscountsAndSurchargesDataProviderFixedPriceCfm565B_PMA() {
        return new Object[][] {
                {
                        WS01,
                        Arrays.asList(2986634.0, 3054377.0, 3161332.0, 3324455.0),  // included
                        Arrays.asList(1541034.0, 1609739.0, 1695416.0, 1796549.0),  // excluded
                        Arrays.asList(4527669.0, 4664116.0, 4856748.0, 5121004.0)   // total
                }
        };
    }

    @DataProvider(name = "summaryDb2ValuesFixedPriceCfm565B_PMA")
    public Object[][] workscopeSummaryDb2ValuesDataProviderFixedPriceCfm565B_PMA() {
        return new Object[][] {
                {
                        WS01,
                        Arrays.asList(1345346.0, 1549752.0, 1733284.0, 1879765.0),  // included
                        Arrays.asList(444085.0, 502136.0, 552543.0, 593611.0),      // excluded
                        Arrays.asList(1789432.0, 2051888.0, 2285827.0, 2473376.0)   // total
                }
        };
    }

    @DataProvider(name = "summaryDb2PercentageValuesFixedPriceCfm565B_PMA")
    public Object[][] workscopeDb2PercentageValuesDataProviderFixedPriceCfm565B_PMA() {
        return new Object[][] {
                {
                        WS01,
                        Arrays.asList(31.056152, 33.660053, 35.41206, 36.120014),    // included
                        Arrays.asList(22.370731, 23.776789, 24.579763, 24.835627),   // excluded
                        Arrays.asList(28.3268, 30.55222, 32.002846, 32.56851)     // total
                }
        };
    }

    @DataProvider(name = "summaryEbitValuesFixedPriceCfm565B_PMA")
    public Object[][] workscopeSummaryEbitValuesDataProviderFixedPriceCfm565B_PMA() {
        return new Object[][] {
                {
                        WS01,
                        Arrays.asList(971340.0, 1250210.0, 1457122.0, 1614868.0), // included
                        Arrays.asList(248665.0, 341757.0, 401635.0, 447864.0),    // excluded
                        Arrays.asList(1220006.0, 1591967.0, 1858758.0, 2062733.0) // total
                }
        };
    }

    @DataProvider(name = "summaryEbitPercentageValuesFixedPriceCfm565B_PMA")
    public Object[][] workscopeEbitPercentageValuesDataProviderFixedPriceCfm565B_PMA() {
        return new Object[][] {
                {
                        WS01,
                        Arrays.asList(22.422552, 27.1541, 29.769907, 31.029976), // included
                        Arrays.asList(12.526493, 16.182663, 17.866663, 18.737837),  // excluded
                        Arrays.asList(19.312761, 23.704086, 26.02364, 27.161303)  // total
                }
        };
    }

    @DataProvider(name = "summaryEatPercentageValuesFixedPriceCfm565B_PMA")
    public Object[][] workscopeEatPercentageValuesDataProviderFixedPriceCfm565B_PMA() {
        return new Object[][] {
                {
                        WS01,
                        Arrays.asList(16.816914, 20.365576, 22.32743, 23.272482), // included
                        Arrays.asList(9.39487, 12.136997, 13.399997, 14.053377),    // excluded
                        Arrays.asList(14.484572, 17.778065, 19.517729, 20.370977)   // total
                }
        };
    }

    @DataProvider(name = "summaryNetMarginPercentageFixedPriceCfm565B_PMA")
    public Object[][] workscopeNetMarginPercentageDataProviderFixedPriceCfm565B_PMA() {
        return new Object[][] {
                {
                        WS01,
                        Arrays.asList(14.416914, 17.965576, 19.92743, 20.872482), // included
                        Arrays.asList(6.9948697, 9.736997, 10.999997, 11.653377),   // excluded
                        Arrays.asList(12.084572, 15.378065, 17.11773, 17.970978)   // total
                }
        };
    }

}