package com.lht.corecalculation.api.wssummarynteandfixprice;

import com.lht.corecalculation.base.BaseCocaApiTest;
import java.util.Arrays;
import org.testng.annotations.DataProvider;


import static com.lht.corecalculation.api.constants.GlobalConstants.CPR;
import static com.lht.corecalculation.api.constants.GlobalConstants.HPC;
import static com.lht.corecalculation.api.constants.GlobalConstants.HPT_LLP;
import static com.lht.corecalculation.api.constants.GlobalConstants.HPT_S1B;
import static com.lht.corecalculation.api.constants.GlobalConstants.NSV;

public class DataProvidersWorkscopeSummaryFixedPriceLeap1A extends BaseCocaApiTest {

    @DataProvider(name = "summaryProductionCostFixedPriceLeap1a")
    public Object[][] workscopeSummaryProductionCostDataProviderFixedPriceLeap1a() {
        return new Object[][] {
                {CPR, Arrays.asList(1654139.0, 1699142.0, 1794108.0, 1882746.0, 1963713.0, 2063595.0)},
                {HPC, Arrays.asList(241751.0, 225872.0, 230754.0, 232770.0, 232961.0, 242138.0)},
                {HPT_S1B, Arrays.asList(247229.0, 238147.0, 245997.0, 251846.0, 256535.0, 268649.0)},
                {HPT_LLP, Arrays.asList(261049.0, 250949.0, 259034.0, 264940.0, 269570.0, 282167.0)},
                {NSV, Arrays.asList(231713.0, 216573.0, 221283.0, 223258.0, 223493.0, 232320.0)}
        };
    }

    @DataProvider(name = "summaryDiscountsFixedPriceLeap1a")
    public Object[][] workscopeSummaryDiscountsDataProviderFixedPriceLeap1a() {
        return new Object[][] {
                {CPR, Arrays.asList(271559.0, 364565.0, 407722.0, 375228.0, 430219.0, 485816.0)},
                {HPC, Arrays.asList(2034.0, 2464.0, 2874.0, 2756.0, 3051.0, 3539.0)},
                {HPT_S1B, Arrays.asList(9420.0, 13657.0, 16311.0, 13291.0, 15649.0, 18436.0)},
                {HPT_LLP, Arrays.asList(9420.0, 13657.0, 16311.0, 13291.0, 15649.0, 18436.0)},
                {NSV, Arrays.asList(2034.0, 2464.0, 2874.0, 2756.0, 3051.0, 3539.0)}
        };
    }

    @DataProvider(name = "summaryRevenueFixedPriceLeap1a")
    public Object[][] workscopeSummaryRevenueDataProviderFixedPriceLeap1a() {
        return new Object[][] {
                {CPR, Arrays.asList(2465668.0, 2660883.0, 2853305.0, 3092560.0, 3350687.0, 3636031.0)},
                {HPC, Arrays.asList(234209.0, 255833.0, 279489.0, 305342.0, 333597.0, 364478.0)},
                {HPT_S1B, Arrays.asList(269927.0, 293241.0, 318895.0, 346831.0, 377257.0, 410396.0)},
                {HPT_LLP, Arrays.asList(269927.0, 293241.0, 318895.0, 346831.0, 377257.0, 410396.0)},
                {NSV, Arrays.asList(256529.0, 280369.0, 306454.0, 334970.0, 366145.0, 400225.0)}
        };
    }

    @DataProvider(name = "summarySurchargesCostFixedPriceLeap1a")
    public Object[][] workscopeSummarySurchargesCostDataProviderFixedPriceLeap1a() {
        return new Object[][] {
                {CPR, Arrays.asList(93688.0, 112016.0, 106492.0, 109429.0, 116128.0, 124858.0)},
                {HPC, Arrays.asList(94568.0, 112755.0, 107998.0, 117148.0, 119165.0, 122775.0)},
                {HPT_S1B, Arrays.asList(32883.0, 34611.0, 33757.0, 35168.0, 35152.0, 36212.0)},
                {HPT_LLP, Arrays.asList(34480.0, 36235.0, 35331.0, 36782.0, 36739.0, 37835.0)},
                {NSV, Arrays.asList(94066.0, 112290.0, 107524.0, 116672.0, 118692.0, 122284.0)}
        };
    }

    @DataProvider(name = "summaryProdCostInclDiscountsAndSurchargesFixedPriceLeap1a")
    public Object[][] workscopeSummaryProdCostInclDiscountsAndSurchargesDataProviderFixedPriceLeap1a() {
        return new Object[][] {
                {CPR, Arrays.asList(2060815.0, 2084151.0, 2172532.0, 2373703.0, 2498933.0, 2647474.0)},
                {HPC, Arrays.asList(334285.0, 336164.0, 335878.0, 347161.0, 349074.0, 361373.0)},
                {HPT_S1B, Arrays.asList(270903.0, 259297.0, 263643.0, 273923.0, 276238.0, 286631.0)},
                {HPT_LLP, Arrays.asList(286320.0, 273722.0, 278253.0, 288630.0, 290860.0, 301773.0)},
                {NSV, Arrays.asList(323745.0, 326400.0, 325934.0, 337174.0, 339133.0, 351065.0)}
        };
    }

    @DataProvider(name = "summaryDb2ValuesFixedPriceLeap1a")
    public Object[][] workscopeSummaryDb2ValuesFixedPriceLeap1a() {
        return new Object[][] {
                {CPR, Arrays.asList(404853.0, 576732.0, 680773.0, 718856.0, 851753.0, 988556.0)},
                {HPC, Arrays.asList(-100075.0, -80330.0, -56388.0, -41818.0, -15477.0, 3104.0)},
                {HPT_S1B, Arrays.asList(-976.0, 33944.0, 55252.0, 72908.0, 101019.0, 123765.0)},
                {HPT_LLP, Arrays.asList(-16393.0, 19518.0, 40641.0, 58201.0, 86397.0, 108623.0)},
                {NSV, Arrays.asList(-67215.0, -46031.0, -19479.0, -2203.0, 27011.0, 49160.0)}
        };
    }

    @DataProvider(name = "summaryDb2PercentageValuesFixedPriceLeap1a")
    public Object[][] workscopeSummaryDb2PercentageValuesFixedPriceLeap1a() {
        return new Object[][] {
                {CPR, Arrays.asList(16.421, 21.674460, 23.859106, 23.244, 25.420260, 27.187796)},
                {HPC, Arrays.asList(-42.729073, -31.399544, -20.175657, -13.695664, -4.639475, 0.8517445)},
                {HPT_S1B, Arrays.asList(-0.362, 11.575432, 17.326063, 21.021, 26.777332, 30.157404)},
                {HPT_LLP, Arrays.asList(-6.074, 6.656208, 12.744490, 16.780775, 22.901556, 26.468020)},
                {NSV, Arrays.asList(-26.202005, -16.418062, -6.356438, -0.65792155, 7.377293, 12.283203)}
        };
    }

    @DataProvider(name = "summaryEbitValuesFixedPriceLeap1a")
    public Object[][] workscopeSummaryEbitValuesFixedPriceLeap1a() {
        return new Object[][] {
                {CPR, Arrays.asList(267032.0, 440537.0, 523320.0, 587618.0, 725425.0, 861515.0)},
                {HPC, Arrays.asList(-198630.0, -178885.0, -170274.0, -133802.0, -100891.0, -79024.0)},
                {HPT_S1B, Arrays.asList(-20754.0, 15674.0, 34697.0, 56866.0, 86688.0, 110201.0)},
                {HPT_LLP, Arrays.asList(-37044.0, 458.0, 19203.0, 41476.0, 71466.0, 94496.0)},
                {NSV, Arrays.asList(-165770.0, -144585.0, -133364.0, -94188.0, -58402.0, -32968.0)}
        };
    }

    @DataProvider(name = "summaryEbitPercentageValuesFixedPriceLeap1a")
    public Object[][] workscopeSummaryEbitPercentageValuesFixedPriceLeap1a() {
        return new Object[][] {
                {CPR, Arrays.asList(10.830042, 16.556076, 18.340858, 19.001053, 21.650053, 23.693834)},
                {HPC, Arrays.asList(-84.808624, -69.922386, -60.923187, -43.820553, -30.243332, -21.681498)},
                {HPT_S1B, Arrays.asList(-7.697, 5.350376, 10.880634, 16.397, 22.978662, 26.852531)},
                {HPT_LLP, Arrays.asList(-13.719, 0.156241, 6.022005, 11.952, 18.943735, 23.025570)},
                {NSV, Arrays.asList(-64.62034, -51.56972, -43.51862, -28.118275, -15.950575, -8.237398)}
        };
    }

    @DataProvider(name = "summaryEatPercentageValuesFixedPriceLeap1a")
    public Object[][] workscopeSummaryEatPercentageValuesFixedPriceLeap1a() {
        return new Object[][] {
                {CPR, Arrays.asList(8.122532, 12.417057, 13.755644, 14.250790, 16.237540, 17.770376)},
                {HPC, Arrays.asList(-63.60647, -52.441788, -45.69239, -32.865414, -22.682499, -16.261124)},
                {HPT_S1B, Arrays.asList(-5.773, 4.012532, 8.160476, 12.298, 17.233997, 20.139399)},
                {HPT_LLP, Arrays.asList(-10.289, 0.117181, 4.516504, 8.964, 14.207801, 17.269176)},
                {NSV, Arrays.asList(-48.465256, -38.67729, -32.638966, -21.088705, -11.962931, -6.178049)}
        };
    }

    @DataProvider(name = "summaryNetMarginePercentageFixedPriceLeap1a")
    public Object[][] workscopeSummaryNetMarginePercentageFixedPriceLeap1a() {
        return new Object[][] {
                {CPR, Arrays.asList(5.722531, 10.017057, 11.355644, 11.850789, 13.837540, 15.370376)},
                {HPC, Arrays.asList(-66.00647, -54.841785, -48.092392, -35.265415, -25.082499, -18.661123)},
                {HPT_S1B, Arrays.asList(-8.173, 1.612532, 5.760476, 9.898, 14.833997, 17.739397)},
                {HPT_LLP, Arrays.asList(-12.689, -2.282819, 2.116504, 6.564, 11.807801, 14.869177)},
                {NSV, Arrays.asList(-50.865253, -41.077293, -35.038963, -23.488707, -14.362931, -8.578049)}
        };
    }
}