package com.lht.corecalculation.api.scrapcaps;

import com.lht.corecalculation.base.BaseCocaApiTest;
import io.restassured.response.Response;
import java.io.IOException;
import org.testng.annotations.Test;


import static com.lht.corecalculation.api.constants.GlobalConstants.ADMIN_USER_PASSWORD;
import static com.lht.corecalculation.api.constants.GlobalConstants.ADMIN_USER_USERNAME;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_CFM56_5B_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_CFM56_7B_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_LEAP_1A_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_LEAP_1B_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_V2500_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_200;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_400;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_403;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.VALID_CLP_THRESHOLD_VALUE;
import static com.lht.corecalculation.api.constants.GlobalConstants.VALID_SCRAP_CAP_VALUE;
import static com.lht.corecalculation.api.utils.ScrapCapsUtil.executePutScrapCapsTest;
import static com.lht.corecalculation.api.utils.ScrapCapsUtil.validatePutScrapCapsResponse;
import static org.testng.Assert.assertEquals;

public class PutScrapCapsFixPriceAndNteApiTests extends BaseCocaApiTest {

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "scrapCapNegativeDataProvider")
    public void testPutScrapCaps_V2500_WithNegativeValues_AssertsBadRequest(
            Float customScrapCapValue,
            Integer clpThreshold
    ) throws IOException {
        String adminToken = utils.sso.getAccessTokenFor(ADMIN_USER_USERNAME, ADMIN_USER_PASSWORD);
        Response putResponse = executePutScrapCapsTest(adminToken, ENGINE_V2500_QUOTATION_ID, customScrapCapValue, clpThreshold);
        assertEquals(putResponse.statusCode(), RESPONSE_STATUS_CODE_400, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "viewOnlyAccessTokenProvider")
    public void testPutScrapCaps_V2500_AsViewOnlyUser_AssertsForbiddenAccess(String adminToken) throws IOException {
        Response putResponse = executePutScrapCapsTest(adminToken, ENGINE_V2500_QUOTATION_ID, VALID_SCRAP_CAP_VALUE, VALID_CLP_THRESHOLD_VALUE);
        assertEquals(putResponse.statusCode(), RESPONSE_STATUS_CODE_400, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "scrapCapDataProvider")
    public void testPutScrapCaps_V2500_WithCustomValues_AssertsSuccess(Float customScrapCapValue, Integer clpThreshold) throws IOException {
        String adminToken = utils.sso.getAccessTokenFor(ADMIN_USER_USERNAME, ADMIN_USER_PASSWORD);
        Response putResponse = executePutScrapCapsTest(adminToken, ENGINE_V2500_QUOTATION_ID, customScrapCapValue, clpThreshold);
        assertEquals(putResponse.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
        validatePutScrapCapsResponse(putResponse, customScrapCapValue, clpThreshold);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "scrapCapDataProvider")
    public void testPutScrapCaps_LEAP_1A_WithCustomValues_AssertsSuccess(
            Float customScrapCapValue,
            Integer clpThreshold
    ) throws IOException {
        String adminToken = utils.sso.getAccessTokenFor(ADMIN_USER_USERNAME, ADMIN_USER_PASSWORD);
        Response putResponse = executePutScrapCapsTest(adminToken, ENGINE_LEAP_1A_QUOTATION_ID, customScrapCapValue, clpThreshold);
        assertEquals(putResponse.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
        validatePutScrapCapsResponse(putResponse, customScrapCapValue, clpThreshold);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "scrapCapDataProvider")
    public void testPutScrapCaps_LEAP_1B_WithCustomValues_AssertsSuccess(
            Float customScrapCapValue,
            Integer clpThreshold
    ) throws IOException {
        String adminToken = utils.sso.getAccessTokenFor(ADMIN_USER_USERNAME, ADMIN_USER_PASSWORD);
        Response putResponse = executePutScrapCapsTest(adminToken, ENGINE_LEAP_1B_QUOTATION_ID, customScrapCapValue, clpThreshold);
        assertEquals(putResponse.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
        validatePutScrapCapsResponse(putResponse, customScrapCapValue, clpThreshold);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "scrapCapDataProvider")
    public void testPutScrapCaps_CFM56_5B_WithCustomValues_AssertsSuccess(
            Float customScrapCapValue,
            Integer clpThreshold
    ) throws IOException {
        String adminToken = utils.sso.getAccessTokenFor(ADMIN_USER_USERNAME, ADMIN_USER_PASSWORD);
        Response putResponse = executePutScrapCapsTest(adminToken, ENGINE_CFM56_5B_QUOTATION_ID, customScrapCapValue, clpThreshold);
        assertEquals(putResponse.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
        validatePutScrapCapsResponse(putResponse, customScrapCapValue, clpThreshold);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "scrapCapDataProvider")
    public void testPutScrapCaps_CFM56_7B_WithCustomValues_AssertsSuccess(
            Float customScrapCapValue,
            Integer clpThreshold
    ) throws IOException {
        String adminToken = utils.sso.getAccessTokenFor(ADMIN_USER_USERNAME, ADMIN_USER_PASSWORD);
        Response putResponse = executePutScrapCapsTest(adminToken, ENGINE_CFM56_7B_QUOTATION_ID, customScrapCapValue, clpThreshold);
        assertEquals(putResponse.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
        validatePutScrapCapsResponse(putResponse, customScrapCapValue, clpThreshold);
    }
}
