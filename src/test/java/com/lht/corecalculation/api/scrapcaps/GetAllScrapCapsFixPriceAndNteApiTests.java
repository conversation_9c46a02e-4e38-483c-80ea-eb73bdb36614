package com.lht.corecalculation.api.scrapcaps;

import com.lht.corecalculation.api.handlingcharges.ClusterOrderAssertion;
import com.lht.corecalculation.api.handlingcharges.ClusterPartsNameOrderAssertion;
import com.lht.corecalculation.api.pojo.dto.scrapcaps.ScrapCapResponseDto;
import com.lht.corecalculation.api.request.scrapcaps.GetScrapCapsRequest;
import com.lht.corecalculation.base.BaseCocaApiTest;
import io.restassured.response.Response;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import org.testng.annotations.Test;


import static com.lht.corecalculation.api.constants.GlobalConstants.ADMIN_TOKEN;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_CFM56_5B_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_LEAP_1A_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_V2500_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ERROR_DETAILS_MISMATCH_AS_MESSAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.NO_ROLE_TOKEN;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_200;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_401;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_403;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.USER_1_TOKEN;
import static com.lht.corecalculation.api.constants.GlobalConstants.VIEW_ONLY_TOKEN;
import static com.lht.corecalculation.api.utils.ScrapCapsUtil.getScrapCaps;
import static com.lht.corecalculation.api.utils.ScrapCapsUtil.getUniqueClusterNames;
import static com.lht.corecalculation.api.utils.ScrapCapsUtil.getUniquePartWithoutKitComponentAndPartsPackage;
import static org.testng.Assert.assertEquals;

public class GetAllScrapCapsFixPriceAndNteApiTests extends BaseCocaApiTest {

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"})
    public void testScrapCapsGet_V2500_ReturnsStatusCode200() {
        String adminToken = utils.context.getContextItem(ADMIN_TOKEN);
        GetScrapCapsRequest getScrapCapsRequest = new GetScrapCapsRequest(ENGINE_V2500_QUOTATION_ID).withBearerToken(adminToken);

        Response response = getScrapCapsRequest.callAPI();
        assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"})
    public void testScrapCapsGet_V2500_ViewOnlyUser_ReturnsStatusCode200() {
        String adminToken = utils.context.getContextItem(VIEW_ONLY_TOKEN);
        GetScrapCapsRequest getScrapCapsRequest = new GetScrapCapsRequest(ENGINE_V2500_QUOTATION_ID).withBearerToken(adminToken);

        Response response = getScrapCapsRequest.callAPI();
        assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"})
    public void testScrapCapsGet_V2500_User1_ReturnsStatusCode200() {
        String adminToken = utils.context.getContextItem(USER_1_TOKEN);
        GetScrapCapsRequest getScrapCapsRequest = new GetScrapCapsRequest(ENGINE_V2500_QUOTATION_ID).withBearerToken(adminToken);

        Response response = getScrapCapsRequest.callAPI();
        assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"})
    public void testScrapCapsGet_V2500_NoRoleToken_ReturnsStatusCode403() {
        String adminToken = utils.context.getContextItem(NO_ROLE_TOKEN);
        GetScrapCapsRequest getScrapCapsRequest = new GetScrapCapsRequest(ENGINE_V2500_QUOTATION_ID).withBearerToken(adminToken);

        Response response = getScrapCapsRequest.callAPI();
        assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_401, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "adminAccessTokenProvider")
    public void testScrapCapsClusterOrder_V2500_AssertsCorrectOrder(String accessToken) throws IOException {
        Response response = getScrapCaps(accessToken, ENGINE_V2500_QUOTATION_ID);
        assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        ScrapCapResponseDto scrapCapResponseDto = utils.convert.jsonToDto(response, ScrapCapResponseDto.class);
        List<String> actualClusterOrderList = getUniqueClusterNames(scrapCapResponseDto);
        List<String> expectedClusterOrderList = Arrays.asList(ClusterOrderAssertion.getScrapCapsClusterOrderV2500());

        assertEquals(actualClusterOrderList, expectedClusterOrderList, ERROR_DETAILS_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "adminAccessTokenProvider")
    public void testScrapCapsClusterOrder_LEAP_1A_AssertsCorrectOrder(String accessToken) throws IOException {
        Response response = getScrapCaps(accessToken, ENGINE_LEAP_1A_QUOTATION_ID);
        assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        ScrapCapResponseDto scrapCapResponseDto = utils.convert.jsonToDto(response, ScrapCapResponseDto.class);
        List<String> actualClusterOrderList = getUniqueClusterNames(scrapCapResponseDto);
        List<String> expectedClusterOrderList = Arrays.asList(ClusterOrderAssertion.getScrapCapsClusterOrderLeap1a());

        assertEquals(actualClusterOrderList, expectedClusterOrderList, ERROR_DETAILS_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "adminAccessTokenProvider")
    public void testScrapCapsClusterOrder_CFM56_5B_AssertsCorrectOrder(String accessToken) throws IOException {
        Response response = getScrapCaps(accessToken, ENGINE_CFM56_5B_QUOTATION_ID);
        assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        ScrapCapResponseDto scrapCapResponseDto = utils.convert.jsonToDto(response, ScrapCapResponseDto.class);
        List<String> actualClusterOrderList = getUniqueClusterNames(scrapCapResponseDto);
        List<String> expectedClusterOrderList = Arrays.asList(ClusterOrderAssertion.getScrapCapsClusterOrderCfm565B());

        assertEquals(actualClusterOrderList, expectedClusterOrderList, ERROR_DETAILS_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "adminAccessTokenProvider")
    public void testScrapCapsPartsOrder_V2500_AssertsCorrectOrder(String accessToken) throws IOException {
        Response response = getScrapCaps(accessToken, ENGINE_V2500_QUOTATION_ID);
        assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        ScrapCapResponseDto scrapCapResponseDto = utils.convert.jsonToDto(response, ScrapCapResponseDto.class);

        List<String> actualClusterPartsOrderList = getUniquePartWithoutKitComponentAndPartsPackage(scrapCapResponseDto);
        List<String> expectedClusterPartsOrderList = Arrays.asList(ClusterPartsNameOrderAssertion.getScrapCapsClusterPartsOrderV2500());

        assertEquals(actualClusterPartsOrderList, expectedClusterPartsOrderList, ERROR_DETAILS_MISMATCH_AS_MESSAGE);
    }
}