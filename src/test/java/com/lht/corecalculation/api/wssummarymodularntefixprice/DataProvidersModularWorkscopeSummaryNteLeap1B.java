package com.lht.corecalculation.api.wssummarymodularntefixprice;

import com.lht.corecalculation.base.BaseCocaApiTest;
import java.util.Arrays;
import org.testng.annotations.DataProvider;


import static com.lht.corecalculation.api.constants.GlobalConstants.WS01;
import static com.lht.corecalculation.api.constants.GlobalConstants.WS02;
import static com.lht.corecalculation.api.constants.GlobalConstants.WS03;
import static com.lht.corecalculation.api.constants.GlobalConstants.WS03_1;
import static com.lht.corecalculation.api.constants.GlobalConstants.WS04;
import static com.lht.corecalculation.api.constants.GlobalConstants.WS05;
import static com.lht.corecalculation.api.constants.GlobalConstants.WS06;
import static com.lht.corecalculation.api.constants.GlobalConstants.WS07;
import static com.lht.corecalculation.api.constants.GlobalConstants.WS08;
import static com.lht.corecalculation.api.constants.GlobalConstants.WS09;
import static com.lht.corecalculation.api.constants.GlobalConstants.WS10;

public class DataProvidersModularWorkscopeSummaryNteLeap1B extends BaseCocaApiTest {

    @DataProvider(name = "summaryProductionCostNteLeap1b")
    public Object[][] workscopeModularSummaryProductionCostDataProviderNteLeap1b() {
        return new Object[][] {
                {
                        WS03,
                        Arrays.asList(2530006.0, 2641225.0, 2793478.0, 2984258.0),
                        Arrays.asList(6065892.0, 6346061.0, 6687539.0, 6817530.0),
                        Arrays.asList(8595898.0, 8987287.0, 9481017.0, 9801789.0)
                },
                {
                        WS05,
                        Arrays.asList(652834.0, 665059.0, 698725.0, 762325.0),
                        Arrays.asList(2007303.0, 2093255.0, 2223037.0, 1964078.0),
                        Arrays.asList(2660138.0, 2758314.0, 2921762.0, 2726403.0)
                },
                {
                        WS07,
                        Arrays.asList(568637.0, 577422.0, 606042.0, 642393.0),
                        Arrays.asList(633656.0, 662250.0, 703422.0, 669860.0),
                        Arrays.asList(1202293.0, 1239672.0, 1309464.0, 1312253.0)
                },
                {
                        WS08,
                        Arrays.asList(202255.0, 196050.0, 202555.0, 207519.0),
                        Arrays.asList(0.0, 0.0, 0.0, 0.0),
                        Arrays.asList(202255.0, 196050.0, 202555.0, 207519.0)
                },
                {
                        WS06,
                        Arrays.asList(396000.0, 396029.0, 414018.0, 430803.0),
                        Arrays.asList(528125.0, 567863.0, 603024.0, 614931.0),
                        Arrays.asList(924125.0, 963893.0, 1017042.0, 1045735.0)
                },
                {
                        WS10,
                        Arrays.asList(242583.0, 233527.0, 240808.0, 246058.0),
                        Arrays.asList(985530.0, 1047421.0, 1112361.0, 1181327.0),
                        Arrays.asList(1228113.0, 1280948.0, 1353169.0, 1427386.0)
                },
                {
                        WS01,
                        Arrays.asList(1253650.0, 1297974.0, 1372819.0, 1446168.0),
                        Arrays.asList(3767984.0, 3926858.0, 4109633.0, 4349110.0),
                        Arrays.asList(5021635.0, 5224833.0, 5482452.0, 5795279.0)
                },
                {
                        WS03_1,
                        Arrays.asList(2370220.0, 2466318.0, 2603984.0, 2783611.0),
                        Arrays.asList(5874932.0, 6185659.0, 6551261.0, 6672803.0),
                        Arrays.asList(8245152.0, 8651977.0, 9155246.0, 9456415.0)
                },
                {
                        WS02,
                        Arrays.asList(1358744.0, 1408836.0, 1489895.0, 1591358.0),
                        Arrays.asList(4300000.0, 4481201.0, 4698346.0, 4840767.0),
                        Arrays.asList(5658744.0, 5890037.0, 6188241.0, 6432125.0)
                },
                {
                        WS09,
                        Arrays.asList(153219.0, 146225.0, 150386.0, 153115.0),
                        Arrays.asList(5497.0, 5727.0, 6082.0, 6460.0),
                        Arrays.asList(158716.0, 151953.0, 156469.0, 159575.0)
                },
                {
                        WS04,
                        Arrays.asList(3441894.0, 3558793.0, 3751117.0, 4004680.0),
                        Arrays.asList(7098003.0, 7401076.0, 7825315.0, 7669817.0),
                        Arrays.asList(1.0539898E7, 1.0959869E7, 1.1576432E7, 1.1674497E7)
                }
        };
    }

    @DataProvider(name = "summaryDiscountsNteLeap1b")
    public Object[][] workscopeModularSummaryDiscountsDataProviderNteLeap1b() {
        return new Object[][] {
                {
                        WS03,
                        Arrays.asList(241341.0, 321974.0, 359253.0, 334036.0),
                        Arrays.asList(769436.0, 1167606.0, 1367578.0, 1016050.0),
                        Arrays.asList(1010777.0, 1489580.0, 1726831.0, 1350087.0)
                },
                {
                        WS05,
                        Arrays.asList(65753.0, 91236.0, 103916.0, 96217.0),
                        Arrays.asList(276098.0, 402189.0, 477546.0, 308357.0),
                        Arrays.asList(341851.0, 493426.0, 581462.0, 404574.0)
                },
                {
                        WS07,
                        Arrays.asList(51228.0, 72338.0, 81926.0, 70951.0),
                        Arrays.asList(83208.0, 124337.0, 145520.0, 100726.0),
                        Arrays.asList(134437.0, 196676.0, 227446.0, 171678.0)
                },
                {
                        WS08,
                        Arrays.asList(8451.0, 11836.0, 14007.0, 11709.0),
                        Arrays.asList(0.0, 0.0, 0.0, 0.0),
                        Arrays.asList(8451.0, 11836.0, 14007.0, 11709.0)
                },
                {
                        WS06,
                        Arrays.asList(99988.0, 120642.0, 133153.0, 129603.0),
                        Arrays.asList(37859.0, 56315.0, 67001.0, 47062.0),
                        Arrays.asList(137847.0, 176958.0, 200154.0, 176666.0)
                },
                {
                        WS10,
                        Arrays.asList(9089.0, 13228.0, 15727.0, 12709.0),
                        Arrays.asList(137974.0, 209484.0, 250281.0, 194919.0),
                        Arrays.asList(147064.0, 222712.0, 266008.0, 207628.0)
                },
                {
                        WS01,
                        Arrays.asList(158905.0, 209740.0, 233416.0, 213601.0),
                        Arrays.asList(494478.0, 740860.0, 863677.0, 667919.0),
                        Arrays.asList(653383.0, 950601.0, 1097093.0, 881521.0)
                },
                {
                        WS03_1,
                        Arrays.asList(213430.0, 285384.0, 316673.0, 294620.0),
                        Arrays.asList(742702.0, 1136508.0, 1337855.0, 992902.0),
                        Arrays.asList(956133.0, 1421893.0, 1654528.0, 1287523.0)
                },
                {
                        WS02,
                        Arrays.asList(178063.0, 233256.0, 259661.0, 244646.0),
                        Arrays.asList(568960.0, 848294.0, 992033.0, 746530.0),
                        Arrays.asList(747024.0, 1081550.0, 1251694.0, 991177.0)
                },
                {
                        WS09,
                        Arrays.asList(4532.0, 6079.0, 7160.0, 6195.0),
                        Arrays.asList(769.0, 1110.0, 1326.0, 1032.0),
                        Arrays.asList(5301.0, 7190.0, 8486.0, 7228.0)
                },
                {
                        WS04,
                        Arrays.asList(390650.0, 512500.0, 569653.0, 532517.0),
                        Arrays.asList(941616.0, 1376803.0, 1626256.0, 1164083.0),
                        Arrays.asList(1332267.0, 1889304.0, 2195909.0, 1696600.0)
                }
        };
    }

    @DataProvider(name = "summaryRevenueNteLeap1b")
    public Object[][] workscopeModularSummaryRevenueDataProviderNteLeap1b() {
        return new Object[][] {
                {
                        WS03,
                        Arrays.asList(1025648.0, 1086292.0, 1144652.0, 1206812.0),
                        Arrays.asList(6161644.0, 6480539.0, 6829804.0, 6959874.0),
                        Arrays.asList(7187292.0, 7566832.0, 7974456.0, 8166687.0)
                },
                {
                        WS05,
                        Arrays.asList(235278.0, 240779.0, 246581.0, 252759.0),
                        Arrays.asList(2053304.0, 2162541.0, 2296855.0, 2036268.0),
                        Arrays.asList(2288583.0, 2403320.0, 2543437.0, 2289027.0)
                },
                {
                        WS07,
                        Arrays.asList(235278.0, 240779.0, 246581.0, 252759.0),
                        Arrays.asList(656249.0, 692289.0, 735389.0, 700977.0),
                        Arrays.asList(891528.0, 933068.0, 981971.0, 953736.0)
                },
                {
                        WS08,
                        Arrays.asList(172225.0, 174581.0, 177080.0, 179750.0),
                        Arrays.asList(0.0, 0.0, 0.0, 0.0),
                        Arrays.asList(172225.0, 174581.0, 177080.0, 179750.0)
                },
                {
                        WS06,
                        Arrays.asList(183956.0, 187410.0, 191110.0, 195093.0),
                        Arrays.asList(538544.0, 581327.0, 617362.0, 629450.0),
                        Arrays.asList(722501.0, 768737.0, 808472.0, 824544.0)
                },
                {
                        WS10,
                        Arrays.asList(220129.0, 223676.0, 227379.0, 230290.0),
                        Arrays.asList(990280.0, 1052498.0, 1117787.0, 1187127.0),
                        Arrays.asList(1210409.0, 1276174.0, 1345166.0, 1417418.0)
                },
                {
                        WS01,
                        Arrays.asList(367663.0, 380896.0, 394970.0, 410042.0),
                        Arrays.asList(3814643.0, 3997177.0, 4183580.0, 4427178.0),
                        Arrays.asList(4182306.0, 4378074.0, 4578551.0, 4837220.0)
                },
                {
                        WS03_1,
                        Arrays.asList(1025117.0, 1085470.0, 1143514.0, 1205330.0),
                        Arrays.asList(5966378.0, 6314052.0, 6687576.0, 6808798.0),
                        Arrays.asList(6991496.0, 7399522.0, 7831090.0, 8014128.0)
                },
                {
                        WS02,
                        Arrays.asList(386831.0, 401720.0, 417102.0, 434928.0),
                        Arrays.asList(4356942.0, 4567931.0, 4789783.0, 4933702.0),
                        Arrays.asList(4743773.0, 4969652.0, 5206885.0, 5368631.0)
                },
                {
                        WS09,
                        Arrays.asList(166751.0, 168594.0, 170533.0, 172590.0),
                        Arrays.asList(5717.0, 6015.0, 6388.0, 6784.0),
                        Arrays.asList(172468.0, 174610.0, 176921.0, 179375.0)
                },
                {
                        WS04,
                        Arrays.asList(1153139.0, 1221875.0, 1289013.0, 1360725.0),
                        Arrays.asList(7230831.0, 7600927.0, 8037558.0, 7880565.0),
                        Arrays.asList(8383970.0, 8822802.0, 9326572.0, 9241290.0)
                }
        };
    }

    @DataProvider(name = "summaryModularSurchargesCostNteLeap1b")
    public Object[][] workscopeModularSummarySurchargesCostDataProviderNteLeap1b() {
        return new Object[][] {
                {
                        WS03,
                        Arrays.asList(103790.0, 124122.0, 118614.0, 123116.0),
                        Arrays.asList(248845.0, 298227.0, 283961.0, 281260.0),
                        Arrays.asList(352636.0, 422349.0, 402576.0, 404376.0)
                },
                {
                        WS05,
                        Arrays.asList(64209.0, 69336.0, 70243.0, 81193.0),
                        Arrays.asList(197429.0, 218234.0, 223484.0, 209189.0),
                        Arrays.asList(261639.0, 287571.0, 293728.0, 290383.0)
                },
                {
                        WS07,
                        Arrays.asList(75188.0, 84110.0, 83358.0, 93261.0),
                        Arrays.asList(83786.0, 96466.0, 96753.0, 97248.0),
                        Arrays.asList(158975.0, 180577.0, 180112.0, 190510.0)
                },
                {
                        WS08,
                        Arrays.asList(93460.0, 112186.0, 107565.0, 118662.0),
                        Arrays.asList(0.0, 0.0, 0.0, 0.0),
                        Arrays.asList(93460.0, 112186.0, 107565.0, 118662.0)
                },
                {
                        WS06,
                        Arrays.asList(56258.0, 62561.0, 61081.0, 66661.0),
                        Arrays.asList(75029.0, 89706.0, 88965.0, 95153.0),
                        Arrays.asList(131287.0, 152267.0, 150047.0, 161815.0)
                },
                {
                        WS10,
                        Arrays.asList(32309.0, 33987.0, 33159.0, 34857.0),
                        Arrays.asList(131260.0, 152441.0, 153174.0, 167352.0),
                        Arrays.asList(163570.0, 186428.0, 186333.0, 202210.0)
                },
                {
                        WS01,
                        Arrays.asList(93027.0, 100021.0, 103573.0, 110363.0),
                        Arrays.asList(279605.0, 302600.0, 310054.0, 331899.0),
                        Arrays.asList(372633.0, 402621.0, 413628.0, 442262.0)
                },
                {
                        WS03_1,
                        Arrays.asList(99752.0, 118922.0, 113127.0, 117521.0),
                        Arrays.asList(247251.0, 298263.0, 284612.0, 281718.0),
                        Arrays.asList(347004.0, 417185.0, 397739.0, 399239.0)
                },
                {
                        WS02,
                        Arrays.asList(99774.0, 107010.0, 111024.0, 119880.0),
                        Arrays.asList(315755.0, 340377.0, 350112.0, 364664.0),
                        Arrays.asList(415529.0, 447388.0, 461136.0, 484544.0)
                },
                {
                        WS09,
                        Arrays.asList(87711.0, 105397.0, 100704.0, 111066.0),
                        Arrays.asList(3147.0, 4128.0, 4073.0, 4686.0),
                        Arrays.asList(90858.0, 109526.0, 104777.0, 115752.0)
                },
                {
                        WS04,
                        Arrays.asList(135486.0, 158240.0, 152806.0, 161063.0),
                        Arrays.asList(279405.0, 329085.0, 318774.0, 308471.0),
                        Arrays.asList(414892.0, 487325.0, 471580.0, 469535.0)
                }
        };
    }

    @DataProvider(name = "summaryModularProdCostInclDiscountsAndSurchargesNteLeap1b")
    public Object[][] workscopeSummaryModularProdCostInclDiscountsAndSurchargesDataProviderNteLeap1b() {
        return new Object[][] {
                {
                        WS03,
                        Arrays.asList(2392455.0, 2443373.0, 2552839.0, 2773339.0),
                        Arrays.asList(5545301.0, 5476682.0, 5603922.0, 6082739.0),
                        Arrays.asList(7937757.0, 7920055.0, 8156762.0, 8856079.0)
                },
                {
                        WS05,
                        Arrays.asList(651290.0, 643158.0, 665052.0, 747300.0),
                        Arrays.asList(1928635.0, 1909300.0, 1968976.0, 1864910.0),
                        Arrays.asList(2579926.0, 2552459.0, 2634029.0, 2612211.0)
                },
                {
                        WS07,
                        Arrays.asList(592597.0, 589194.0, 607475.0, 664702.0),
                        Arrays.asList(634233.0, 634379.0, 654655.0, 666383.0),
                        Arrays.asList(1226831.0, 1223573.0, 1262130.0, 1331085.0)
                },
                {
                        WS08,
                        Arrays.asList(287265.0, 296400.0, 296114.0, 314472.0),
                        Arrays.asList(0.0, 0.0, 0.0, 0.0),
                        Arrays.asList(287265.0, 296400.0, 296114.0, 314472.0)
                },
                {
                        WS06,
                        Arrays.asList(352270.0, 337948.0, 341946.0, 367862.0),
                        Arrays.asList(565295.0, 601254.0, 624988.0, 663022.0),
                        Arrays.asList(917565.0, 939202.0, 966934.0, 1030884.0)
                },
                {
                        WS10,
                        Arrays.asList(265802.0, 254286.0, 258240.0, 268207.0),
                        Arrays.asList(978816.0, 990378.0, 1015254.0, 1153761.0),
                        Arrays.asList(1244619.0, 1244665.0, 1273494.0, 1421969.0)
                },
                {
                        WS01,
                        Arrays.asList(1187773.0, 1188255.0, 1242976.0, 1342930.0),
                        Arrays.asList(3553112.0, 3488598.0, 3556010.0, 4013090.0),
                        Arrays.asList(4740885.0, 4676853.0, 4798987.0, 5356020.0)
                },
                {
                        WS03_1,
                        Arrays.asList(2256542.0, 2299856.0, 2400439.0, 2606512.0),
                        Arrays.asList(5379482.0, 5347413.0, 5498018.0, 5961619.0),
                        Arrays.asList(7636024.0, 7647269.0, 7898457.0, 8568132.0)
                },
                {
                        WS02,
                        Arrays.asList(1280454.0, 1282590.0, 1341257.0, 1466592.0),
                        Arrays.asList(4046795.0, 3973285.0, 4056425.0, 4458901.0),
                        Arrays.asList(5327250.0, 5255875.0, 5397683.0, 5925493.0)
                },
                {
                        WS09,
                        Arrays.asList(236398.0, 245543.0, 243930.0, 257986.0),
                        Arrays.asList(7874.0, 8746.0, 8830.0, 10113.0),
                        Arrays.asList(244273.0, 254289.0, 252760.0, 268099.0)
                },
                {
                        WS04,
                        Arrays.asList(3186730.0, 3204532.0, 3334270.0, 3633226.0),
                        Arrays.asList(6435792.0, 6353358.0, 6517833.0, 6814205.0),
                        Arrays.asList(9622523.0, 9557890.0, 9852104.0, 1.0447432E7)
                }
        };
    }

    @DataProvider(name = "summaryModularDb2ValuesNteLeap1b")
    public Object[][] workscopeSummaryModularDb2ValuesNteLeap1b() {
        return new Object[][] {
                {
                        WS03,
                        Arrays.asList(-1366807.0, -1357080.0, -1408187.0, -1566526.0),
                        Arrays.asList(616342.0, 1003857.0, 1225881.0, 877134.0),
                        Arrays.asList(-750464.0, -353223.0, -182306.0, -689392.0)
                },
                {
                        WS05,
                        Arrays.asList(-416012.0, -402379.0, -418470.0, -494541.0),
                        Arrays.asList(124669.0, 253240.0, 327879.0, 171357.0),
                        Arrays.asList(-291342.0, -149139.0, -90591.0, -323184.0)
                },
                {
                        WS07,
                        Arrays.asList(-357318.0, -348414.0, -360893.0, -411943.0),
                        Arrays.asList(22016.0, 57909.0, 80734.0, 34593.0),
                        Arrays.asList(-335302.0, -290505.0, -280159.0, -377349.0)
                },
                {
                        WS08,
                        Arrays.asList(-115039.0, -121819.0, -119033.0, -134722.0),
                        Arrays.asList(0.0, 0.0, 0.0, 0.0),
                        Arrays.asList(-115039.0, -121819.0, -119033.0, -134722.0)
                },
                {
                        WS06,
                        Arrays.asList(-168313.0, -150537.0, -150836.0, -172768.0),
                        Arrays.asList(-26751.0, -19927.0, -7625.0, -33571.0),
                        Arrays.asList(-195064.0, -170464.0, -158462.0, -206340.0)
                },
                {
                        WS10,
                        Arrays.asList(-45673.0, -30610.0, -30860.0, -37916.0),
                        Arrays.asList(11463.0, 62119.0, 102533.0, 33365.0),
                        Arrays.asList(-34209.0, 31509.0, 71672.0, -4550.0)
                },
                {
                        WS01,
                        Arrays.asList(-820109.0, -807358.0, -848006.0, -932887.0),
                        Arrays.asList(261530.0, 508579.0, 627569.0, 414087.0),
                        Arrays.asList(-558579.0, -298779.0, -220436.0, -518800.0)
                },
                {
                        WS03_1,
                        Arrays.asList(-1231424.0, -1214385.0, -1256925.0, -1401182.0),
                        Arrays.asList(586896.0, 966638.0, 1189557.0, 847178.0),
                        Arrays.asList(-644528.0, -247746.0, -67367.0, -554003.0)
                },
                {
                        WS02,
                        Arrays.asList(-893622.0, -880869.0, -924155.0, -1031663.0),
                        Arrays.asList(310146.0, 594646.0, 733357.0, 474801.0),
                        Arrays.asList(-583476.0, -286223.0, -190797.0, -556861.0)
                },
                {
                        WS09,
                        Arrays.asList(-69647.0, -76949.0, -73397.0, -85395.0),
                        Arrays.asList(-2157.0, -2730.0, -2441.0, -3328.0),
                        Arrays.asList(-71805.0, -79679.0, -75838.0, -88724.0)
                },
                {
                        WS04,
                        Arrays.asList(-2033591.0, -1982657.0, -2045256.0, -2272501.0),
                        Arrays.asList(795038.0, 1247569.0, 1519724.0, 1066359.0),
                        Arrays.asList(-1238553.0, -735088.0, -525531.0, -1206141.0)
                }
        };
    }

    @DataProvider(name = "summaryModularDb2PercentageValuesNteLeap1b")
    public Object[][] workscopeSummaryModularDb2PercentageValuesNteLeap1b() {
        return new Object[][] {
                {
                        WS03,
                        Arrays.asList(-133.2628, -124.92775, -123.02318, -129.8069),
                        Arrays.asList(10.002894, 15.490333, 17.949, 12.602732),
                        Arrays.asList(-10.44155, -4.668051, -2.2861252, -8.441514)
                },
                {
                        WS05,
                        Arrays.asList(-176.81657, -167.11555, -169.7087, -195.65733),
                        Arrays.asList(6.0716414, 11.710314, 14.275131, 8.4152565),
                        Arrays.asList(-12.730262, -6.2055516, -3.5617774, -14.118862)
                },
                {
                        WS07,
                        Arrays.asList(-151.87032, -144.703, -146.35855, -162.97855),
                        Arrays.asList(3.354821, 8.36491, 10.978438, 4.9350333),
                        Arrays.asList(-37.609867, -31.134432, -28.530281, -39.56542)
                },
                {
                        WS08,
                        Arrays.asList(-66.79553, -69.77802, -67.22012, -74.94941),
                        Arrays.asList(0.0, 0.0, 0.0, 0.0),
                        Arrays.asList(-66.79553, -69.77802, -67.22012, -74.94941)
                },
                {
                        WS06,
                        Arrays.asList(-91.49633, -80.32537, -78.92639, -88.55691),
                        Arrays.asList(-4.967293, -3.427848, -1.2352313, -5.3334403),
                        Arrays.asList(-26.998549, -22.174652, -19.600197, -25.02475)
                },
                {
                        WS10,
                        Arrays.asList(-20.748331, -13.684971, -13.572419, -16.46461),
                        Arrays.asList(1.1575819, 5.9021106, 9.172867, 2.8106244),
                        Arrays.asList(-2.826307, 2.4690602, 5.3281336, -0.32106414)
                },
                {
                        WS01,
                        Arrays.asList(-223.05988, -211.96265, -214.70117, -227.50993),
                        Arrays.asList(6.8559737, 12.723463, 15.000785, 9.3533125),
                        Arrays.asList(-13.355764, -6.824443, -4.814543, -10.725168)
                },
                {
                        WS03_1,
                        Arrays.asList(-120.12524, -111.87635, -109.91776, -116.24885),
                        Arrays.asList(9.836733, 15.309321, 17.787577, 12.442414),
                        Arrays.asList(-9.218743, -3.3481445, -0.8602528, -6.912834)
                },
                {
                        WS02,
                        Arrays.asList(-231.01086, -219.27426, -221.56537, -237.20279),
                        Arrays.asList(7.1184506, 13.017849, 15.31087, 9.623637),
                        Arrays.asList(-12.299832, -5.759424, -3.6643355, -10.372506)
                },
                {
                        WS09,
                        Arrays.asList(-41.767242, -45.641533, -43.039894, -49.478874),
                        Arrays.asList(-37.736557, -45.391697, -38.21469, -49.057987),
                        Arrays.asList(-41.63362, -45.632923, -42.865658, -49.462955)
                },
                {
                        WS04,
                        Arrays.asList(-176.35269, -162.26344, -158.66832, -167.00658),
                        Arrays.asList(10.995121, 16.41338, 18.907791, 13.531509),
                        Arrays.asList(-14.772869, -8.331684, -5.634779, -13.051658)
                }
        };
    }

    @DataProvider(name = "summaryModularEbitValuesNteLeap1b")
    public Object[][] workscopeSummaryModularEbitValuesNteLeap1b() {
        return new Object[][] {
                {
                        WS03,
                        Arrays.asList(-1511843.0, -1501899.0, -1575962.0, -1706554.0),
                        Arrays.asList(268606.0, 655902.0, 824230.0, 557241.0),
                        Arrays.asList(-1243237.0, -845996.0, -751732.0, -1149313.0)
                },
                {
                        WS05,
                        Arrays.asList(-440198.0, -426142.0, -445705.0, -520261.0),
                        Arrays.asList(50301.0, 178448.0, 241229.0, 105092.0),
                        Arrays.asList(-389897.0, -247693.0, -204476.0, -415168.0)
                },
                {
                        WS07,
                        Arrays.asList(-403931.0, -394320.0, -413601.0, -456972.0),
                        Arrays.asList(-29926.0, 5260.0, 19557.0, -12361.0),
                        Arrays.asList(-433857.0, -389060.0, -394044.0, -469333.0)
                },
                {
                        WS08,
                        Arrays.asList(-213593.0, -220373.0, -232918.0, -226706.0),
                        Arrays.asList(0.0, 0.0, 0.0, 0.0),
                        Arrays.asList(-213593.0, -220373.0, -232918.0, -226706.0)
                },
                {
                        WS06,
                        Arrays.asList(-210545.0, -191030.0, -197196.0, -210662.0),
                        Arrays.asList(-83073.0, -77988.0, -75150.0, -87661.0),
                        Arrays.asList(-293619.0, -269019.0, -272347.0, -298324.0)
                },
                {
                        WS10,
                        Arrays.asList(-65140.0, -48577.0, -51127.0, -53773.0),
                        Arrays.asList(-67624.0, -18467.0, 8914.0, -42761.0),
                        Arrays.asList(-132764.0, -67044.0, -42212.0, -96535.0)
                },
                {
                        WS01,
                        Arrays.asList(-844714.0, -831841.0, -876523.0, -955841.0),
                        Arrays.asList(187580.0, 434508.0, 542201.0, 345057.0),
                        Arrays.asList(-657133.0, -397333.0, -334321.0, -610784.0)
                },
                {
                        WS03_1,
                        Arrays.asList(-1373081.0, -1354854.0, -1418884.0, -1536565.0),
                        Arrays.asList(235780.0, 614334.0, 782091.0, 522641.0),
                        Arrays.asList(-1137300.0, -740519.0, -636793.0, -1013924.0)
                },
                {
                        WS02,
                        Arrays.asList(-917287.0, -904443.0, -951574.0, -1054420.0),
                        Arrays.asList(235256.0, 519665.0, 646891.0, 405575.0),
                        Arrays.asList(-682030.0, -384777.0, -304682.0, -648845.0)
                },
                {
                        WS09,
                        Arrays.asList(-164788.0, -171788.0, -182855.0, -173656.0),
                        Arrays.asList(-5571.0, -6445.0, -6868.0, -7052.0),
                        Arrays.asList(-170359.0, -178234.0, -189723.0, -180708.0)
                },
                {
                        WS04,
                        Arrays.asList(-2194510.0, -2142665.0, -2229768.0, -2430266.0),
                        Arrays.asList(463185.0, 914805.0, 1134810.0, 764204.0),
                        Arrays.asList(-1731325.0, -1227860.0, -1094957.0, -1666062.0)
                },
        };
    }

    @DataProvider(name = "summaryModularEbitPercentageValuesNteLeap1b")
    public Object[][] workscopeSummaryModularEbitPercentageValuesNteLeap1b() {
        return new Object[][] {
                {
                        WS03,
                        Arrays.asList(-147.40375, -138.25919, -137.6805, -141.41),
                        Arrays.asList(4.359332, 10.121116, 12.068146, 8.006482),
                        Arrays.asList(-17.297714, -11.18032, -9.426749, -14.073185)
                },
                {
                        WS05,
                        Arrays.asList(-187.09656, -176.98456, -180.75374, -205.83284),
                        Arrays.asList(2.449779, 8.251792, 10.502577, 5.1610365),
                        Arrays.asList(-17.036615, -10.306316, -8.039387, -18.137346)
                },
                {
                        WS07,
                        Arrays.asList(-171.68188, -163.76831, -167.73401, -180.79369),
                        Arrays.asList(-4.560169, 0.7598206, 2.6594195, -1.7634457),
                        Arrays.asList(-48.66442, -41.696842, -40.12789, -49.210037)
                },
                {
                        WS08,
                        Arrays.asList(-124.0195, -126.22996, -131.5328, -126.12264),
                        Arrays.asList(0.0, 0.0, 0.0, 0.0),
                        Arrays.asList(-124.0195, -126.22996, -131.5328, -126.12264)
                },
                {
                        WS06,
                        Arrays.asList(-114.45387, -101.93174, -103.1849, -107.98044),
                        Arrays.asList(-15.425589, -13.415668, -12.172846, -13.926672),
                        Arrays.asList(-40.63929, -34.994953, -33.686657, -36.18051)
                },
                {
                        WS10,
                        Arrays.asList(-29.591766, -21.717684, -22.485651, -23.35008),
                        Arrays.asList(-6.8287954, -1.7546456, 0.79753953, -3.6021323),
                        Arrays.asList(-10.968552, -5.25359, -3.138118, -6.810624)
                },
                {
                        WS01,
                        Arrays.asList(-229.7519, -218.39046, -221.92123, -233.10788),
                        Arrays.asList(4.9173813, 10.870376, 12.960234, 7.7940755),
                        Arrays.asList(-15.712227, -9.075536, -7.301906, -12.62676)
                },
                {
                        WS03_1,
                        Arrays.asList(-133.9438, -124.81718, -124.08105, -127.4809),
                        Arrays.asList(3.9518225, 9.729647, 11.694685, 7.6759696),
                        Arrays.asList(-16.266912, -10.007662, -8.131602, -12.651711)
                },
                {
                        WS02,
                        Arrays.asList(-237.12836, -225.14233, -228.13911, -242.43527),
                        Arrays.asList(5.3995805, 11.376378, 13.505654, 8.220501),
                        Arrays.asList(-14.377386, -7.742551, -5.851539, -12.08587)
                },
                {
                        WS09,
                        Arrays.asList(-98.82272, -101.89458, -107.22545, -100.61746),
                        Arrays.asList(-97.442924, -107.14676, -107.51646, -103.94301),
                        Arrays.asList(-98.77698, -102.07553, -107.23596, -100.74324)
                },
                {
                        WS04,
                        Arrays.asList(-190.30757, -175.35878, -172.98248, -178.60081),
                        Arrays.asList(6.405699, 12.035444, 14.118843, 9.697327),
                        Arrays.asList(-20.650423, -13.916897, -11.740195, -18.028463)
                },
        };
    }

    @DataProvider(name = "summaryModularEatPercentageValuesNteLeap1b")
    public Object[][] workscopeSummaryModularEatPercentageValuesNteLeap1b() {
        return new Object[][] {
                {
                        WS03,
                        Arrays.asList(-110.55281, -103.69439, -103.26037, -106.05751),
                        Arrays.asList(3.2694993, 7.590837, 9.051109, 6.004862),
                        Arrays.asList(-12.973286, -8.38524, -7.070062, -10.554889)
                },
                {
                        WS05,
                        Arrays.asList(-140.32242, -132.73842, -135.5653, -154.37463),
                        Arrays.asList(1.8373343, 6.188844, 7.8769326, 3.8707774),
                        Arrays.asList(-12.777461, -7.729737, -6.02954, -13.60301)
                },
                {
                        WS07,
                        Arrays.asList(-128.76141, -122.82623, -125.80051, -135.59528),
                        Arrays.asList(-3.420127, 0.5698654, 1.9945647, -1.3225843),
                        Arrays.asList(-36.498318, -31.27263, -30.095919, -36.907528)
                },
                {
                        WS08,
                        Arrays.asList(-93.014626, -94.67247, -98.6496, -94.59198),
                        Arrays.asList(0.0, 0.0, 0.0, 0.0),
                        Arrays.asList(-93.014626, -94.67247, -98.6496, -94.59198)
                },
                {
                        WS06,
                        Arrays.asList(-85.8404, -76.44881, -77.38867, -80.98533),
                        Arrays.asList(-11.569192, -10.061751, -9.129635, -10.445004),
                        Arrays.asList(-30.479467, -26.246216, -25.264994, -27.135384)
                },
                {
                        WS10,
                        Arrays.asList(-22.193825, -16.288261, -16.864239, -17.512562),
                        Arrays.asList(-5.121597, -1.3159842, 0.5981546, -2.7015991),
                        Arrays.asList(-8.226414, -3.9401925, -2.3535886, -5.107968)
                },
                {
                        WS01,
                        Arrays.asList(-172.31393, -163.79285, -166.44093, -174.83092),
                        Arrays.asList(3.688036, 8.152782, 9.720176, 5.8455567),
                        Arrays.asList(-11.78417, -6.806652, -5.4764295, -9.47007)
                },
                {
                        WS03_1,
                        Arrays.asList(-100.45785, -93.612885, -93.06079, -95.61067),
                        Arrays.asList(2.963867, 7.297235, 8.771014, 5.756977),
                        Arrays.asList(-12.200185, -7.5057464, -6.098702, -9.488783)
                },
                {
                        WS02,
                        Arrays.asList(-177.84625, -168.85675, -171.10432, -181.82645),
                        Arrays.asList(4.0496855, 8.532284, 10.129241, 6.1653757),
                        Arrays.asList(-10.78304, -5.8069134, -4.388654, -9.064403)
                },
                {
                        WS09,
                        Arrays.asList(-74.11704, -76.42094, -80.41909, -75.4631),
                        Arrays.asList(-73.08219, -80.36007, -80.637344, -77.95726),
                        Arrays.asList(-74.08273, -76.55665, -80.42697, -75.55743)
                },
                {
                        WS04,
                        Arrays.asList(-142.73068, -131.51909, -129.73686, -133.9506),
                        Arrays.asList(4.804274, 9.026583, 10.589132, 7.2729955),
                        Arrays.asList(-15.487818, -10.437674, -8.805146, -13.521347)
                },
        };
    }

    @DataProvider(name = "summaryModularNetMarginPercentageNteLeap1b")
    public Object[][] workscopeSummaryModularNetMarginPercentageNteLeap1b() {
        return new Object[][] {
                {
                        WS03,
                        Arrays.asList(-112.95281, -106.09439, -105.66037, -108.45751),
                        Arrays.asList(0.8694992, 5.190837, 6.6511097, 3.604862),
                        Arrays.asList(-15.373286, -10.78524, -9.470062, -12.954889)
                },
                {
                        WS05,
                        Arrays.asList(-142.72243, -135.13841, -137.96532, -156.77463),
                        Arrays.asList(-0.5626657, 3.7888443, 5.476933, 1.4707773),
                        Arrays.asList(-15.177461, -10.129737, -8.429541, -16.00301)
                },
                {
                        WS07,
                        Arrays.asList(-131.1614, -125.22623, -128.20052, -137.99527),
                        Arrays.asList(-5.820127, -1.8301346, -0.4054353, -3.7225842),
                        Arrays.asList(-38.898315, -33.67263, -32.49592, -39.30753)
                },
                {
                        WS08,
                        Arrays.asList(-95.41463, -97.07247, -101.0496, -96.99198),
                        Arrays.asList(-2.4, -2.4, -2.4, -2.4),
                        Arrays.asList(-95.41463, -97.07247, -101.0496, -96.99198)
                },
                {
                        WS06,
                        Arrays.asList(-88.2404, -78.84881, -79.78867, -83.38533),
                        Arrays.asList(-13.969192, -12.461751, -11.529635, -12.845004),
                        Arrays.asList(-32.879467, -28.646215, -27.664993, -29.535383)
                },
                {
                        WS10,
                        Arrays.asList(-24.593824, -18.688263, -19.264238, -19.912561),
                        Arrays.asList(-7.5215964, -3.715984, -1.8018454, -5.101599),
                        Arrays.asList(-10.626414, -6.3401923, -4.7535887, -7.507968)
                },
                {
                        WS01,
                        Arrays.asList(-174.71393, -166.19284, -168.84093, -177.23091),
                        Arrays.asList(1.2880359, 5.752782, 7.3201756, 3.4455564),
                        Arrays.asList(-14.18417, -9.206652, -7.8764296, -11.87007)
                },
                {
                        WS03_1,
                        Arrays.asList(-102.85785, -96.012886, -95.460785, -98.01067),
                        Arrays.asList(0.5638669, 4.897235, 6.3710136, 3.356977),
                        Arrays.asList(-14.600185, -9.905746, -8.498702, -11.888783)
                },
                {
                        WS02,
                        Arrays.asList(-180.24626, -171.25674, -173.50433, -184.22646),
                        Arrays.asList(1.6496855, 6.1322837, 7.729241, 3.7653759),
                        Arrays.asList(-13.18304, -8.206913, -6.7886543, -11.464403)
                },
                {
                        WS09,
                        Arrays.asList(-76.517044, -78.82094, -82.81909, -77.8631),
                        Arrays.asList(-75.48219, -82.76007, -83.037346, -80.35726),
                        Arrays.asList(-76.482735, -78.95665, -82.82697, -77.95743)
                },
                {
                        WS04,
                        Arrays.asList(-145.13068, -133.91908, -132.13686, -136.35062),
                        Arrays.asList(2.4042742, 6.626582, 8.189132, 4.8729954),
                        Arrays.asList(-17.887817, -12.837674, -11.205146, -15.921347)
                },
        };
    }
}
