package com.lht.corecalculation.api.wssummarymodularntefixprice;

import com.lht.corecalculation.base.BaseCocaApiTest;
import java.util.Arrays;
import org.testng.annotations.DataProvider;


import static com.lht.corecalculation.api.constants.GlobalConstants.WS01;

public class DataProvidersWorkscopeSummaryModularNteCfm565bPma extends BaseCocaApiTest {

    @DataProvider(name = "summaryModularProductionCostNteCfm565B_PMA")
    public Object[][] workscopeSummaryModularProductionCostDataProviderNteCfm565B_PMA() {
        return new Object[][] {
                {
                        WS01,
                        Arrays.asList(3027977.0, 3137726.0, 3271512.0, 3450529.0), // included production costs
                        Arrays.asList(1629806.0, 1728102.0, 1836852.0, 1949817.0), // excluded production costs
                        Arrays.asList(4657783.0, 4865828.0, 5108365.0, 5400347.0)  // total production costs
                }
        };
    }

    @DataProvider(name = "summaryModularDiscountsNteCfm565B_PMA")
    public Object[][] workscopeSummaryModularDiscountsDataProviderNteCfm565B_PMA() {
        return new Object[][] {
                {
                        WS01,
                        Arrays.asList(297283.0, 329729.0, 361872.0, 386349.0), // included
                        Arrays.asList(175741.0, 203223.0, 231079.0, 246497.0), // excluded
                        Arrays.asList(473025.0, 532952.0, 592951.0, 632846.0)  // total
                }
        };
    }

    @DataProvider(name = "summaryModularRevenueNteCfm565B_PMA")
    public Object[][] workscopeSummaryModularRevenueDataProviderNteCfm565B_PMA() {
        return new Object[][] {
                {
                        WS01,
                        Arrays.asList(604471.0, 630735.0, 658704.0, 688494.0), // included
                        Arrays.asList(2043914.0, 2173730.0, 2313036.0, 2458628.0), // excluded
                        Arrays.asList(2648386.0, 2804465.0, 2971741.0, 3147123.0)  // total
                }
        };
    }

    @DataProvider(name = "summaryModularSurchargesNtePriceCfm565B_PMA")
    public Object[][] workscopeSummarySurchargesCostDataProviderNteCfm565B_PMA() {
        return new Object[][] {
                {
                        WS01,
                        Arrays.asList(215365.0, 205744.0, 210423.0, 217202.0), // included
                        Arrays.asList(115920.0, 113313.0, 118146.0, 122736.0), // excluded
                        Arrays.asList(331286.0, 319058.0, 328569.0, 339938.0)  // total
                }
        };
    }

    @DataProvider(name = "summaryModularProdCostInclDiscountsAndSurchargesFixedPriceCfm565B_PMA")
    public Object[][] workscopeSummaryProdCostInclDiscountsAndSurchargesDataProviderFixedPriceCfm565B_PMA() {
        return new Object[][] {
                {
                        WS01,
                        Arrays.asList(2946059.0, 3013741.0, 3120063.0, 3281381.0),  // included
                        Arrays.asList(1569984.0, 1638192.0, 1723919.0, 1826056.0),  // excluded
                        Arrays.asList(4516044.0, 4651934.0, 4843982.0, 5107438.0)   // total
                }
        };
    }

    @DataProvider(name = "summaryModularDb2ValuesFixedPriceCfm565B_PMA")
    public Object[][] workscopeSummaryDb2ValuesDataProviderFixedPriceCfm565B_PMA() {
        return new Object[][] {
                {
                        WS01,
                        Arrays.asList(-2341588.0, -2383006.0, -2461358.0, -2592887.0),  // included
                        Arrays.asList(473929.0, 535537.0, 589117.0, 632572.0),      // excluded
                        Arrays.asList(-1867658.0, -1847468.0, -1872240.0, -1960314.0)   // total
                }
        };
    }

    @DataProvider(name = "summaryModularDb2PercentageValuesFixedPriceCfm565B_PMA")
    public Object[][] workscopeDb2PercentageValuesDataProviderFixedPriceCfm565B_PMA() {
        return new Object[][] {
                {
                        WS01,
                        Arrays.asList(-387.37775, -377.81412, -373.66647, -376.60226),    // included
                        Arrays.asList(23.18736, 24.63681, 25.469456, 25.728659),   // excluded
                        Arrays.asList(-70.520615, -65.87595, -63.001465, -62.2891)     // total
                }
        };
    }

    @DataProvider(name = "summaryModularEbitValuesFixedPriceCfm565B_PMA")
    public Object[][] workscopeSummaryEbitValuesDataProviderFixedPriceCfm565B_PMA() {
        return new Object[][] {
                {
                        WS01,
                        Arrays.asList(-2711766.0, -2679586.0, -2734863.0, -2855266.0), // included
                        Arrays.asList(274681.0, 372196.0, 435553.0, 484307.0),    // excluded
                        Arrays.asList(-2437084.0, -2307389.0, -2299310.0, -2370958.0) // total
                }
        };
    }

    @DataProvider(name = "summaryModularEbitPercentageValuesFixedPriceCfm565B_PMA")
    public Object[][] workscopeEbitPercentageValuesDataProviderFixedPriceCfm565B_PMA() {
        return new Object[][] {
                {
                        WS01,
                        Arrays.asList(-448.6177, -424.8354, -415.18808, -414.71133), // included
                        Arrays.asList(13.43901, 17.122482, 18.83037, 19.69828),  // excluded
                        Arrays.asList(-92.02148, -82.27554, -77.37248, -75.337326)  // total
                }
        };
    }

    @DataProvider(name = "summaryModularEatPercentageValuesFixedPriceCfm565B_PMA")
    public Object[][] workscopeEatPercentageValuesDataProviderFixedPriceCfm565B_PMA() {
        return new Object[][] {
                {
                        WS01,
                        Arrays.asList(-336.4633, -318.62656, -311.39108, -311.0335), // included
                        Arrays.asList(10.079258, 12.841862, 14.122777, 14.773711),    // excluded
                        Arrays.asList(-69.01611, -61.70666, -58.02936, -56.502995)   // total
                }
        };
    }

    @DataProvider(name = "summaryModularNetMarginPercentageFixedPriceCfm565B_PMA")
    public Object[][] workscopeNetMarginPercentageDataProviderFixedPriceCfm565B_PMA() {
        return new Object[][] {
                {
                        WS01,
                        Arrays.asList(-338.86328, -321.02655, -313.79108, -313.4335), // included
                        Arrays.asList(7.679258, 10.441862, 11.722777, 12.373711),   // excluded
                        Arrays.asList(-71.41611, -64.10666, -60.429363, -58.902992)   // total
                }
        };
    }

}