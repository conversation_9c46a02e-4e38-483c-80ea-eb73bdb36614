package com.lht.corecalculation.api.wssummarymodularntefixprice;

import com.lht.corecalculation.base.BaseCocaApiTest;
import java.util.Arrays;
import org.testng.annotations.DataProvider;


import static com.lht.corecalculation.api.constants.GlobalConstants.CPR;
import static com.lht.corecalculation.api.constants.GlobalConstants.C_N_A;
import static com.lht.corecalculation.api.constants.GlobalConstants.C_N_A_K;
import static com.lht.corecalculation.api.constants.GlobalConstants.C_N_B;
import static com.lht.corecalculation.api.constants.GlobalConstants.C_N_B_K;
import static com.lht.corecalculation.api.constants.GlobalConstants.HPC;
import static com.lht.corecalculation.api.constants.GlobalConstants.HPT_LLP;
import static com.lht.corecalculation.api.constants.GlobalConstants.HPT_S1B;
import static com.lht.corecalculation.api.constants.GlobalConstants.NSV;

public class DataProvidersWorkscopeSummaryModularFixedPriceLeap1A extends BaseCocaApiTest {

    @DataProvider(name = "summaryModularProductionCostFixedPriceLeap1a")
    public Object[][] workscopeModularSummaryProductionCostDataProviderFixedPriceLeap1a() {
        return new Object[][] {
                {CPR, Arrays.asList(2238686.0, 2336700.0, 2473762.0, 2639502.0, 2813025.0, 3008432.0)},
                {HPC, Arrays.asList(241751.0, 225872.0, 230754.0, 232770.0, 232961.0, 242138.0)},
                {HPT_S1B, Arrays.asList(247440.0, 238343.0, 246196.0, 252045.0, 256734.0, 268856.0)},
                {HPT_LLP, Arrays.asList(261260.0, 251145.0, 259233.0, 265139.0, 269769.0, 282373.0)},
                {NSV, Arrays.asList(231713.0, 216573.0, 221283.0, 223258.0, 223493.0, 232320.0)}
        };
    }

    @DataProvider(name = "summaryModularDiscountsFixedPriceLeap1a")
    public Object[][] workscopeSummaryModularDiscountsDataProviderFixedPriceLeap1a() {
        return new Object[][] {
                {CPR, Arrays.asList(343655.0, 444591.0, 492550.0, 465907.0, 527045.0, 589306.0)},
                {HPC, Arrays.asList(2034.0, 2464.0, 2874.0, 2756.0, 3051.0, 3539.0)},
                {HPT_S1B, Arrays.asList(9420.0, 13657.0, 16311.0, 13291.0, 15649.0, 18436.0)},
                {HPT_LLP, Arrays.asList(9420.0, 13657.0, 16311.0, 13291.0, 15649.0, 18436.0)},
                {NSV, Arrays.asList(2034.0, 2464.0, 2874.0, 2756.0, 3051.0, 3539.0)}
        };
    }

    @DataProvider(name = "summaryModularRevenueFixedPriceLeap1a")
    public Object[][] workscopeModularSummaryRevenueDataProviderFixedPriceLeap1a() {
        return new Object[][] {
                {CPR, Arrays.asList(650813.0, 678531.0, 709016.0, 734733.0, 751979.0, 766725.0)},
                {HPC, Arrays.asList(238460.0, 243749.0, 249535.0, 255840.0, 262709.0, 270195.0)},
                {HPT_S1B, Arrays.asList(216252.0, 219575.0, 223218.0, 227187.0, 231512.0, 236224.0)},
                {HPT_LLP, Arrays.asList(216252.0, 219575.0, 223218.0, 227187.0, 231512.0, 236224.0)},
                {NSV, Arrays.asList(249144.0, 255500.0, 262458.0, 270049.0, 278331.0, 287367.0)}
        };
    }

    @DataProvider(name = "summaryModularSurchargesCostFixedPriceLeap1a")
    public Object[][] workscopeModularSummarySurchargesCostDataProviderFixedPriceLeap1a() {
        return new Object[][] {
                {CPR, Arrays.asList(93688.0, 112016.0, 106492.0, 109429.0, 116128.0, 124858.0)},
                {HPC, Arrays.asList(94568.0, 112755.0, 107998.0, 117148.0, 119165.0, 122775.0)},
                {HPT_S1B, Arrays.asList(32883.0, 34611.0, 33757.0, 35168.0, 35152.0, 36212.0)},
                {HPT_LLP, Arrays.asList(34480.0, 36235.0, 35331.0, 36782.0, 36739.0, 37835.0)},
                {NSV, Arrays.asList(94066.0, 112290.0, 107524.0, 116672.0, 118692.0, 122284.0)}
        };
    }

    @DataProvider(name = "summaryModularProdCostInclDiscountsAndSurchargesFixedPriceLeap1a")
    public Object[][] workscopeModularSummaryProdCostInclDiscountsAndSurchargesDataProviderFixedPriceLeap1a() {
        return new Object[][] {
                {CPR, Arrays.asList(1988719.0, 2004125.0, 2087704.0, 2283024.0, 2402108.0, 2543984.0)},
                {HPC, Arrays.asList(334285.0, 336164.0, 335878.0, 347161.0, 349074.0, 361373.0)},
                {HPT_S1B, Arrays.asList(270903.0, 259297.0, 263643.0, 273923.0, 276238.0, 286631.0)},
                {HPT_LLP, Arrays.asList(286320.0, 273722.0, 278253.0, 288630.0, 290860.0, 301773.0)},
                {NSV, Arrays.asList(323745.0, 326400.0, 325934.0, 337174.0, 339133.0, 351065.0)}
        };
    }

    @DataProvider(name = "summaryModularDb2ValuesFixedPriceLeap1a")
    public Object[][] workscopeModularSummaryDb2ValuesFixedPriceLeap1a() {
        return new Object[][] {
                {CPR, Arrays.asList(-1337906.0, -1325594.0, -1378688.0, -1548290.0, -1650129.0, -1777258.0)},
                {HPC, Arrays.asList(-95825.0, -92415.0, -86342.0, -91321.0, -86364.0, -91177.0)},
                {HPT_S1B, Arrays.asList(-54650.0, -39722.0, -40425.0, -46735.0, -44726.0, -50407.0)},
                {HPT_LLP, Arrays.asList(-70067.0, -54147.0, -55035.0, -61443.0, -59347.0, -65548.0)},
                {NSV, Arrays.asList(-74601.0, -70900.0, -63475.0, -67125.0, -60802.0, -63698.0)}
        };
    }

    @DataProvider(name = "summaryModularDb2PercentageValuesFixedPriceLeap1a")
    public Object[][] workscopeModularSummaryDb2PercentageValuesFixedPriceLeap1a() {
        return new Object[][] {
                {CPR, Arrays.asList(-205.57468, -195.3623, -194.45091, -210.72816, -219.43817, -231.79832)},
                {HPC, Arrays.asList(-40.185085, -37.91401, -34.601425, -35.69464, -32.874657, -33.745148)},
                {HPT_S1B, Arrays.asList(-25.271643, -18.090725, -18.110182, -20.571547, -19.319191, -21.338688)},
                {HPT_LLP, Arrays.asList(-32.40068, -24.660332, -24.65554, -27.045202, -25.634916, -27.748318)},
                {NSV, Arrays.asList(-29.943087, -27.74953, -24.185085, -24.856764, -21.845427, -22.166105)}
        };
    }

    @DataProvider(name = "summaryModularEbitValuesFixedPriceLeap1a")
    public Object[][] workscopeModularSummaryEbitValuesFixedPriceLeap1a() {
        return new Object[][] {
                {CPR, Arrays.asList(-1475727.0, -1461788.0, -1536140.0, -1679528.0, -1776456.0, -1904299.0)},
                {HPC, Arrays.asList(-194379.0, -190969.0, -200228.0, -183305.0, -171778.0, -173306.0)},
                {HPT_S1B, Arrays.asList(-74429.0, -57991.0, -60979.0, -62778.0, -59057.0, -63970.0)},
                {HPT_LLP, Arrays.asList(-90719.0, -73208.0, -76473.0, -78167.0, -74279.0, -79676.0)},
                {NSV, Arrays.asList(-173156.0, -169454.0, -177360.0, -159109.0, -146216.0, -145826.0)}
        };
    }

    @DataProvider(name = "summaryModularEbitPercentageValuesFixedPriceLeap1a")
    public Object[][] workscopeModularSummaryEbitPercentageValuesFixedPriceLeap1a() {
        return new Object[][] {
                {CPR, Arrays.asList(-226.75134, -215.4342, -216.6581, -228.59015, -236.23755, -248.36768)},
                {HPC, Arrays.asList(-81.51462, -78.34672, -80.24025, -71.64839, -65.38728, -64.14116)},
                {HPT_S1B, Arrays.asList(-34.417667, -26.410942, -27.318295, -27.632805, -25.509272, -27.0803)},
                {HPT_LLP, Arrays.asList(-41.95049, -33.341015, -34.25946, -34.40659, -32.08434, -33.728943)},
                {NSV, Arrays.asList(-69.50028, -66.32265, -67.5768, -58.918774, -52.533302, -50.74583)}
        };
    }

    @DataProvider(name = "summaryModularEatPercentageValuesFixedPriceLeap1a")
    public Object[][] workscopeModularSummaryEatPercentageValuesFixedPriceLeap1a() {
        return new Object[][] {
                {CPR, Arrays.asList(-170.0635, -161.57565, -162.49358, -171.44261, -177.17816, -186.27576)},
                {HPC, Arrays.asList(-61.135963, -58.760036, -60.180187, -53.736298, -49.040462, -48.10587)},
                {HPT_S1B, Arrays.asList(-25.81325, -19.808207, -20.488722, -20.724604, -19.131952, -20.310225)},
                {HPT_LLP, Arrays.asList(-31.462868, -25.00576, -25.694595, -25.804943, -24.063255, -25.296707)},
                {NSV, Arrays.asList(-52.12521, -49.74199, -50.682602, -44.18908, -39.399975, -38.059372)}
        };
    }

    @DataProvider(name = "summaryModularNetMarginePercentageFixedPriceLeap1a")
    public Object[][] workscopeModularSummaryNetMarginePercentageFixedPriceLeap1a() {
        return new Object[][] {
                {CPR, Arrays.asList(-172.46352, -163.97566, -164.89357, -173.8426, -179.57816, -188.67577)},
                {HPC, Arrays.asList(-63.53596, -61.160038, -62.58019, -56.136295, -51.44046, -50.50587)},
                {HPT_S1B, Arrays.asList(-28.21325, -22.208206, -22.888721, -23.124603, -21.531954, -22.710224)},
                {HPT_LLP, Arrays.asList(-33.86287, -27.405762, -28.094597, -28.204943, -26.463257, -27.696709)},
                {NSV, Arrays.asList(-54.525208, -52.141987, -53.0826, -46.58908, -41.799976, -40.459373)}
        };
    }
}
