package com.lht.corecalculation.api.wssummarymodularntefixprice;

import com.lht.corecalculation.base.BaseCocaApiTest;
import java.util.Arrays;
import org.testng.annotations.DataProvider;


import static com.lht.corecalculation.api.constants.GlobalConstants.C_N_A;
import static com.lht.corecalculation.api.constants.GlobalConstants.C_N_A_K;
import static com.lht.corecalculation.api.constants.GlobalConstants.C_N_B;
import static com.lht.corecalculation.api.constants.GlobalConstants.C_N_B_K;

public class DataProvidersWorkscopeSummaryModularFixedPriceV2500 extends BaseCocaApiTest {

    @DataProvider(name = "summaryModularProductionCostFixedPriceV2500")
    public Object[][] workscopeSummaryModularProductionCostDataProviderFixedPriceV2500() {
        return new Object[][] {
                {
                        C_N_A_K,
                        Arrays.asList(1656671.0, 1742354.0, 1833075.0, 1923057.0, 2014712.0, 2132638.0),
                        Arrays.asList(1289308.0, 1412995.0, 1505070.0, 1602606.0, 1706087.0, 1816231.0),
                        Arrays.asList(2945980.0, 3155349.0, 3338145.0, 3525664.0, 3720799.0, 3948869.0)
                },
                {
                        C_N_A,
                        Arrays.asList(2069009.0, 2169091.0, 2280870.0, 2391155.0, 2502971.0, 2648873.0),
                        Arrays.asList(1653207.0, 1810841.0, 1928989.0, 2054221.0, 2186630.0, 2327314.0),
                        Arrays.asList(3722216.0, 3979933.0, 4209860.0, 4445377.0, 4689601.0, 4976188.0)
                },
                {
                        C_N_B_K,
                        Arrays.asList(1637530.0, 1724364.0, 1815056.0, 1905243.0, 1997274.0, 2115146.0),
                        Arrays.asList(955249.0, 1047287.0, 1118362.0, 1193701.0, 1273719.0, 1359065.0),
                        Arrays.asList(2592780.0, 2771651.0, 2933418.0, 3098944.0, 3270994.0, 3474211.0)
                },
                {
                        C_N_B,
                        Arrays.asList(2425096.0, 2545556.0, 2678094.0, 2809266.0, 2942575.0, 3115689.0),
                        Arrays.asList(1520514.0, 1666008.0, 1778815.0, 1898621.0, 2025758.0, 2160867.0),
                        Arrays.asList(3945611.0, 4211564.0, 4456909.0, 4707887.0, 4968333.0, 5276556.0)
                }

        };
    }

    @DataProvider(name = "summaryDiscountsModularFixedPriceV2500")
    public Object[][] workscopeSummaryModularDiscountsDataProviderFixedPriceV2500() {
        return new Object[][] {
                {
                        C_N_A_K,
                        Arrays.asList(13315.0, 14780.0, 15667.0, 16607.0, 17603.0, 18659.0),
                        Arrays.asList(0.0, 0.0, 0.0, 0.0, 0.0, 0.0),
                        Arrays.asList(13315.0, 14780.0, 15667.0, 16607.0, 17603.0, 18659.0)
                },
                {
                        C_N_A,
                        Arrays.asList(15349.0, 17038.0, 18060.0, 19144.0, 20292.0, 21510.0),
                        Arrays.asList(0.0, 0.0, 0.0, 0.0, 0.0, 0.0),
                        Arrays.asList(15349.0, 17038.0, 18060.0, 19144.0, 20292.0, 21510.0)
                },
                {
                        C_N_B_K,
                        Arrays.asList(15345.0, 17033.0, 18055.0, 19138.0, 20287.0, 21504.0),
                        Arrays.asList(0.0, 0.0, 0.0, 0.0, 0.0, 0.0),
                        Arrays.asList(15345.0, 17033.0, 18055.0, 19138.0, 20287.0, 21504.0)
                },
                {
                        C_N_B,
                        Arrays.asList(16565.0, 18387.0, 19491.0, 20660.0, 21900.0, 23214.0),
                        Arrays.asList(0.0, 0.0, 0.0, 0.0, 0.0, 0.0),
                        Arrays.asList(16565.0, 18387.0, 19491.0, 20660.0, 21900.0, 23214.0)
                }
        };
    }

    @DataProvider(name = "summaryModularRevenueFixedPriceV2500")
    public Object[][] workscopeSummaryModularRevenueDataProviderFixedPriceV2500() {
        return new Object[][] {
                {
                        C_N_A_K,
                        Arrays.asList(638075.0, 677545.0, 709801.0, 744043.0, 780395.0, 819152.0),
                        Arrays.asList(1472060.0, 1612672.0, 1717903.0, 1829796.0, 1948845.0, 2075661.0),
                        Arrays.asList(2110135.0, 2290217.0, 2427704.0, 2573840.0, 2729241.0, 2894813.0)
                },
                {
                        C_N_A,
                        Arrays.asList(657040.0, 697079.0, 729921.0, 764766.0, 801741.0, 841138.0),
                        Arrays.asList(1908004.0, 2089803.0, 2226456.0, 2371837.0, 2526243.0, 2690414.0),
                        Arrays.asList(2565044.0, 2786882.0, 2956377.0, 3136603.0, 3327984.0, 3531552.0)
                },
                {
                        C_N_B_K,
                        Arrays.asList(638084.0, 677555.0, 709811.0, 744054.0, 780407.0, 819164.0),
                        Arrays.asList(1177895.0, 1290919.0, 1378261.0, 1471283.0, 1570425.0, 1676179.0),
                        Arrays.asList(1815980.0, 1968475.0, 2088073.0, 2215337.0, 2350833.0, 2495344.0)
                },
                {
                        C_N_B,
                        Arrays.asList(691688.0, 734225.0, 769152.0, 806203.0, 845510.0, 887440.0),
                        Arrays.asList(1912717.0, 2095178.0, 2236430.0, 2386987.0, 2547346.0, 2718012.0),
                        Arrays.asList(2604405.0, 2829404.0, 3005582.0, 3193191.0, 3392856.0, 3605453.0)
                }
        };
    }

    @DataProvider(name = "summaryModularSurchargesCostFixedPriceV2500")
    public Object[][] workscopeSummaryModularSurchargesCostDataProviderFixedPriceV2500() {
        return new Object[][] {
                {
                        C_N_A_K,
                        Arrays.asList(162325.0, 197281.0, 183445.0, 175566.0, 179419.0, 185161.0),
                        Arrays.asList(126330.0, 159989.0, 150620.0, 146310.0, 151935.0, 157690.0),
                        Arrays.asList(288656.0, 357270.0, 334065.0, 321877.0, 331354.0, 342851.0)
                },
                {
                        C_N_A,
                        Arrays.asList(170096.0, 204873.0, 191690.0, 184385.0, 188670.0, 194981.0),
                        Arrays.asList(135912.0, 171036.0, 162117.0, 158404.0, 164824.0, 171312.0),
                        Arrays.asList(306008.0, 375909.0, 353807.0, 342790.0, 353494.0, 366294.0)
                },
                {
                        C_N_B_K,
                        Arrays.asList(186167.0, 226349.0, 210994.0, 202405.0, 207071.0, 213743.0),
                        Arrays.asList(108600.0, 137472.0, 130006.0, 126813.0, 132055.0, 137338.0),
                        Arrays.asList(294768.0, 363822.0, 341000.0, 329218.0, 339126.0, 351082.0)
                },
                {
                        C_N_B,
                        Arrays.asList(195395.0, 235084.0, 220953.0, 213399.0, 218739.0, 226271.0),
                        Arrays.asList(122511.0, 153857.0, 146759.0, 144224.0, 150586.0, 156929.0),
                        Arrays.asList(317906.0, 388942.0, 367712.0, 357624.0, 369326.0, 383200.0)
                }
        };
    }

    @DataProvider(name = "summaryModularProdCostInclDiscountsAndSurchargesFixedPriceV2500")
    public Object[][] workscopeSummaryModularProdCostInclDiscountsAndSurchargesDataProviderFixedPriceV2500() {
        return new Object[][] {
                {
                        C_N_A_K,
                        Arrays.asList(1805681.0, 1924856.0, 2000853.0, 2082017.0, 2176528.0, 2299139.0),
                        Arrays.asList(1415639.0, 1572984.0, 1655690.0, 1748917.0, 1858022.0, 1973921.0),
                        Arrays.asList(3221320.0, 3497840.0, 3656543.0, 3830934.0, 4034550.0, 4273061.0)
                },
                {
                        C_N_A,
                        Arrays.asList(2223755.0, 2356926.0, 2454500.0, 2556397.0, 2671349.0, 2822344.0),
                        Arrays.asList(1789119.0, 1981878.0, 2091107.0, 2212625.0, 2351454.0, 2498626.0),
                        Arrays.asList(4012875.0, 4338804.0, 4545607.0, 4769023.0, 5022803.0, 5320971.0)
                },
                {
                        C_N_B_K,
                        Arrays.asList(1808353.0, 1933680.0, 2007995.0, 2088509.0, 2184059.0, 2307385.0),
                        Arrays.asList(1063850.0, 1184760.0, 1248368.0, 1320515.0, 1405775.0, 1496404.0),
                        Arrays.asList(2872203.0, 3118440.0, 3256363.0, 3409024.0, 3589834.0, 3803789.0)
                },
                {
                        C_N_B,
                        Arrays.asList(2603926.0, 2762253.0, 2879556.0, 3002005.0, 3139414.0, 3318746.0),
                        Arrays.asList(1643025.0, 1819865.0, 1925574.0, 2042846.0, 2176345.0, 2317796.0),
                        Arrays.asList(4246952.0, 4582119.0, 4805130.0, 5044851.0, 5315760.0, 5636542.0)
                }
        };
    }

    @DataProvider(name = "summaryModularDb2ValuesFixedPriceV2500")
    public Object[][] summaryModularDb2ValuesFixedPriceV2500() {
        return new Object[][] {
                {
                        C_N_A_K,
                        Arrays.asList(-1167606.0, -1247311.0, -1291052.0, -1337973.0, -1396132.0, -1479987.0),
                        Arrays.asList(56421.0, 39688.0, 62213.0, 80879.0, 90823.0, 101739.0),
                        Arrays.asList(-1111185.0, -1207622.0, -1228839.0, -1257094.0, -1305309.0, -1378247.0)
                },
                {
                        C_N_A,
                        Arrays.asList(-1566715.0, -1659847.0, -1724579.0, -1791630.0, -1869608.0, -1981206.0),
                        Arrays.asList(118884.0, 107924.0, 135349.0, 159211.0, 174788.0, 191787.0),
                        Arrays.asList(-1447831.0, -1551922.0, -1589230.0, -1632419.0, -1694819.0, -1789419.0)
                },
                {
                        C_N_B_K,
                        Arrays.asList(-1170268.0, -1256125.0, -1298183.0, -1344455.0, -1403651.0, -1488220.0),
                        Arrays.asList(114045.0, 106159.0, 129893.0, 150768.0, 164650.0, 179775.0),
                        Arrays.asList(-1056223.0, -1149965.0, -1168290.0, -1193686.0, -1239000.0, -1308444.0)
                },
                {
                        C_N_B,
                        Arrays.asList(-1912238.0, -2028028.0, -2110404.0, -2195802.0, -2293903.0, -2431305.0),
                        Arrays.asList(269692.0, 275312.0, 310856.0, 344141.0, 371000.0, 400216.0),
                        Arrays.asList(-1642546.0, -1752715.0, -1799548.0, -1851660.0, -1922903.0, -2031089.0)
                }
        };
    }

    @DataProvider(name = "summaryModularDb2PercentageValuesFixedPriceV2500")
    public Object[][] summaryModularDb2PercentageValuesFixedPriceV2500() {
        return new Object[][] {
                {
                        C_N_A_K,
                        Arrays.asList(-182.98892, -184.09264, -181.8893, -179.82475, -178.9006, -180.67297),
                        Arrays.asList(3.8328218, 2.4610236, 3.6214657, 4.420128, 4.6603546, 4.90155),
                        Arrays.asList(-52.65941, -52.729603, -50.61732, -48.841206, -47.826836, -47.61093)
                },
                {
                        C_N_A,
                        Arrays.asList(-238.45058, -238.11455, -236.26929, -234.27151, -233.19351, -235.53877),
                        Arrays.asList(6.2308283, 5.164356, 6.0791345, 6.7125664, 6.918915, 7.128541),
                        Arrays.asList(-56.444695, -55.686676, -53.755997, -52.044174, -50.9263, -50.669483)
                },
                {
                        C_N_B_K,
                        Arrays.asList(-183.40332, -185.39075, -182.89119, -180.69312, -179.86134, -181.6753),
                        Arrays.asList(9.682116, 8.2235565, 9.424423, 10.247399, 10.484452, 10.725328),
                        Arrays.asList(-58.1627, -58.419106, -55.950626, -53.88283, -52.704758, -52.435417)
                },
                {
                        C_N_B,
                        Arrays.asList(-276.45966, -276.2133, -274.38065, -272.36343, -271.30392, -273.96826),
                        Arrays.asList(14.099939, 13.140305, 13.899656, 14.417396, 14.564202, 14.7246),
                        Arrays.asList(-63.067997, -61.94644, -59.873528, -57.987785, -56.67504, -56.33381)
                }
        };
    }

    @DataProvider(name = "summaryModularEbitValuesFixedPriceV2500")
    public Object[][] summaryModularEbitValuesFixedPriceV2500() {
        return new Object[][] {
                {
                        C_N_A_K,
                        Arrays.asList(-1444717.0, -1519415.0, -1603741.0, -1588835.0, -1627379.0, -1701760.0),
                        Arrays.asList(-159240.0, -180979.0, -194523.0, -128179.0, -104999.0, -87130.0),
                        Arrays.asList(-1603957.0, -1700395.0, -1798265.0, -1717015.0, -1732379.0, -1788891.0)
                },
                {
                        C_N_A,
                        Arrays.asList(-1840625.0, -1928411.0, -2033090.0, -2039020.0, -2097546.0, -2199796.0),
                        Arrays.asList(-99978.0, -116283.0, -125565.0, -53319.0, -24341.0, -266.0),
                        Arrays.asList(-1940603.0, -2044694.0, -2158656.0, -2092340.0, -2121888.0, -2200063.0)
                },
                {
                        C_N_B_K,
                        Arrays.asList(-1481490.0, -1562700.0, -1650516.0, -1627216.0, -1664420.0, -1738225.0),
                        Arrays.asList(-67505.0, -80037.0, -87199.0, -26391.0, -1649.0, 19137.0),
                        Arrays.asList(-1548995.0, -1642738.0, -1737716.0, -1653607.0, -1666070.0, -1719088.0)
                },
                {
                        C_N_B,
                        Arrays.asList(-2215112.0, -2325869.0, -2452564.0, -2470244.0, -2546842.0, -2673781.0),
                        Arrays.asList(79792.0, 80382.0, 83590.0, 158662.0, 196869.0, 232048.0),
                        Arrays.asList(-2135319.0, -2245487.0, -2368974.0, -2311581.0, -2349972.0, -2441732.0)
                }
        };
    }

    @DataProvider(name = "summaryModularEbitPercentageValuesFixedPriceV2500")
    public Object[][] summaryModularEbitPercentageValuesFixedPriceV2500() {
        return new Object[][] {
                {
                        C_N_A_K,
                        Arrays.asList(-226.41806, -224.25298, -225.9423, -213.54079, -208.53258, -207.7465),
                        Arrays.asList(-10.817523, -11.222357, -11.323331, -7.0051394, -5.3877954, -4.1977324),
                        Arrays.asList(-76.012054, -74.246, -74.07265, -66.710266, -63.47476, -61.79643)
                },
                {
                        C_N_A,
                        Arrays.asList(-280.139, -276.64166, -278.5356, -266.61996, -261.624, -261.52615),
                        Arrays.asList(-5.2399483, -5.564314, -5.639724, -2.2480328, -0.9635621, -0.009917296),
                        Arrays.asList(-75.65577, -73.36853, -73.01694, -66.7072, -63.75898, -62.29734)
                },
                {
                        C_N_B_K,
                        Arrays.asList(-232.17773, -230.63799, -232.52872, -218.69588, -213.27586, -212.19482),
                        Arrays.asList(-5.731013, -6.20007, -6.326795, -1.7937737, -0.10504297, 1.1417273),
                        Arrays.asList(-85.29805, -83.45232, -83.22103, -74.64359, -70.87149, -68.891815)
                },
                {
                        C_N_B,
                        Arrays.asList(-320.24722, -316.77875, -318.86603, -306.4047, -301.21942, -301.29132),
                        Arrays.asList(4.171708, 3.836533, 3.7376635, 6.646961, 7.728431, 8.537441),
                        Arrays.asList(-81.98872, -79.362564, -78.81914, -72.39096, -69.26235, -67.72333)
                }
        };
    }

    @DataProvider(name = "summaryModularEatPercentageValuesFixedPriceV2500")
    public Object[][] summaryModularEatPercentageValuesFixedPriceV2500() {
        return new Object[][] {
                {
                        C_N_A_K,
                        Arrays.asList(-169.81355, -168.18974, -169.45674, -160.15559, -156.39942, -155.80988),
                        Arrays.asList(-8.113142, -8.416768, -8.492498, -5.2538544, -4.0408464, -3.1482993),
                        Arrays.asList(-57.009042, -55.684503, -55.554487, -50.032697, -47.60607, -46.347322)
                },
                {
                        C_N_A,
                        Arrays.asList(-210.10426, -207.48124, -208.9017, -199.96497, -196.218, -196.14462),
                        Arrays.asList(-3.9299613, -4.1732355, -4.2297927, -1.6860248, -0.7226714, -0.007438),
                        Arrays.asList(-56.741827, -55.0264, -54.7627, -50.0304, -47.819234, -46.723005)
                },
                {
                        C_N_B_K,
                        Arrays.asList(-174.13328, -172.9785, -174.39652, -164.0219, -159.9569, -159.14613),
                        Arrays.asList(-4.2982603, -4.650052, -4.7450963, -1.3453303, -0.07878822, 0.8562955),
                        Arrays.asList(-63.973535, -62.589235, -62.41577, -55.982694, -53.153616, -51.66886)
                },
                {
                        C_N_B,
                        Arrays.asList(-240.18542, -237.58405, -239.14951, -229.8035, -225.91458, -225.9685),
                        Arrays.asList(3.1287811, 2.8773998, 2.8032477, 4.9852208, 5.7963232, 6.4030813),
                        Arrays.asList(-61.49154, -59.52192, -59.114355, -54.293222, -51.946767, -50.792497)
                }
        };
    }

    @DataProvider(name = "workscopeModularSummaryNetMarginPercentageFixedPriceV2500")
    public Object[][] workscopeModularSummaryNetMarginPercentageFixedPriceV2500() {
        return new Object[][] {
                {
                        C_N_A_K,
                        Arrays.asList(-172.21355, -170.58974, -171.85674, -162.55559, -158.79942, -158.20988),
                        Arrays.asList(-10.513142, -10.816768, -10.892498, -7.6538544, -6.4408464, -5.5482993),
                        Arrays.asList(-59.409042, -58.084503, -57.954487, -52.432697, -50.00607, -48.747322)
                },
                {
                        C_N_A,
                        Arrays.asList(-212.50426, -209.88124, -211.3017, -202.36497, -198.618, -198.54462),
                        Arrays.asList(-6.3299613, -6.5732355, -6.6297927, -4.0860248, -3.1226714, -2.407438),
                        Arrays.asList(-59.141827, -57.4264, -57.1627, -52.4304, -50.219234, -49.123005)
                },
                {
                        C_N_B_K,
                        Arrays.asList(-176.53328, -175.3785, -176.79652, -166.4219, -162.3569, -161.54613),
                        Arrays.asList(-6.6982603, -7.050052, -7.1450963, -3.7453303, -2.4787822, -1.5437045),
                        Arrays.asList(-66.373535, -64.989235, -64.81577, -58.382694, -55.553616, -54.06886)
                },
                {
                        C_N_B,
                        Arrays.asList(-242.58542, -239.98405, -241.54951, -232.2035, -228.31458, -228.3685),
                        Arrays.asList(0.7287811, 0.4773998, 0.4032477, 2.5852208, 3.3963232, 4.0030813),
                        Arrays.asList(-63.89154, -61.92192, -61.514355, -56.693222, -54.346767, -53.192497)
                }
        };
    }
}
