package com.lht.corecalculation.api.wssummarymodularntefixprice;

import com.lht.corecalculation.api.pojo.dto.wssummary.WorkscopeSummaryItemDto;
import com.lht.corecalculation.api.utils.WorkscopeUtil;
import com.lht.corecalculation.api.wssummarynteandfixprice.DataProvidersWorkscopeSummaryFixedPriceLeap1A;
import io.restassured.response.Response;
import java.io.IOException;
import java.util.List;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;


import static com.lht.corecalculation.api.constants.GlobalConstants.ADMIN_USER_PASSWORD;
import static com.lht.corecalculation.api.constants.GlobalConstants.ADMIN_USER_USERNAME;
import static com.lht.corecalculation.api.constants.GlobalConstants.ATTRIBUTE_DO_NOT_MATCH_FOR_WORKSCOPE;
import static com.lht.corecalculation.api.constants.GlobalConstants.BIG_TOLERANCE;
import static com.lht.corecalculation.api.constants.GlobalConstants.DB2;
import static com.lht.corecalculation.api.constants.GlobalConstants.DB2_PERCENTAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.DISCOUNT;
import static com.lht.corecalculation.api.constants.GlobalConstants.EAT_PERCENTAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.EBIT;
import static com.lht.corecalculation.api.constants.GlobalConstants.EBIT_PERCENTAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_LEAP_1A_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.NET_MARGIN;
import static com.lht.corecalculation.api.constants.GlobalConstants.PRODUCTION_COST;
import static com.lht.corecalculation.api.constants.GlobalConstants.PRODUCTION_COST_AFTER_DISCOUNTS_AND_SURCHARGES;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_200;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.REVENUE;
import static com.lht.corecalculation.api.constants.GlobalConstants.SURCHARGES_COST;
import static com.lht.corecalculation.api.constants.GlobalConstants.TOLERANCE;
import static com.lht.corecalculation.api.utils.WorkscopeUtil.assertListEqualsWithTolerance;
import static com.lht.corecalculation.api.utils.WorkscopeUtil.updateEngineValuesForLeap1aFixedPrice;
import static org.testng.Assert.assertEquals;

public class GetWsModularFixedAndNteLeap1aApiTests extends DataProvidersWorkscopeSummaryModularFixedPriceLeap1A {

    private static Response sharedWorkscopeSummaryResponse;

    @BeforeClass(alwaysRun = true)
    public void fetchWorkscopeSummary() throws IOException, InterruptedException, WorkscopeUtil.ApiException {
        String adminToken = utils.sso.getAccessTokenFor(ADMIN_USER_USERNAME, ADMIN_USER_PASSWORD);
        updateEngineValuesForLeap1aFixedPrice(adminToken);
        sharedWorkscopeSummaryResponse = WorkscopeUtil.initializeWorkscopeAndAwaitCompletionThenFetchSummary(ENGINE_LEAP_1A_QUOTATION_ID, adminToken);
        assertEquals(sharedWorkscopeSummaryResponse.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = "modularFixedPriceAndNte", dataProvider = "summaryModularProductionCostFixedPriceLeap1a")
    public void verifyWorkscopeSummary_ProductionCost_FixedPrice_Leap1a(
            String workscope,
            List<Double> expectedProductionCost
    ) throws IOException {
        String errorMessage = String.format(ATTRIBUTE_DO_NOT_MATCH_FOR_WORKSCOPE, PRODUCTION_COST, workscope);
        List<Double> actualProductionCost = WorkscopeUtil.extractValueFromResponse(sharedWorkscopeSummaryResponse, WorkscopeSummaryItemDto::getProductionCost, workscope);
        assertListEqualsWithTolerance(actualProductionCost, expectedProductionCost, BIG_TOLERANCE, errorMessage);
    }

    @Test(groups = "modularFixedPriceAndNte", dataProvider = "summaryModularDiscountsFixedPriceLeap1a")
    public void verifyWorkscopeSummary_Discounts_FixedPrice_Leap1a(String workscope, List<Double> expectedDiscount) throws IOException {
        String errorMessage = String.format(ATTRIBUTE_DO_NOT_MATCH_FOR_WORKSCOPE, DISCOUNT, workscope);
        List<Double> actualDiscount = WorkscopeUtil.extractValueFromResponse(sharedWorkscopeSummaryResponse, WorkscopeSummaryItemDto::getDiscount, workscope);
        assertListEqualsWithTolerance(actualDiscount, expectedDiscount, BIG_TOLERANCE, errorMessage);
    }

    @Test(groups = "modularFixedPriceAndNte", dataProvider = "summaryModularRevenueFixedPriceLeap1a")
    public void verifyWorkscopeSummary_Revenue_FixedPrice_Leap1a(String workscope, List<Double> expectedRevenue) throws IOException {
        String errorMessage = String.format(ATTRIBUTE_DO_NOT_MATCH_FOR_WORKSCOPE, REVENUE, workscope);
        List<Double> actualRevenue = WorkscopeUtil.extractValueFromResponse(sharedWorkscopeSummaryResponse, WorkscopeSummaryItemDto::getRevenue, workscope);
        assertListEqualsWithTolerance(actualRevenue, expectedRevenue, BIG_TOLERANCE, errorMessage);
    }

    @Test(groups = "modularFixedPriceAndNte", dataProvider = "summaryModularSurchargesCostFixedPriceLeap1a")
    public void verifyWorkscopeSummary_SurchargesCost_FixedPrice_Leap1a(
            String workscope,
            List<Double> expectedSurchargesCost
    ) throws IOException {
        String errorMessage = String.format(ATTRIBUTE_DO_NOT_MATCH_FOR_WORKSCOPE, SURCHARGES_COST, workscope);
        List<Double> actualSurchargesCost = WorkscopeUtil.extractValueFromResponse(sharedWorkscopeSummaryResponse, WorkscopeSummaryItemDto::getSurchargesCost, workscope);
        assertEquals(actualSurchargesCost, expectedSurchargesCost, errorMessage);
    }

    @Test(groups = "modularFixedPriceAndNte", dataProvider = "summaryModularProdCostInclDiscountsAndSurchargesFixedPriceLeap1a")
    public void verifyWorkscopeSummary_ProdCostInclDiscountsAndSurcharges_FixedPrice_Leap1a(
            String workscope,
            List<Double> expectedProdCostInclDiscountsAndSurcharges
    ) throws IOException {
        String errorMessage = String.format(ATTRIBUTE_DO_NOT_MATCH_FOR_WORKSCOPE, PRODUCTION_COST_AFTER_DISCOUNTS_AND_SURCHARGES, workscope);
        List<Double> actalProdCostInclDiscountsAndSurcharges = WorkscopeUtil.extractValueFromResponse(sharedWorkscopeSummaryResponse, WorkscopeSummaryItemDto::getProductionCostAfterDiscountAndSurcharges, workscope);
        assertListEqualsWithTolerance(actalProdCostInclDiscountsAndSurcharges, expectedProdCostInclDiscountsAndSurcharges, BIG_TOLERANCE, errorMessage);
    }

    @Test(groups = "modularFixedPriceAndNte", dataProvider = "summaryModularDb2ValuesFixedPriceLeap1a")
    public void verifyWorkscopeSummary_db2_FixedPrice_Leap1a(String workscope, List<Double> expectedDb2) throws IOException {
        String errorMessage = String.format(ATTRIBUTE_DO_NOT_MATCH_FOR_WORKSCOPE, DB2, workscope);
        List<Double> actualDb2 = WorkscopeUtil.extractValueFromResponse(sharedWorkscopeSummaryResponse, WorkscopeSummaryItemDto::getDb2, workscope);
        assertListEqualsWithTolerance(actualDb2, expectedDb2, BIG_TOLERANCE, errorMessage);
    }

    @Test(groups = "modularFixedPriceAndNte", dataProvider = "summaryModularDb2PercentageValuesFixedPriceLeap1a")
    public void verifyWorkscopeSummary_db2Percentage_FixedPrice_Leap1a(
            String workscope,
            List<Double> expecteddD2Percentage
    ) throws IOException {
        String errorMessage = String.format(ATTRIBUTE_DO_NOT_MATCH_FOR_WORKSCOPE, DB2_PERCENTAGE, workscope);
        List<Double> actualDb2Percentage = WorkscopeUtil.extractValueFromResponse(sharedWorkscopeSummaryResponse, WorkscopeSummaryItemDto::getDb2Percentage, workscope);
        assertListEqualsWithTolerance(actualDb2Percentage, expecteddD2Percentage, TOLERANCE, errorMessage);
    }

    @Test(groups = "modularFixedPriceAndNte", dataProvider = "summaryModularEbitValuesFixedPriceLeap1a")
    public void verifyWorkscopeSummary_ebit_FixedPrice_Leap1a(String workscope, List<Double> expectedEbit) throws IOException {
        String errorMessage = String.format(ATTRIBUTE_DO_NOT_MATCH_FOR_WORKSCOPE, EBIT, workscope);
        List<Double> actualEbit = WorkscopeUtil.extractValueFromResponse(sharedWorkscopeSummaryResponse, WorkscopeSummaryItemDto::getEbit, workscope);
        assertListEqualsWithTolerance(actualEbit, expectedEbit, BIG_TOLERANCE, errorMessage);
    }

    @Test(groups = "modularFixedPriceAndNte", dataProvider = "summaryModularEbitPercentageValuesFixedPriceLeap1a")
    public void verifyWorkscopeSummary_ebitPercentage_FixedPrice_Leap1a(
            String workscope,
            List<Double> expectedEbitPercentage
    ) throws IOException {
        String errorMessage = String.format(ATTRIBUTE_DO_NOT_MATCH_FOR_WORKSCOPE, EBIT_PERCENTAGE, workscope);
        List<Double> actualEbitPercentage = WorkscopeUtil.extractValueFromResponse(sharedWorkscopeSummaryResponse, WorkscopeSummaryItemDto::getEbitPercentage, workscope);
        assertListEqualsWithTolerance(actualEbitPercentage, expectedEbitPercentage, TOLERANCE, errorMessage);
    }

    @Test(groups = "modularFixedPriceAndNte", dataProvider = "summaryModularEatPercentageValuesFixedPriceLeap1a")
    public void verifyWorkscopeSummary_eatPercentage_FixedPrice_Leap1a(
            String workscope,
            List<Double> expectedEatPercentage
    ) throws IOException {
        String errorMessage = String.format(ATTRIBUTE_DO_NOT_MATCH_FOR_WORKSCOPE, EAT_PERCENTAGE, workscope);
        List<Double> actalEatPercentage = WorkscopeUtil.extractValueFromResponse(sharedWorkscopeSummaryResponse, WorkscopeSummaryItemDto::getEatPercentage, workscope);
        assertListEqualsWithTolerance(actalEatPercentage, expectedEatPercentage, TOLERANCE, errorMessage);
    }

    @Test(groups = "modularFixedPriceAndNte", dataProvider = "summaryModularNetMarginePercentageFixedPriceLeap1a")
    public void verifyWorkscopeSummary_netMargine_FixedPrice_Leap1a(
            String workscope,
            List<Double> expectedNetMargin
    ) throws IOException {
        String errorMessage = String.format(ATTRIBUTE_DO_NOT_MATCH_FOR_WORKSCOPE, NET_MARGIN, workscope);
        List<Double> actualNetMargin = WorkscopeUtil.extractValueFromResponse(sharedWorkscopeSummaryResponse, WorkscopeSummaryItemDto::getNetMargin, workscope);
        assertListEqualsWithTolerance(actualNetMargin, expectedNetMargin, TOLERANCE, errorMessage);
    }
}