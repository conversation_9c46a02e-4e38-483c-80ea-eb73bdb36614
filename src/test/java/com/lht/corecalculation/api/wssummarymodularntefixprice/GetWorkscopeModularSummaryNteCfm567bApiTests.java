package com.lht.corecalculation.api.wssummarymodularntefixprice;

import com.lht.corecalculation.api.pojo.dto.wssummary.WorkscopeSummaryItemDto;
import com.lht.corecalculation.api.utils.WorkscopeUtil;
import io.restassured.response.Response;
import java.io.IOException;
import java.util.List;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;


import static com.lht.corecalculation.api.constants.GlobalConstants.ADMIN_USER_PASSWORD;
import static com.lht.corecalculation.api.constants.GlobalConstants.ADMIN_USER_USERNAME;
import static com.lht.corecalculation.api.constants.GlobalConstants.ATTRIBUTE_DO_NOT_MATCH_FOR_WORKSCOPE;
import static com.lht.corecalculation.api.constants.GlobalConstants.BIG_TOLERANCE;
import static com.lht.corecalculation.api.constants.GlobalConstants.DB2;
import static com.lht.corecalculation.api.constants.GlobalConstants.DB2_PERCENTAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.DISCOUNT;
import static com.lht.corecalculation.api.constants.GlobalConstants.EAT_PERCENTAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.EBIT;
import static com.lht.corecalculation.api.constants.GlobalConstants.EBIT_PERCENTAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_CFM56_7B_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.NET_MARGIN;
import static com.lht.corecalculation.api.constants.GlobalConstants.PRODUCTION_COST;
import static com.lht.corecalculation.api.constants.GlobalConstants.PRODUCTION_COST_AFTER_DISCOUNTS_AND_SURCHARGES;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_200;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.REVENUE;
import static com.lht.corecalculation.api.constants.GlobalConstants.SURCHARGES_COST;
import static com.lht.corecalculation.api.constants.GlobalConstants.TOLERANCE;
import static com.lht.corecalculation.api.enums.WorkscopeItemType.EXCLUDED_ITEMS;
import static com.lht.corecalculation.api.enums.WorkscopeItemType.INCLUDED_ITEMS;
import static com.lht.corecalculation.api.enums.WorkscopeItemType.TOTAL_ITEMS;
import static com.lht.corecalculation.api.utils.WorkscopeUtil.assertListEqualsWithTolerance;
import static com.lht.corecalculation.api.utils.WorkscopeUtil.extractValueFromResponse;
import static com.lht.corecalculation.api.utils.WorkscopeUtil.updateEngineValuesForCfm567bNte;
import static org.testng.Assert.assertEquals;

public class GetWorkscopeModularSummaryNteCfm567bApiTests extends DataProvidersModularWorkscopeSummaryNteCfm567b {

    private static Response sharedWorkscopeSummaryResponse;

    @BeforeClass(alwaysRun = true)
    public void fetchWorkscopeSummary() throws IOException, InterruptedException, WorkscopeUtil.ApiException {
        String adminToken = utils.sso.getAccessTokenFor(ADMIN_USER_USERNAME, ADMIN_USER_PASSWORD);
        updateEngineValuesForCfm567bNte(adminToken);
        sharedWorkscopeSummaryResponse = WorkscopeUtil.initializeWorkscopeAndAwaitCompletionThenFetchSummary(ENGINE_CFM56_7B_QUOTATION_ID, adminToken);
        assertEquals(sharedWorkscopeSummaryResponse.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = "modularFixedPriceAndNte", dataProvider = "summaryProductionCostNteCfm567b")
    public void verifyWorkscopeSummary_ProductionCost_Nte_Cfm567b(
            String workscope,
            List<Double> expectedIncludedProductionCost,
            List<Double> expectedExcludedProductionCost,
            List<Double> expectedTotalProductionCost
    ) throws IOException {
        String errorMessage = String.format(ATTRIBUTE_DO_NOT_MATCH_FOR_WORKSCOPE, PRODUCTION_COST, workscope);

        List<Double> actualIncludedProductionCost = extractValueFromResponse(sharedWorkscopeSummaryResponse, WorkscopeSummaryItemDto::getProductionCost, workscope, INCLUDED_ITEMS);
        List<Double> actualExcludedProductionCost = extractValueFromResponse(sharedWorkscopeSummaryResponse, WorkscopeSummaryItemDto::getProductionCost, workscope, EXCLUDED_ITEMS);
        List<Double> actualTotalProductionCost = extractValueFromResponse(sharedWorkscopeSummaryResponse, WorkscopeSummaryItemDto::getProductionCost, workscope, TOTAL_ITEMS);

        assertListEqualsWithTolerance(actualIncludedProductionCost, expectedIncludedProductionCost, BIG_TOLERANCE, errorMessage);
        assertListEqualsWithTolerance(actualExcludedProductionCost, expectedExcludedProductionCost, BIG_TOLERANCE, errorMessage);
        assertListEqualsWithTolerance(actualTotalProductionCost, expectedTotalProductionCost, BIG_TOLERANCE, errorMessage);
    }

    @Test(groups = "modularFixedPriceAndNte", dataProvider = "summaryDiscountsNteCfm567b")
    public void verifyWorkscopeSummary_Discounts_Nte_Cfm567b(
            String workscope,
            List<Double> expectedIncludedDiscount,
            List<Double> expectedExcludedDiscount,
            List<Double> expectedTotalDiscount
    ) throws IOException {
        String errorMessage = String.format(ATTRIBUTE_DO_NOT_MATCH_FOR_WORKSCOPE, DISCOUNT, workscope);

        List<Double> actualIncludedDiscount = extractValueFromResponse(sharedWorkscopeSummaryResponse, WorkscopeSummaryItemDto::getDiscount, workscope, INCLUDED_ITEMS);
        List<Double> actualExcludedDiscount = extractValueFromResponse(sharedWorkscopeSummaryResponse, WorkscopeSummaryItemDto::getDiscount, workscope, EXCLUDED_ITEMS);
        List<Double> actualTotalDiscount = extractValueFromResponse(sharedWorkscopeSummaryResponse, WorkscopeSummaryItemDto::getDiscount, workscope, TOTAL_ITEMS);

        assertListEqualsWithTolerance(actualIncludedDiscount, expectedIncludedDiscount, BIG_TOLERANCE, errorMessage);
        assertListEqualsWithTolerance(actualExcludedDiscount, expectedExcludedDiscount, BIG_TOLERANCE, errorMessage);
        assertListEqualsWithTolerance(actualTotalDiscount, expectedTotalDiscount, BIG_TOLERANCE, errorMessage);
    }

    @Test(groups = "modularFixedPriceAndNte", dataProvider = "summaryRevenueNteCfm567b")
    public void verifyWorkscopeSummary_Revenue_Nte_Cfm567b(
            String workscope,
            List<Double> expectedIncludedRevenue,
            List<Double> expectedExcludedRevenue,
            List<Double> expectedTotalRevenue
    ) throws IOException {
        String errorMessage = String.format(ATTRIBUTE_DO_NOT_MATCH_FOR_WORKSCOPE, REVENUE, workscope);

        List<Double> actualIncludedRevenue = extractValueFromResponse(sharedWorkscopeSummaryResponse, WorkscopeSummaryItemDto::getRevenue, workscope, INCLUDED_ITEMS);
        List<Double> actualExcludedRevenue = extractValueFromResponse(sharedWorkscopeSummaryResponse, WorkscopeSummaryItemDto::getRevenue, workscope, EXCLUDED_ITEMS);
        List<Double> actualTotalRevenue = extractValueFromResponse(sharedWorkscopeSummaryResponse, WorkscopeSummaryItemDto::getRevenue, workscope, TOTAL_ITEMS);

        assertListEqualsWithTolerance(actualIncludedRevenue, expectedIncludedRevenue, BIG_TOLERANCE, errorMessage);
        assertListEqualsWithTolerance(actualExcludedRevenue, expectedExcludedRevenue, BIG_TOLERANCE, errorMessage);
        assertListEqualsWithTolerance(actualTotalRevenue, expectedTotalRevenue, BIG_TOLERANCE, errorMessage);
    }

    @Test(groups = "modularFixedPriceAndNte", dataProvider = "summarySurchargesNtePriceCfm567b")
    public void verifyWorkscopeSummary_SurchargesCost_Nte_Cfm567b(
            String workscope,
            List<Double> expectedIncludedSurchargesCost,
            List<Double> expectedExcludedSurchargesCost,
            List<Double> expectedTotalSurchargesCost
    ) throws IOException {
        String errorMessage = String.format(ATTRIBUTE_DO_NOT_MATCH_FOR_WORKSCOPE, SURCHARGES_COST, workscope);

        List<Double> actualIncludedSurchargesCost = extractValueFromResponse(sharedWorkscopeSummaryResponse, WorkscopeSummaryItemDto::getSurchargesCost, workscope, INCLUDED_ITEMS);
        List<Double> actualExcludedSurchargesCost = extractValueFromResponse(sharedWorkscopeSummaryResponse, WorkscopeSummaryItemDto::getSurchargesCost, workscope, EXCLUDED_ITEMS);
        List<Double> actualTotalSurchargesCost = extractValueFromResponse(sharedWorkscopeSummaryResponse, WorkscopeSummaryItemDto::getSurchargesCost, workscope, TOTAL_ITEMS);

        assertEquals(actualIncludedSurchargesCost, expectedIncludedSurchargesCost, errorMessage);
        assertEquals(actualExcludedSurchargesCost, expectedExcludedSurchargesCost, errorMessage);
        assertEquals(actualTotalSurchargesCost, expectedTotalSurchargesCost, errorMessage);
    }

    @Test(groups = "modularFixedPriceAndNte", dataProvider = "summaryProdCostInclDiscountsAndSurchargesNteCfm567b")
    public void verifyWorkscopeSummary_ProdCostInclDiscountsAndSurcharges_Nte_Cfm567b(
            String workscope,
            List<Double> expectedIncludedProdCostInclDiscountsAndSurcharges,
            List<Double> expectedExcludedProdCostInclDiscountsAndSurcharges,
            List<Double> expectedTotalProdCostInclDiscountsAndSurcharges
    ) throws IOException {
        String errorMessage = String.format(ATTRIBUTE_DO_NOT_MATCH_FOR_WORKSCOPE, PRODUCTION_COST_AFTER_DISCOUNTS_AND_SURCHARGES, workscope);

        List<Double> actualIncludedProdCostInclDiscountsAndSurcharges = extractValueFromResponse(sharedWorkscopeSummaryResponse, WorkscopeSummaryItemDto::getProductionCostAfterDiscountAndSurcharges, workscope, INCLUDED_ITEMS);
        List<Double> actualExcludedProdCostInclDiscountsAndSurcharges = extractValueFromResponse(sharedWorkscopeSummaryResponse, WorkscopeSummaryItemDto::getProductionCostAfterDiscountAndSurcharges, workscope, EXCLUDED_ITEMS);
        List<Double> actualTotalProdCostInclDiscountsAndSurcharges = extractValueFromResponse(sharedWorkscopeSummaryResponse, WorkscopeSummaryItemDto::getProductionCostAfterDiscountAndSurcharges, workscope, TOTAL_ITEMS);

        assertListEqualsWithTolerance(actualIncludedProdCostInclDiscountsAndSurcharges, expectedIncludedProdCostInclDiscountsAndSurcharges, BIG_TOLERANCE, errorMessage);
        assertListEqualsWithTolerance(actualExcludedProdCostInclDiscountsAndSurcharges, expectedExcludedProdCostInclDiscountsAndSurcharges, BIG_TOLERANCE, errorMessage);
        assertListEqualsWithTolerance(actualTotalProdCostInclDiscountsAndSurcharges, expectedTotalProdCostInclDiscountsAndSurcharges, BIG_TOLERANCE, errorMessage);
    }

    @Test(groups = "modularFixedPriceAndNte", dataProvider = "summaryDb2ValuesNteCfm567b")
    public void verifyWorkscopeSummary_db2_Nte_Cfm567b(
            String workscope,
            List<Double> expectedIncludedDb2,
            List<Double> expectedExcludedDb2,
            List<Double> expectedTotalDb2
    ) throws IOException {
        String errorMessage = String.format(ATTRIBUTE_DO_NOT_MATCH_FOR_WORKSCOPE, DB2, workscope);

        List<Double> actualIncludedDb2 = extractValueFromResponse(sharedWorkscopeSummaryResponse, WorkscopeSummaryItemDto::getDb2, workscope, INCLUDED_ITEMS);
        List<Double> actualExcludedDb2 = extractValueFromResponse(sharedWorkscopeSummaryResponse, WorkscopeSummaryItemDto::getDb2, workscope, EXCLUDED_ITEMS);
        List<Double> actualTotalDb2 = extractValueFromResponse(sharedWorkscopeSummaryResponse, WorkscopeSummaryItemDto::getDb2, workscope, TOTAL_ITEMS);

        assertListEqualsWithTolerance(actualIncludedDb2, expectedIncludedDb2, BIG_TOLERANCE, errorMessage);
        assertListEqualsWithTolerance(actualExcludedDb2, expectedExcludedDb2, BIG_TOLERANCE, errorMessage);
        assertListEqualsWithTolerance(actualTotalDb2, expectedTotalDb2, BIG_TOLERANCE, errorMessage);
    }

    @Test(groups = "modularFixedPriceAndNte", dataProvider = "summaryDb2PercentageValuesNteCfm567b")
    public void verifyWorkscopeSummary_db2Percentage_Nte_Cfm567b(
            String workscope,
            List<Double> expectedIncludedDb2Percentage,
            List<Double> expectedExcludedDb2Percentage,
            List<Double> expectedTotalDb2Percentage
    ) throws IOException {
        String errorMessage = String.format(ATTRIBUTE_DO_NOT_MATCH_FOR_WORKSCOPE, DB2_PERCENTAGE, workscope);

        List<Double> actualIncludedDb2Percentage = extractValueFromResponse(sharedWorkscopeSummaryResponse, WorkscopeSummaryItemDto::getDb2Percentage, workscope, INCLUDED_ITEMS);
        List<Double> actualExcludedDb2Percentage = extractValueFromResponse(sharedWorkscopeSummaryResponse, WorkscopeSummaryItemDto::getDb2Percentage, workscope, EXCLUDED_ITEMS);
        List<Double> actualTotalDb2Percentage = extractValueFromResponse(sharedWorkscopeSummaryResponse, WorkscopeSummaryItemDto::getDb2Percentage, workscope, TOTAL_ITEMS);

        assertListEqualsWithTolerance(actualIncludedDb2Percentage, expectedIncludedDb2Percentage, BIG_TOLERANCE, errorMessage);
        assertListEqualsWithTolerance(actualExcludedDb2Percentage, expectedExcludedDb2Percentage, BIG_TOLERANCE, errorMessage);
        assertListEqualsWithTolerance(actualTotalDb2Percentage, expectedTotalDb2Percentage, BIG_TOLERANCE, errorMessage);
    }

    @Test(groups = "modularFixedPriceAndNte", dataProvider = "summaryEbitValuesNteCfm567b")
    public void verifyWorkscopeSummary_ebit_Nte_Cfm567b(
            String workscope,
            List<Double> expectedIncludedEbit,
            List<Double> expectedExcludedEbit,
            List<Double> expectedTotalEbit
    ) throws IOException {
        String errorMessage = String.format(ATTRIBUTE_DO_NOT_MATCH_FOR_WORKSCOPE, EBIT, workscope);

        List<Double> actualIncludedEbit = extractValueFromResponse(sharedWorkscopeSummaryResponse, WorkscopeSummaryItemDto::getEbit, workscope, INCLUDED_ITEMS);
        List<Double> actualExcludedEbit = extractValueFromResponse(sharedWorkscopeSummaryResponse, WorkscopeSummaryItemDto::getEbit, workscope, EXCLUDED_ITEMS);
        List<Double> actualTotalEbit = extractValueFromResponse(sharedWorkscopeSummaryResponse, WorkscopeSummaryItemDto::getEbit, workscope, TOTAL_ITEMS);

        assertListEqualsWithTolerance(actualIncludedEbit, expectedIncludedEbit, BIG_TOLERANCE, errorMessage);
        assertListEqualsWithTolerance(actualExcludedEbit, expectedExcludedEbit, BIG_TOLERANCE, errorMessage);
        assertListEqualsWithTolerance(actualTotalEbit, expectedTotalEbit, BIG_TOLERANCE, errorMessage);
    }

    @Test(groups = "modularFixedPriceAndNte", dataProvider = "summaryEbitPercentageValuesNteCfm567b")
    public void verifyWorkscopeSummary_ebitPercentage_Nte_Cfm567b(
            String workscope,
            List<Double> expectedIncludedEbitPercentage,
            List<Double> expectedExcludedEbitPercentage,
            List<Double> expectedTotalEbitPercentage
    ) throws IOException {
        String errorMessage = String.format(ATTRIBUTE_DO_NOT_MATCH_FOR_WORKSCOPE, EBIT_PERCENTAGE, workscope);

        List<Double> actualIncludedEbitPercentage = extractValueFromResponse(sharedWorkscopeSummaryResponse, WorkscopeSummaryItemDto::getEbitPercentage, workscope, INCLUDED_ITEMS);
        List<Double> actualExcludedEbitPercentage = extractValueFromResponse(sharedWorkscopeSummaryResponse, WorkscopeSummaryItemDto::getEbitPercentage, workscope, EXCLUDED_ITEMS);
        List<Double> actualTotalEbitPercentage = extractValueFromResponse(sharedWorkscopeSummaryResponse, WorkscopeSummaryItemDto::getEbitPercentage, workscope, TOTAL_ITEMS);

        assertListEqualsWithTolerance(actualIncludedEbitPercentage, expectedIncludedEbitPercentage, TOLERANCE, errorMessage);
        assertListEqualsWithTolerance(actualExcludedEbitPercentage, expectedExcludedEbitPercentage, TOLERANCE, errorMessage);
        assertListEqualsWithTolerance(actualTotalEbitPercentage, expectedTotalEbitPercentage, TOLERANCE, errorMessage);
    }

    @Test(groups = "modularFixedPriceAndNte", dataProvider = "summaryEatPercentageValuesNteCfm567b")
    public void verifyWorkscopeSummary_eatPercentage_Nte_Cfm567b(
            String workscope,
            List<Double> expectedIncludedEatPercentage,
            List<Double> expectedExcludedEatPercentage,
            List<Double> expectedTotalEatPercentage
    ) throws IOException {
        String errorMessage = String.format(ATTRIBUTE_DO_NOT_MATCH_FOR_WORKSCOPE, EAT_PERCENTAGE, workscope);

        List<Double> actualIncludedEatPercentage = extractValueFromResponse(sharedWorkscopeSummaryResponse, WorkscopeSummaryItemDto::getEatPercentage, workscope, INCLUDED_ITEMS);
        List<Double> actualExcludedEatPercentage = extractValueFromResponse(sharedWorkscopeSummaryResponse, WorkscopeSummaryItemDto::getEatPercentage, workscope, EXCLUDED_ITEMS);
        List<Double> actualTotalEatPercentage = extractValueFromResponse(sharedWorkscopeSummaryResponse, WorkscopeSummaryItemDto::getEatPercentage, workscope, TOTAL_ITEMS);

        assertListEqualsWithTolerance(actualIncludedEatPercentage, expectedIncludedEatPercentage, TOLERANCE, errorMessage);
        assertListEqualsWithTolerance(actualExcludedEatPercentage, expectedExcludedEatPercentage, TOLERANCE, errorMessage);
        assertListEqualsWithTolerance(actualTotalEatPercentage, expectedTotalEatPercentage, TOLERANCE, errorMessage);
    }

    @Test(groups = "modularFixedPriceAndNte", dataProvider = "summaryNetMarginPercentageNteCfm567b")
    public void verifyWorkscopeSummary_netMargin_Nte_Cfm567b(
            String workscope,
            List<Double> expectedIncludedNetMarginPercentage,
            List<Double> expectedExcludedNetMarginPercentage,
            List<Double> expectedTotalNetMarginPercentage
    ) throws IOException {
        String errorMessage = String.format(ATTRIBUTE_DO_NOT_MATCH_FOR_WORKSCOPE, NET_MARGIN, workscope);

        List<Double> actualIncludedNetMarginPercentage = extractValueFromResponse(sharedWorkscopeSummaryResponse, WorkscopeSummaryItemDto::getNetMargin, workscope, INCLUDED_ITEMS);
        List<Double> actualExcludedNetMarginPercentage = extractValueFromResponse(sharedWorkscopeSummaryResponse, WorkscopeSummaryItemDto::getNetMargin, workscope, EXCLUDED_ITEMS);
        List<Double> actualTotalNetMarginPercentage = extractValueFromResponse(sharedWorkscopeSummaryResponse, WorkscopeSummaryItemDto::getNetMargin, workscope, TOTAL_ITEMS);

        assertListEqualsWithTolerance(actualIncludedNetMarginPercentage, expectedIncludedNetMarginPercentage, TOLERANCE, errorMessage);
        assertListEqualsWithTolerance(actualExcludedNetMarginPercentage, expectedExcludedNetMarginPercentage, TOLERANCE, errorMessage);
        assertListEqualsWithTolerance(actualTotalNetMarginPercentage, expectedTotalNetMarginPercentage, TOLERANCE, errorMessage);
    }
}