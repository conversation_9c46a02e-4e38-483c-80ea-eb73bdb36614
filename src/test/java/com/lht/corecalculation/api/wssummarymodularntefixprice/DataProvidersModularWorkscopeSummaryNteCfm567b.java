package com.lht.corecalculation.api.wssummarymodularntefixprice;

import com.lht.corecalculation.base.BaseCocaApiTest;
import java.util.Arrays;
import org.testng.annotations.DataProvider;


import static com.lht.corecalculation.api.constants.GlobalConstants.WS1;
import static com.lht.corecalculation.api.constants.GlobalConstants.WS2;

public class DataProvidersModularWorkscopeSummaryNteCfm567b extends BaseCocaApiTest {

    @DataProvider(name = "summaryProductionCostNteCfm567b")
    public Object[][] workscopeSummaryProductionCostDataProviderNteCfm567b() {
        return new Object[][] {
                {
                        WS1,
                        Arrays.asList(2335719.0, 2450265.0, 2560613.0),
                        Arrays.asList(1069330.0, 1135615.0, 1204832.0),
                        Arrays.asList(3405049.0, 3585881.0, 3765446.0)
                },
                {
                        WS2,
                        Arrays.asList(2401434.0, 2519209.0, 2629703.0),
                        Arrays.asList(519470.0, 551774.0, 584295.0),
                        Arrays.asList(2920904.0, 3070984.0, 3213998.0)
                }
        };
    }

    @DataProvider(name = "summaryDiscountsNteCfm567b")
    public Object[][] workscopeSummaryDiscountsDataProviderNteCfm567b() {
        return new Object[][] {
                {
                        WS1,
                        Arrays.asList(206300.0, 227077.0, 249281.0),
                        Arrays.asList(110478.0, 128983.0, 149273.0),
                        Arrays.asList(316778.0, 356061.0, 398555.0)
                },
                {
                        WS2,
                        Arrays.asList(159657.0, 177601.0, 196582.0),
                        Arrays.asList(43942.0, 51607.0, 60036.0),
                        Arrays.asList(203600.0, 229209.0, 256618.0)
                }
        };
    }

    @DataProvider(name = "summaryRevenueNteCfm567b")
    public Object[][] workscopeSummaryRevenueDataProviderNteCfm567b() {
        return new Object[][] {
                {
                        WS1,
                        Arrays.asList(919302.0, 964992.0, 1013480.0),
                        Arrays.asList(1170673.0, 1243617.0, 1320420.0),
                        Arrays.asList(2089976.0, 2208610.0, 2333900.0)
                },
                {
                        WS2,
                        Arrays.asList(919302.0, 964992.0, 1013480.0),
                        Arrays.asList(597192.0, 634505.0, 673295.0),
                        Arrays.asList(1516495.0, 1599497.0, 1686775.0)
                }
        };
    }

    @DataProvider(name = "summarySurchargesNtePriceCfm567b")
    public Object[][] workscopeSummarySurchargesCostDataProviderNteCfm567b() {
        return new Object[][] {
                {
                        WS1,
                        Arrays.asList(240123.0, 223073.0, 213413.0),
                        Arrays.asList(109932.0, 103386.0, 100416.0),
                        Arrays.asList(350056.0, 326460.0, 313829.0)
                },
                {
                        WS2,
                        Arrays.asList(281755.0, 261392.0, 249920.0),
                        Arrays.asList(60948.0, 57251.0, 55530.0),
                        Arrays.asList(342703.0, 318644.0, 305450.0)
                }
        };
    }

    @DataProvider(name = "summaryProdCostInclDiscountsAndSurchargesNteCfm567b")
    public Object[][] workscopeSummaryProdCostInclDiscountsAndSurchargesDataProviderNteCfm567b() {
        return new Object[][] {
                {
                        WS1,
                        Arrays.asList(2369542.0, 2446261.0, 2524745.0),
                        Arrays.asList(1068784.0, 1110019.0, 1155975.0),
                        Arrays.asList(3438327.0, 3556280.0, 3680720.0)
                },
                {
                        WS2,
                        Arrays.asList(2523532.0, 2602999.0, 2683041.0),
                        Arrays.asList(536476.0, 557418.0, 579789.0),
                        Arrays.asList(3060008.0, 3160418.0, 3262830.0)
                }
        };
    }

    @DataProvider(name = "summaryDb2ValuesNteCfm567b")
    public Object[][] workscopeSummaryDb2ValuesDataProviderNteCfm567b() {
        return new Object[][] {
                {
                        WS1,
                        Arrays.asList(-1450240.0, -1481268.0, -1511265.0),
                        Arrays.asList(101889.0, 133598.0, 164444.0),
                        Arrays.asList(-1348350.0, -1347670.0, -1346820.0)
                },
                {
                        WS2,
                        Arrays.asList(-1604229.0, -1638007.0, -1669561.0),
                        Arrays.asList(60716.0, 77086.0, 93506.0),
                        Arrays.asList(-1543513.0, -1560920.0, -1576055.0)
                }
        };
    }

    @DataProvider(name = "summaryDb2PercentageValuesNteCfm567b")
    public Object[][] workscopeDb2PercentageValuesDataProviderNteCfm567b() {
        return new Object[][] {
                {
                        WS1,
                        Arrays.asList(-157.75443, -153.5005, -149.1164),
                        Arrays.asList(8.703492, 10.742727, 12.45398),
                        Arrays.asList(-64.51513, -61.01892, -57.70684)
                },
                {
                        WS2,
                        Arrays.asList(-174.50507, -169.74294, -164.73547),
                        Arrays.asList(10.166923, 12.149035, 13.887814),
                        Arrays.asList(-101.78163, -97.58817, -93.43596)
                }
        };
    }

    @DataProvider(name = "summaryEbitValuesNteCfm567b")
    public Object[][] workscopeSummaryEbitValuesDataProviderNteCfm567b() {
        return new Object[][] {
                {
                        WS1,
                        Arrays.asList(-1788261.0, -1870362.0, -1824024.0),
                        Arrays.asList(-52861.0, -46733.0, 17283.0),
                        Arrays.asList(-1841123.0, -1917096.0, -1806741.0)
                },
                {
                        WS2,
                        Arrays.asList(-2009364.0, -2105122.0, -2045869.0),
                        Arrays.asList(-26921.0, -25224.0, 9893.0),
                        Arrays.asList(-2036285.0, -2130346.0, -2035976.0)
                }
        };
    }

    @DataProvider(name = "summaryEbitPercentageValuesNteCfm567b")
    public Object[][] workscopeEbitPercentageValuesDataProviderNteCfm567b() {
        return new Object[][] {
                {
                        WS1,
                        Arrays.asList(-194.52371, -193.82143, -179.97638),
                        Arrays.asList(-4.5155163, -3.7578712, 1.3089482),
                        Arrays.asList(-88.093025, -86.80102, -77.41295)
                },
                {
                        WS2,
                        Arrays.asList(-218.57489, -218.14902, -201.86581),
                        Arrays.asList(-4.507987, -3.9754734, 1.469448),
                        Arrays.asList(-134.2758, -133.18848, -120.70224)
                }
        };
    }

    @DataProvider(name = "summaryEatPercentageValuesNteCfm567b")
    public Object[][] workscopeEatPercentageValuesDataProviderNteCfm567b() {
        return new Object[][] {
                {
                        WS1,
                        Arrays.asList(-145.89278, -145.36607, -134.98228),
                        Arrays.asList(-3.3866372, -2.8184032, 0.9817111),
                        Arrays.asList(-66.06977, -65.10076, -58.05971)
                },
                {
                        WS2,
                        Arrays.asList(-163.93117, -163.61177, -151.39937),
                        Arrays.asList(-3.38099, -2.981605, 1.102086),
                        Arrays.asList(-100.70685, -99.89135, -90.52668)
                }
        };
    }

    @DataProvider(name = "summaryNetMarginPercentageNteCfm567b")
    public Object[][] workscopeNetMarginPercentageDataProviderNteCfm567b() {
        return new Object[][] {
                {
                        WS1,
                        Arrays.asList(-148.29279, -147.76607, -137.3823),
                        Arrays.asList(-5.7866373, -5.2184033, -1.418289),
                        Arrays.asList(-68.46977, -67.50076, -60.459713)
                },
                {
                        WS2,
                        Arrays.asList(-166.33118, -166.01176, -153.79938),
                        Arrays.asList(-5.78099, -5.381605, -1.297914),
                        Arrays.asList(-103.10685, -102.29135, -92.92668)
                }
        };
    }
}