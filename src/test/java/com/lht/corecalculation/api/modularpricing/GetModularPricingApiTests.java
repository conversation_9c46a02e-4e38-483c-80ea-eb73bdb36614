package com.lht.corecalculation.api.modularpricing;


import com.lht.corecalculation.api.request.modularpricing.GetModularPricingRequest;
import com.lht.corecalculation.base.BaseCocaApiTest;
import io.restassured.response.Response;
import java.io.IOException;
import org.testng.annotations.Test;


import static com.lht.corecalculation.api.constants.GlobalConstants.ADMIN_TOKEN;
import static com.lht.corecalculation.api.utils.WorkscopeUtil.updateEngineValuesForModularPrice;
import static org.testng.AssertJUnit.assertEquals;


public class GetModularPricingApiTests extends BaseCocaApiTest {

    @Test(groups = "modularFixedPriceAndNte", dataProvider = "quotationIdsByEngineProvider")
    public void verifyModularPricingReturn200forAllEngineTypes(String engineType) throws IOException {
        refreshAccessTokens();
        updateEngineValuesForModularPrice(utils.context.getContextItem(ADMIN_TOKEN), engineType);

        Response response = new GetModularPricingRequest(engineType).withBearerToken(utils.context.getContextItem(ADMIN_TOKEN)).callAPI();
        assertEquals(200, response.statusCode());
    }
}

