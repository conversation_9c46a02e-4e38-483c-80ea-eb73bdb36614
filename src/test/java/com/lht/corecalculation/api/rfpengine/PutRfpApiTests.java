package com.lht.corecalculation.api.rfpengine;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.lht.corecalculation.api.pojo.dto.rfpengine.RfpEngineResponseDto;
import com.lht.corecalculation.api.pojo.dto.rfpengine.RfpItemInputDto;
import com.lht.corecalculation.api.utils.RfpEngineUtil;
import com.lht.corecalculation.base.BaseCocaApiTest;
import io.restassured.response.Response;
import java.util.List;
import org.testng.Assert;
import org.testng.annotations.Test;


import static com.lht.corecalculation.api.constants.GlobalConstants.DIFFERENCE_BETWEEN_ACTUAL_AND_EXPECTED_PRICE;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_CFM56_5B_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_LEAP_1A_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_LEAP_1B_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_V2500_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_200;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_400;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_403;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE;

public class PutRfpApiTests extends BaseCocaApiTest {

    @Test(groups = {"smoke", "workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "adminAccessTokenProvider")
    public void verifyRfpEngine_GlobalFieldModify_LEAP_1A_255_ModifyDbii(String accessToken) throws JsonProcessingException {
        RfpEngineUtil.verifyDbiiModification(accessToken, ENGINE_LEAP_1A_QUOTATION_ID, 0.1, false);
    }

    @Test(groups = {"smoke", "workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "adminAccessTokenProvider")
    public void verifyRfpEngine_GlobalFieldModify_LEAP_1B_ModifyDbii(String accessToken) throws JsonProcessingException {
        RfpEngineUtil.verifyDbiiModification(accessToken, ENGINE_LEAP_1B_QUOTATION_ID, -99.9, false);
    }

    @Test(groups = {"smoke", "workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "adminAccessTokenProvider")
    public void verifyRfpEngine_GlobalFieldModify_V2500_ModifyDbii(String accessToken) throws JsonProcessingException {
        RfpEngineUtil.verifyDbiiModification(accessToken, ENGINE_V2500_QUOTATION_ID, 10.0, false);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "adminAccessTokenProvider")
    public void verifyRfpEngine_GlobalFieldModify_CFM56_5B_ModifyDbii(String accessToken) throws JsonProcessingException {
        RfpEngineUtil.verifyDbiiModification(accessToken, ENGINE_CFM56_5B_QUOTATION_ID, 0.0, false);
    }

    @Test(groups = "workscopeFixedPriceAndNte", dataProvider = "adminAccessTokenProvider")
    public void verifyRfpEngine_GlobalFieldModify_LEAP_1A_255_ModifyIncludeC_I_Hours(String accessToken) throws JsonProcessingException {
        RfpEngineUtil.verifyRfpEngineIncluceCiHours(accessToken, ENGINE_LEAP_1A_QUOTATION_ID, 0.0, true);
    }

    @Test(groups = "workscopeFixedPriceAndNte", dataProvider = "adminAccessTokenProvider")
    public void verifyRfpEngine_GlobalFieldModify_LEAP_1B_ModifyIncludeC_I_Hours(String accessToken) throws JsonProcessingException {
        RfpEngineUtil.verifyRfpEngineIncluceCiHours(accessToken, ENGINE_LEAP_1B_QUOTATION_ID, 0.0, true);
    }

    @Test(groups = {"smoke", "workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "adminAccessTokenProvider")
    public void verifyRfpEngine_GlobalFieldModify_V2500_ModifyIncludeC_I_Hours(String accessToken) throws JsonProcessingException {
        RfpEngineUtil.verifyRfpEngineIncluceCiHours(accessToken, ENGINE_V2500_QUOTATION_ID, 0.0, true);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "adminAccessTokenProvider")
    public void verifyRfpEngine_GlobalFieldModify_CFM56_5B_ModifyIncludeC_I_Hours(String accessToken) throws JsonProcessingException {
        RfpEngineUtil.verifyRfpEngineIncluceCiHours(accessToken, ENGINE_CFM56_5B_QUOTATION_ID, 0.0, true);
    }

    @Test(groups = "workscopeFixedPriceAndNte", dataProvider = "adminAccessTokenProvider")
    public void verifyRfpEngine_GlobalFieldModify_LEAP_1A_255_CheckPriceCalculation(String accessToken) throws JsonProcessingException {
        Response getRfpEngineResponse = RfpEngineUtil.callGetRfpEngineApi(accessToken, ENGINE_LEAP_1A_QUOTATION_ID);
        RfpEngineResponseDto rfpEngineResponse = utils.convert.jsonToDto(getRfpEngineResponse, RfpEngineResponseDto.class);

        List<RfpItemInputDto> rfpItemInputs = RfpEngineUtil.transformRfpItemsWithModifiedDbii(rfpEngineResponse, -33.3);
        Response putRfpEngineResponse = RfpEngineUtil.callPutRfpEngineApi(accessToken, true, rfpItemInputs, ENGINE_LEAP_1A_QUOTATION_ID);
        Assert.assertEquals(putRfpEngineResponse.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        Response updatedGetRfpEngineResponse = RfpEngineUtil.callGetRfpEngineApi(accessToken, ENGINE_LEAP_1A_QUOTATION_ID);
        RfpEngineResponseDto updatedRfpEngineResponseDto = utils.convert.jsonToDto(updatedGetRfpEngineResponse, RfpEngineResponseDto.class);
        List<Double> actualPrice = RfpEngineUtil.extractCiHoursPriceValuesFromResponse(updatedRfpEngineResponseDto);
        List<Double> sumPriceCostAndCiHours = RfpEngineUtil.calculateSumPriceCostAndCiHours(updatedRfpEngineResponseDto);

        List<Double> actualPriceRounded = RfpEngineUtil.roundDoublesToOneDecimal(actualPrice);
        List<Double> sumCostAndCiHoursRounded = RfpEngineUtil.roundDoublesToOneDecimal(sumPriceCostAndCiHours);

        Assert.assertEquals(actualPriceRounded, sumCostAndCiHoursRounded, DIFFERENCE_BETWEEN_ACTUAL_AND_EXPECTED_PRICE);
    }

    @Test(groups = "workscopeFixedPriceAndNte", dataProvider = "adminAccessTokenProvider")
    public void verifyRfpEngine_GlobalFieldModify_LEAP_1B_CheckPriceCalculation(String accessToken) throws JsonProcessingException {
        Response getRfpEngineResponse = RfpEngineUtil.callGetRfpEngineApi(accessToken, ENGINE_LEAP_1B_QUOTATION_ID);
        RfpEngineResponseDto rfpEngineResponse = utils.convert.jsonToDto(getRfpEngineResponse, RfpEngineResponseDto.class);

        List<RfpItemInputDto> rfpItemInputs = RfpEngineUtil.transformRfpItemsWithModifiedDbii(rfpEngineResponse, 0.1);
        Response putRfpEngineResponse = RfpEngineUtil.callPutRfpEngineApi(accessToken, true, rfpItemInputs, ENGINE_LEAP_1B_QUOTATION_ID);
        Assert.assertEquals(putRfpEngineResponse.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        Response updatedGetRfpEngineResponse = RfpEngineUtil.callGetRfpEngineApi(accessToken, ENGINE_LEAP_1B_QUOTATION_ID);
        RfpEngineResponseDto updatedRfpEngineResponseDto = utils.convert.jsonToDto(updatedGetRfpEngineResponse, RfpEngineResponseDto.class);
        List<Double> actualPrice = RfpEngineUtil.extractCiHoursPriceValuesFromResponse(updatedRfpEngineResponseDto);
        List<Double> sumPriceCostAndCiHours = RfpEngineUtil.calculateSumPriceCostAndCiHours(updatedRfpEngineResponseDto);

        List<Double> actualPriceRounded = RfpEngineUtil.roundDoublesToOneDecimal(actualPrice);
        List<Double> sumCostAndCiHoursRounded = RfpEngineUtil.roundDoublesToOneDecimal(sumPriceCostAndCiHours);

        Assert.assertEquals(actualPriceRounded, sumCostAndCiHoursRounded, DIFFERENCE_BETWEEN_ACTUAL_AND_EXPECTED_PRICE);
    }

    @Test(groups = {"smoke", "workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "adminAccessTokenProvider")
    public void verifyRfpEngine_GlobalFieldModify_V2500_CheckPriceCalculation(String accessToken) throws JsonProcessingException {
        Response getRfpEngineResponse = RfpEngineUtil.callGetRfpEngineApi(accessToken, ENGINE_V2500_QUOTATION_ID);
        RfpEngineResponseDto rfpEngineResponse = utils.convert.jsonToDto(getRfpEngineResponse, RfpEngineResponseDto.class);

        List<RfpItemInputDto> rfpItemInputs = RfpEngineUtil.transformRfpItemsWithModifiedDbii(rfpEngineResponse, 1.0);
        RfpEngineUtil.callPutRfpEngineApi(accessToken, true, rfpItemInputs, ENGINE_V2500_QUOTATION_ID);

        Response updatedGetRfpEngineResponse = RfpEngineUtil.callGetRfpEngineApi(accessToken, ENGINE_V2500_QUOTATION_ID);
        RfpEngineResponseDto updatedRfpEngineResponseDto = utils.convert.jsonToDto(updatedGetRfpEngineResponse, RfpEngineResponseDto.class);
        List<Double> actualPrice = RfpEngineUtil.extractCiHoursPriceValuesFromResponse(updatedRfpEngineResponseDto);
        List<Double> sumPriceCostAndCiHours = RfpEngineUtil.calculateSumPriceCostAndCiHours(updatedRfpEngineResponseDto);

        List<Double> actualPriceRounded = RfpEngineUtil.roundDoublesToOneDecimal(actualPrice);
        List<Double> sumCostAndCiHoursRounded = RfpEngineUtil.roundDoublesToOneDecimal(sumPriceCostAndCiHours);

        Assert.assertEquals(actualPriceRounded, sumCostAndCiHoursRounded, DIFFERENCE_BETWEEN_ACTUAL_AND_EXPECTED_PRICE);
    }

    @Test(groups = "workscopeFixedPriceAndNte", dataProvider = "adminAccessTokenProvider")
    public void verifyRfpEngine_GlobalFieldModify_CFM56_5B_CheckPriceCalculation(String accessToken) throws JsonProcessingException {
        Response getRfpEngineResponse = RfpEngineUtil.callGetRfpEngineApi(accessToken, ENGINE_CFM56_5B_QUOTATION_ID);
        RfpEngineResponseDto rfpEngineResponse = utils.convert.jsonToDto(getRfpEngineResponse, RfpEngineResponseDto.class);

        List<RfpItemInputDto> rfpItemInputs = RfpEngineUtil.transformRfpItemsWithModifiedDbii(rfpEngineResponse, 10.0);
        RfpEngineUtil.callPutRfpEngineApi(accessToken, true, rfpItemInputs, ENGINE_CFM56_5B_QUOTATION_ID);

        Response updatedGetRfpEngineResponse = RfpEngineUtil.callGetRfpEngineApi(accessToken, ENGINE_CFM56_5B_QUOTATION_ID);
        RfpEngineResponseDto updatedRfpEngineResponseDto = utils.convert.jsonToDto(updatedGetRfpEngineResponse, RfpEngineResponseDto.class);
        List<Double> actualPrice = RfpEngineUtil.extractCiHoursPriceValuesFromResponse(updatedRfpEngineResponseDto);
        List<Double> sumPriceCostAndCiHours = RfpEngineUtil.calculateSumPriceCostAndCiHours(updatedRfpEngineResponseDto);

        List<Double> actualPriceRounded = RfpEngineUtil.roundDoublesToOneDecimal(actualPrice);
        List<Double> sumCostAndCiHoursRounded = RfpEngineUtil.roundDoublesToOneDecimal(sumPriceCostAndCiHours);

        Assert.assertEquals(actualPriceRounded, sumCostAndCiHoursRounded, DIFFERENCE_BETWEEN_ACTUAL_AND_EXPECTED_PRICE);
    }

    @Test(groups = "workscopeFixedPriceAndNte", dataProvider = "adminAccessTokenProvider")
    public void verifyRfpEngine_GlobalFieldModify_CFM56_5B_CheckPriceCalculationMaxValue(String accessToken) throws JsonProcessingException {
        Response getRfpEngineResponse = RfpEngineUtil.callGetRfpEngineApi(accessToken, ENGINE_CFM56_5B_QUOTATION_ID);
        RfpEngineResponseDto rfpEngineResponse = utils.convert.jsonToDto(getRfpEngineResponse, RfpEngineResponseDto.class);

        List<RfpItemInputDto> rfpItemInputs = RfpEngineUtil.transformRfpItemsWithModifiedDbii(rfpEngineResponse, 99.9);
        RfpEngineUtil.callPutRfpEngineApi(accessToken, true, rfpItemInputs, ENGINE_CFM56_5B_QUOTATION_ID);

        Response updatedGetRfpEngineResponse = RfpEngineUtil.callGetRfpEngineApi(accessToken, ENGINE_CFM56_5B_QUOTATION_ID);
        RfpEngineResponseDto updatedRfpEngineResponseDto = utils.convert.jsonToDto(updatedGetRfpEngineResponse, RfpEngineResponseDto.class);
        List<Double> actualPrice = RfpEngineUtil.extractCiHoursPriceValuesFromResponse(updatedRfpEngineResponseDto);
        List<Double> sumPriceCostAndCiHours = RfpEngineUtil.calculateSumPriceCostAndCiHours(updatedRfpEngineResponseDto);

        List<Double> actualPriceRounded = RfpEngineUtil.roundDoublesToOneDecimal(actualPrice);
        List<Double> sumCostAndCiHoursRounded = RfpEngineUtil.roundDoublesToOneDecimal(sumPriceCostAndCiHours);

        Assert.assertEquals(actualPriceRounded, sumCostAndCiHoursRounded, DIFFERENCE_BETWEEN_ACTUAL_AND_EXPECTED_PRICE);
    }

    @Test(groups = "workscopeFixedPriceAndNte", dataProvider = "viewOnlyAccessTokenProvider")
    public void verifyRfpEngine_GlobalFieldModify_V2500_CheckPriceCalculation_ViewOnlyUser(String accessToken) throws JsonProcessingException {
        Response getRfpEngineResponse = RfpEngineUtil.callGetRfpEngineApi(accessToken, ENGINE_V2500_QUOTATION_ID);
        RfpEngineResponseDto rfpEngineResponse = utils.convert.jsonToDto(getRfpEngineResponse, RfpEngineResponseDto.class);

        List<RfpItemInputDto> rfpItemInputs = RfpEngineUtil.transformRfpItemsWithModifiedDbii(rfpEngineResponse, 99.9);
        Response putResponse = RfpEngineUtil.callPutRfpEngineApi(accessToken, true, rfpItemInputs, ENGINE_V2500_QUOTATION_ID);
        Assert.assertEquals(putResponse.statusCode(), RESPONSE_STATUS_CODE_400, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
    }
}
