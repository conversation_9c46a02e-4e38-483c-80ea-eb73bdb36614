package com.lht.corecalculation.api.rfpengine;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.lht.corecalculation.api.enums.QuotationStatus;
import com.lht.corecalculation.api.pojo.dto.quotation.BeginQuotationProgressDto;
import com.lht.corecalculation.api.request.quotation.GetQuotationsRequest;
import com.lht.corecalculation.api.request.quotation.PutQuotationByIdRequest;
import com.lht.corecalculation.api.request.rfpengine.GetRfpEngineRequest;
import com.lht.corecalculation.api.utils.RfpEngineUtil;
import com.lht.corecalculation.base.BaseCocaApiTest;
import io.restassured.response.Response;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;


import static com.lht.corecalculation.api.constants.GlobalConstants.ADMIN_TOKEN;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_CFM56_5B_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_LEAP_1A_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_LEAP_1B_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_V2500_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ERROR_DETAIL;
import static com.lht.corecalculation.api.constants.GlobalConstants.GENERIC_ERROR_DESCRIPTION_MISMATCH_AS_MESSAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_200;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_401;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_403;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_404;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_USERS_COUNT_MISMATCH_AS_MESSAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.USER_DOESNT_HAVE_REQUIRED_PERMISSIONS;

public class GetRfpEngineApiTests extends BaseCocaApiTest {

    @BeforeClass(alwaysRun = true)
    public void beginAllQuotations() throws JsonProcessingException {
        String adminToken = utils.context.getContextItem(ADMIN_TOKEN);
        GetQuotationsRequest getQuotationsRequest = new GetQuotationsRequest().withAccessToken(adminToken);
        Response response = getQuotationsRequest.callAPI();

        String statuses = response.jsonPath().get("data.quotations.status").toString();
        Integer totalQuotations = response.jsonPath().get("data.totalItems");

        if (statuses.contains(QuotationStatus.ANKA_VALIDATED.name())) {
            for (int i = 1; i <= totalQuotations; i++) {
                BeginQuotationProgressDto beginQuotationProgressDto = new BeginQuotationProgressDto()
                        .withRoutineFixedPrice(true);

                PutQuotationByIdRequest putQuotationByIdRequest =
                        new PutQuotationByIdRequest(String.valueOf(i), utils.convert.dtoToJsonString(beginQuotationProgressDto))
                                .withBearerToken(adminToken);

                putQuotationByIdRequest.callAPI();
            }
        }
    }

    @Test(groups = {"smoke", "workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "adminAccessTokenProvider")
    public void verifyRfpEngine_getEndpointReturn_StatusCode_200(String accessToken) {
        GetRfpEngineRequest getRfpEngineRequest = new GetRfpEngineRequest(ENGINE_LEAP_1B_QUOTATION_ID).withBearerToken(accessToken);

        Response response = getRfpEngineRequest.callAPI();
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = "workscopeFixedPriceAndNte", dataProvider = "viewOnlyAccessTokenProvider")
    public void verifyRfpEngine_getEndpointReturn_StatusCode_200_ForViewOnlyUser(String accessToken) {
        GetRfpEngineRequest getRfpEngineRequest = new GetRfpEngineRequest(ENGINE_LEAP_1B_QUOTATION_ID).withBearerToken(accessToken);

        Response response = getRfpEngineRequest.callAPI();
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = "workscopeFixedPriceAndNte", dataProvider = "adminAccessTokenProvider")
    public void verifyRfpEngine_getEndpointReturn_StatusCode_404_NotExistingQuotationId(String accessToken) throws JsonProcessingException {
        String overMaxQuotation = String.valueOf((RfpEngineUtil.getCurrentMaxQuotations(accessToken) + 1));

        GetRfpEngineRequest getRfpEngineRequest = new GetRfpEngineRequest(overMaxQuotation).withBearerToken(accessToken);

        Response response = getRfpEngineRequest.callAPI();
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_404, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "noRoleAccessTokenProvider")
    public void verifyRfpEngine_getEndpointReturn_StatusCode_401_NotValidAccessToken(String accessToken) {
        GetRfpEngineRequest getRfpEngineRequest = new GetRfpEngineRequest(ENGINE_LEAP_1B_QUOTATION_ID).withBearerToken("accessToken");

        Response response = getRfpEngineRequest.callAPI();
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_401, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
        Assert.assertEquals(response.getBody().jsonPath().get("message").toString(), "Full authentication is required to access this resource", GENERIC_ERROR_DESCRIPTION_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = "workscopeFixedPriceAndNte", dataProvider = "noRoleAccessTokenProvider")
    public void verifyRfpEngine_getEndpointReturn_StatusCode_403_UserDontHaveRequiredPermissions(String accessToken) {
        GetRfpEngineRequest getRfpEngineRequest = new GetRfpEngineRequest(ENGINE_LEAP_1B_QUOTATION_ID).withBearerToken(accessToken);

        Response response = getRfpEngineRequest.callAPI();
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_401, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
        Assert.assertTrue(response.getBody().jsonPath().get(ERROR_DETAIL).toString().contains(USER_DOESNT_HAVE_REQUIRED_PERMISSIONS), GENERIC_ERROR_DESCRIPTION_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = {"smoke", "workscopeFixedPriceAndNte"}, dataProvider = "adminAccessTokenProvider")
    public void verifyRfpEngine_ClustersPartsNames_LEAP_1A_255(String accessToken) throws IOException {
        Response rfpGetResponse = RfpEngineUtil.getRfpEngineResponseWithBearerToken(ENGINE_LEAP_1A_QUOTATION_ID, accessToken);
        Assert.assertEquals(rfpGetResponse.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        List<String> actualRfpClusterNames = RfpEngineUtil.extractRfpClusterNamesFromResponse(rfpGetResponse);
        List<String> expectedRfpClusterNames = Arrays.asList(ClusterOrderAssertionRfp.getClusterOrder_LEAP_1A());

        Assert.assertEquals(actualRfpClusterNames.size(), expectedRfpClusterNames.size(), RESPONSE_USERS_COUNT_MISMATCH_AS_MESSAGE);
        RfpEngineUtil.checkForListMismatch(actualRfpClusterNames, expectedRfpClusterNames);
    }

    @Test(groups = {"smoke", "workscopeFixedPriceAndNte"}, dataProvider = "adminAccessTokenProvider")
    public void verifyRfpEngine_ClustersPartsNames_LEAP_1B(String accessToken) throws IOException {
        Response rfpGetResponse = RfpEngineUtil.getRfpEngineResponseWithBearerToken(ENGINE_LEAP_1B_QUOTATION_ID, accessToken);
        Assert.assertEquals(rfpGetResponse.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        List<String> actualRfpClusterNames = RfpEngineUtil.extractRfpClusterNamesFromResponse(rfpGetResponse);
        List<String> expectedRfpClusterNames = Arrays.asList(ClusterOrderAssertionRfp.getClusterOrder_LEAP_1B());

        Assert.assertEquals(actualRfpClusterNames.size(), expectedRfpClusterNames.size(), RESPONSE_USERS_COUNT_MISMATCH_AS_MESSAGE);
        RfpEngineUtil.checkForListMismatch(actualRfpClusterNames, expectedRfpClusterNames);
    }

    @Test(groups = "workscopeFixedPriceAndNte", dataProvider = "adminAccessTokenProvider")
    public void verifyRfpEngine_ClustersPartsNames_CFM56_5B(String accessToken) throws IOException {
        Response rfpGetResponse = RfpEngineUtil.getRfpEngineResponseWithBearerToken(ENGINE_CFM56_5B_QUOTATION_ID, accessToken);
        Assert.assertEquals(rfpGetResponse.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        List<String> actualRfpClusterNames = RfpEngineUtil.extractRfpClusterNamesFromResponse(rfpGetResponse);
        List<String> expectedRfpClusterNames = Arrays.asList(ClusterOrderAssertionRfp.getClusterOrder_CFM56_5B());

        Assert.assertEquals(actualRfpClusterNames.size(), expectedRfpClusterNames.size(), RESPONSE_USERS_COUNT_MISMATCH_AS_MESSAGE);
        RfpEngineUtil.checkForListMismatch(actualRfpClusterNames, expectedRfpClusterNames);
    }

    @Test(groups = {"smoke", "workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "adminAccessTokenProvider")
    public void verifyRfpEngine_ClustersPartsNames_V2500(String accessToken) throws IOException {
        Response rfpGetResponse = RfpEngineUtil.getRfpEngineResponseWithBearerToken(ENGINE_V2500_QUOTATION_ID, accessToken);
        Assert.assertEquals(rfpGetResponse.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        List<String> actualRfpClusterNames = RfpEngineUtil.extractRfpClusterNamesFromResponse(rfpGetResponse);
        List<String> expectedRfpClusterNames = Arrays.asList(ClusterOrderAssertionRfp.getClusterOrder_V2500());

        Assert.assertEquals(actualRfpClusterNames.size(), expectedRfpClusterNames.size(), RESPONSE_USERS_COUNT_MISMATCH_AS_MESSAGE);
        RfpEngineUtil.checkForListMismatch(actualRfpClusterNames, expectedRfpClusterNames);
    }

    @Test(groups = {"smoke", "workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "adminAccessTokenProvider")
    public void verifyRfpEngine_ClustersPartsAndCost_V2500(String accessToken) throws IOException {
        Response rfpGetResponse = RfpEngineUtil.getRfpEngineResponseWithBearerToken(ENGINE_V2500_QUOTATION_ID, accessToken);
        Assert.assertEquals(rfpGetResponse.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        Map<String, Double> actualRfpClusterNamesAndCost = RfpEngineUtil.extractRfpClusterNameAndCostFromResponse(rfpGetResponse);

        Map<String, Double> expectedRfpClusterNamesAndCost = ClusterOrderAssertionRfp.getClusterOrderNamesAndCost();

        Assert.assertEquals(actualRfpClusterNamesAndCost, expectedRfpClusterNamesAndCost);
    }
}
