package com.lht.corecalculation.api.rfpengine;

import com.lht.corecalculation.api.pojo.dto.rfpengine.RfpEngineItemDto;
import java.util.List;
import java.util.Map;
import org.testng.Assert;


import static com.lht.corecalculation.api.constants.GlobalConstants.CLUSTER_ORDER_MISMATCH_AS_MESSAGE;
import static java.util.Map.entry;

public class ClusterOrderAssertionRfp {

    private static final String[] CLUSTER_ORDER_V2500 = {
            "Incoming inspection",
            "Rem/Inst of QEC",
            "Rem/Inst of ext. Hardware",
            "Rem/Inst of Accessories",
            "Rem/Inst of Core (Separate Core & Fan)",
            "Rem/Inst of LPT",
            "Rem/Inst of Fan",
            "Rem/Inst of LPC",
            "Rem/Inst of IGB / Front Brg. Comp.",
            "Rem/Inst of Diffusor Case",
            "Rem/Inst of Brg. 4 Comp.",
            "Rem/Inst of HPT Nozzle",
            "Rem/Inst of HPT",
            "Rem/Inst of Turbine Exhaust Case",
            "Rem/Inst of External Gearbox Module",
            "Testrun excl. Fuel & Oil",
            "Fuel & Oil MATERIAL for the Testrun",
            "Final Assembly"
    };

    private static final Map<String, Double> CLUSTER_ORDER_NAMES_AND_COST_V2500 = Map.ofEntries(
            entry("Incoming inspection", 8798.179225),
            entry("Rem/Inst of QEC", 19883.8850485),
            entry("Rem/Inst of ext. Hardware", 32553.2631325),
            entry("Rem/Inst of Accessories", 16188.649774),
            entry("Rem/Inst of Core (Separate Core & Fan)", 8094.324887),
            entry("Rem/Inst of LPT", 3871.198859),
            entry("Rem/Inst of Fan", 1759.635845),
            entry("Rem/Inst of LPC", 5102.9439505),
            entry("Rem/Inst of IGB / Front Brg. Comp.", 2639.4537675),
            entry("Rem/Inst of Diffusor Case", 3167.344521),
            entry("Rem/Inst of Brg. 4 Comp.", 0.0),
            entry("Rem/Inst of HPT Nozzle", 2287.5265985),
            entry("Rem/Inst of HPT", 2815.417352),
            entry("Rem/Inst of Turbine Exhaust Case", 2463.490183),
            entry("Rem/Inst of External Gearbox Module", 7566.4341335),
            entry("Testrun excl. Fuel & Oil", 14604.9775135),
            entry("Fuel & Oil MATERIAL for the Testrun", 3854.05),
            entry("Final Assembly", 19883.8850485)
    );

    private static final String[] CLUSTER_ORDER_CFM56_5B = {
            "Incoming inspection",
            "Rem/Inst of QEC",
            "Rem/Inst of ext. Hardware",
            "Rem/Inst of Accessories",
            "Rem/Inst of Core (Separate Core & Fan)",
            "Rem/Inst of LPT",
            "Rem/Inst of LPC",
            "Rem/Inst of Bearing Unit",
            "Rem/Inst of IGB",
            "Rem/Inst of AGB",
            "Rem/Inst of TGB",
            "Rem/Inst of HPC Front Stator",
            "Rem/Inst of HPC Rear Stator",
            "Rem/Inst of Combustor",
            "Rem/Inst of Combustion Chamber",
            "Rem/Inst of HPT Nozzle",
            "Rem/Inst of HPT Rotor",
            "Rem/Inst of HPT Stator/LPT Nozzle Stg. 1",
            "Rem/Inst of Turbinen Frame",
            "Rem/Inst of LPT Bearing Unit",
            "Testrun excl. Fuel & Oil",
            "Fuel & Oil MATERIAL for the Testrun",
            "Final Assembly"
    };

    private static final String[] CLUSTER_ORDER_LEAP_1A = {
            "Incoming inspection",
            "Removal & Installation of tubes & hoses",
            "Removal & Installation of QEC",
            "Removal & Installation of Accessories",
            "Removal & Installation of MM02",
            "Removal & Installation of MM03",
            "Removal & Installation of SM21",
            "Removal & Installation of SM22",
            "Removal & Installation of SM23",
            "Removal & Installation of SM24",
            "Removal & Installation of SM61",
            "Removal & Installation of SM62",
            "Removal & Installation of SM63",
            "Removal & Installation of SM30",
            "Removal & Installation of SM31",
            "Removal & Installation of SM32",
            "Removal & Installation of SM41",
            "Removal & Installation of SM42",
            "Removal & Installation of SM51",
            "Removal & Installation of SM52",
            "Removal & Installation of SM53",
            "Removal & Installation of SM54",
            "Removal & Installation of SM56",
            "Removal & Installation of SM57",
            "Removal & Installation of SM58",
            "Removal & Installation of SM59",
            "Final assembly & inspection",
            "Testrun",
            "Fuel & Oil MATERIAL for the Testrun",
            "Preparation for shipment & storage"
    };

    private static final String[] CLUSTER_ORDER_LEAP_1B = {
            "Incoming inspection",
            "Removal & Installation of tubes & hoses",
            "Removal & Installation of QEC",
            "Removal & Installation of Accessories",
            "Removal & Installation of MM02",
            "Removal & Installation of MM03",
            "Removal & Installation of SM21",
            "Removal & Installation of SM22",
            "Removal & Installation of SM23",
            "Removal & Installation of SM24",
            "Removal & Installation of SM61",
            "Removal & Installation of SM62",
            "Removal & Installation of SM63",
            "Removal & Installation of SM30",
            "Removal & Installation of SM31",
            "Removal & Installation of SM32",
            "Removal & Installation of SM41",
            "Removal & Installation of SM42",
            "Removal & Installation of SM51",
            "Removal & Installation of SM52",
            "Removal & Installation of SM53",
            "Removal & Installation of SM54",
            "Removal & Installation of SM56",
            "Removal & Installation of SM55",
            "Removal & Installation of SM57",
            "Removal & Installation of SM58",
            "Removal & Installation of SM59",
            "Final assembly & inspection",
            "Testrun",
            "Fuel & Oil MATERIAL for the Testrun",
            "Preparation for shipment & storage"
    };

    public static void verifyClusterOrder_V2500(List<RfpEngineItemDto> clusters) {
        Assert.assertEquals(CLUSTER_ORDER_V2500, getRfpEngineItemNames(clusters), CLUSTER_ORDER_MISMATCH_AS_MESSAGE);
    }

    public static void verifyClusterOrder_CFM56_5B(List<RfpEngineItemDto> clusters) {
        Assert.assertEquals(CLUSTER_ORDER_CFM56_5B, getRfpEngineItemNames(clusters), CLUSTER_ORDER_MISMATCH_AS_MESSAGE);
    }

    public static void verifyClusterOrder_LEAP_1B(List<RfpEngineItemDto> clusters) {
        Assert.assertEquals(CLUSTER_ORDER_LEAP_1B, getRfpEngineItemNames(clusters), CLUSTER_ORDER_MISMATCH_AS_MESSAGE);
    }

    public static void verifyClusterOrder_LEAP_1A(List<RfpEngineItemDto> clusters) {
        Assert.assertEquals(CLUSTER_ORDER_LEAP_1A, getRfpEngineItemNames(clusters), CLUSTER_ORDER_MISMATCH_AS_MESSAGE);
    }

    private static String[] getRfpEngineItemNames(List<RfpEngineItemDto> items) {
        return items.stream()
                .map(RfpEngineItemDto::getName)
                .toArray(String[]::new);
    }

    public static String[] getClusterOrder_LEAP_1A() {
        return CLUSTER_ORDER_LEAP_1A.clone();
    }

    public static String[] getClusterOrder_LEAP_1B() {
        return CLUSTER_ORDER_LEAP_1B.clone();
    }

    public static String[] getClusterOrder_V2500() {
        return CLUSTER_ORDER_V2500.clone();
    }

    public static String[] getClusterOrder_CFM56_5B() {
        return CLUSTER_ORDER_CFM56_5B.clone();
    }

    public static Map<String, Double> getClusterOrderNamesAndCost() {
        return CLUSTER_ORDER_NAMES_AND_COST_V2500;
    }
}
