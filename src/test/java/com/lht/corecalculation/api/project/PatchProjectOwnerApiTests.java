package com.lht.corecalculation.api.project;

import com.lht.corecalculation.api.enums.TestUser;
import com.lht.corecalculation.api.pojo.dto.common.ErrorDto;
import com.lht.corecalculation.api.pojo.dto.common.GenericErrorDto;
import com.lht.corecalculation.api.pojo.dto.project.ChangeProjectOwnerDto;
import com.lht.corecalculation.api.request.project.ChangeOwnerRequest;
import com.lht.corecalculation.base.BaseCocaApiTest;
import io.restassured.response.Response;
import java.io.IOException;
import javax.sound.midi.SysexMessage;
import org.testng.Assert;
import org.testng.annotations.Test;
import org.testng.asserts.SoftAssert;


import static com.lht.corecalculation.api.constants.GlobalConstants.ADMIN_TOKEN;
import static com.lht.corecalculation.api.constants.GlobalConstants.BAD_REQUEST_ERROR_MESSAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_V2500_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ERROR_NOT_FOUND;
import static com.lht.corecalculation.api.constants.GlobalConstants.INVALID_REQUEST_CONTENT;
import static com.lht.corecalculation.api.constants.GlobalConstants.OFFER_NUMBER_V2500;
import static com.lht.corecalculation.api.constants.GlobalConstants.READ_ONLY_USER_NOT_ALLOWED_TO_CHANGE_OWNERSHIP;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_200;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_400;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_401;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_404;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.UPDATE_QUERY_ERROR_MESSAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.USER_WITH_ID_NOT_FOUND;
import static com.lht.corecalculation.api.constants.GlobalConstants.VIEW_ONLY_USER_CANNOT_MODIFY_DATA;
import static com.lht.corecalculation.api.enums.TestUser.ADMIN;
import static com.lht.corecalculation.api.enums.TestUser.TEST_USER_1;
import static com.lht.corecalculation.api.enums.TestUser.TEST_USER_2;
import static com.lht.corecalculation.api.enums.TestUser.TEST_USER_3;
import static com.lht.corecalculation.api.enums.TestUser.VIEW_ONLY;

public class PatchProjectOwnerApiTests extends BaseCocaApiTest {

    @Test()
    public void changeProjectOwner_WithAdminToken_ToCalculationUserId_Positive() throws IOException {
        TestUser admin = ADMIN;
        TestUser currentOwner = TEST_USER_3;
        TestUser newOwner = TEST_USER_1;

        long newOwnerId = dbGetUserIdByName(newOwner.getName());

        int entitiesUpdated = dbResetProjectOwner(Long.parseLong(ENGINE_V2500_QUOTATION_ID), dbGetUserIdByName(currentOwner.getName()));
        Assert.assertEquals(entitiesUpdated, 1, UPDATE_QUERY_ERROR_MESSAGE);

        ChangeProjectOwnerDto changeProjectOwnerDto = new ChangeProjectOwnerDto().withNewOwnerId(newOwnerId);

        ChangeOwnerRequest changeOwnerRequest = new ChangeOwnerRequest(
                ENGINE_V2500_QUOTATION_ID,
                utils.convert.dtoToJsonString(changeProjectOwnerDto))
                .withBearerToken(utils.context.getContextItem(admin.getContextKey()));

        Response response = changeOwnerRequest.callAPI();
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
        Assert.assertEquals(dbGetCurrentProjectOwnerId(Long.parseLong(ENGINE_V2500_QUOTATION_ID)), newOwnerId);
    }

    @Test(enabled = false)
    public void changeProjectOwner_WithAdminToken_ToInvalidUserId_Negative() throws IOException {
        TestUser admin = ADMIN;
        TestUser currentOwner = TEST_USER_3;

        int entitiesUpdated = dbResetProjectOwner(Long.parseLong(ENGINE_V2500_QUOTATION_ID), dbGetUserIdByName(currentOwner.getName()));
        Assert.assertEquals(entitiesUpdated, 1, UPDATE_QUERY_ERROR_MESSAGE);

        long invalidUserId = -1;

        ChangeProjectOwnerDto changeProjectOwnerDto = new ChangeProjectOwnerDto().withNewOwnerId(invalidUserId);

        ChangeOwnerRequest changeOwnerRequest = new ChangeOwnerRequest(
                OFFER_NUMBER_V2500,
                utils.convert.dtoToJsonString(changeProjectOwnerDto))
                .withBearerToken(utils.context.getContextItem(admin.getContextKey()));

        Response response = changeOwnerRequest.callAPI();
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_400, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        ErrorDto errorDto = utils.convert.jsonToDto(response, ErrorDto.class);
        var soft = new SoftAssert();
        soft.assertEquals(errorDto.getError().getStatus(), RESPONSE_STATUS_CODE_400);
        soft.assertEquals(errorDto.getError().getType(), BAD_REQUEST_ERROR_MESSAGE);
        soft.assertTrue(errorDto.getError().getDetail().contains(INVALID_REQUEST_CONTENT));
        soft.assertAll();
    }

    @Test()
    public void changeProjectOwner_WithAdminToken_ToNonExistingUserId_Negative() throws IOException {
        TestUser admin = ADMIN;
        TestUser currentOwner = TEST_USER_3;

        int entitiesUpdated = dbResetProjectOwner(Long.parseLong(ENGINE_V2500_QUOTATION_ID), dbGetUserIdByName(currentOwner.getName()));
        Assert.assertEquals(entitiesUpdated, 1, UPDATE_QUERY_ERROR_MESSAGE);

        long invalidUserId = 424242;

        ChangeProjectOwnerDto changeProjectOwnerDto = new ChangeProjectOwnerDto().withNewOwnerId(invalidUserId);

        ChangeOwnerRequest changeOwnerRequest = new ChangeOwnerRequest(
                OFFER_NUMBER_V2500,
                utils.convert.dtoToJsonString(changeProjectOwnerDto))
                .withBearerToken(utils.context.getContextItem(admin.getContextKey()));

        Response response = changeOwnerRequest.callAPI();
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_404, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        ErrorDto errorDto = utils.convert.jsonToDto(response, ErrorDto.class);
        var soft = new SoftAssert();
        soft.assertEquals(errorDto.getError().getStatus(), RESPONSE_STATUS_CODE_404);
        soft.assertEquals(errorDto.getError().getType(), ERROR_NOT_FOUND);
        soft.assertTrue(errorDto.getError().getDetail().contains(String.format("User with id: [%d] is not found", invalidUserId)));
        soft.assertAll();
    }

    @Test()
    public void changeProjectOwner_WithCurrentOwnerToken_ToAnotherCalculationUserId_Positive() throws IOException {
        TestUser currentOwner = TEST_USER_3;
        TestUser newOwner = TEST_USER_2;

        int entitiesUpdated = dbResetProjectOwner(Long.parseLong(ENGINE_V2500_QUOTATION_ID), dbGetUserIdByName(currentOwner.getName()));
        Assert.assertEquals(entitiesUpdated, 1, UPDATE_QUERY_ERROR_MESSAGE);

        long newOwnerId = dbGetUserIdByName(newOwner.getName());

        ChangeProjectOwnerDto changeProjectOwnerDto = new ChangeProjectOwnerDto().withNewOwnerId(newOwnerId);

        String token = utils.context.getContextItem(currentOwner.getContextKey());

        ChangeOwnerRequest changeOwnerRequest = new ChangeOwnerRequest(
                ENGINE_V2500_QUOTATION_ID,
                utils.convert.dtoToJsonString(changeProjectOwnerDto))
                .withBearerToken(token);
        Response response = changeOwnerRequest.callAPI();
        response.body().prettyPrint();

        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
        Assert.assertEquals(dbGetCurrentProjectOwnerId(Long.parseLong(ENGINE_V2500_QUOTATION_ID)), newOwnerId);
    }

    @Test()
    public void changeProjectOwner_WithViewOnlyUserToken_Negative() throws IOException {
        TestUser validNewOwner = TEST_USER_2;
        TestUser viewOnlyUser = VIEW_ONLY;

        long newOwnerId = dbGetUserIdByName(validNewOwner.getName());

        ChangeProjectOwnerDto changeProjectOwnerDto = new ChangeProjectOwnerDto().withNewOwnerId(newOwnerId);

        ChangeOwnerRequest changeOwnerRequest = new ChangeOwnerRequest(
                OFFER_NUMBER_V2500,
                utils.convert.dtoToJsonString(changeProjectOwnerDto))
                .withBearerToken(utils.context.getContextItem(viewOnlyUser.getContextKey()));

        Response response = changeOwnerRequest.callAPI();
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_400, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        ErrorDto errorDto = utils.convert.jsonToDto(response, ErrorDto.class);
        var soft = new SoftAssert();
        soft.assertEquals(errorDto.getError().getStatus(), RESPONSE_STATUS_CODE_400);
        soft.assertEquals(errorDto.getError().getType(), BAD_REQUEST_ERROR_MESSAGE);
        soft.assertTrue(errorDto.getError().getDetail().startsWith(VIEW_ONLY_USER_CANNOT_MODIFY_DATA));
        soft.assertAll();
    }

    @Test()
    public void changeProjectOwner_WithNonOwnerCalculationUserToken_Negative() throws IOException {
        TestUser currentOwner = TEST_USER_3;
        TestUser nonOwnerCalculationUser = TEST_USER_2;
        int invalidOwnerNumber = 555;

        int entitiesUpdated = dbResetProjectOwner(Long.parseLong(ENGINE_V2500_QUOTATION_ID), dbGetUserIdByName(currentOwner.getName()));
        Assert.assertEquals(entitiesUpdated, 1, UPDATE_QUERY_ERROR_MESSAGE);

        String nonOwnerToken = utils.context.getContextItem(nonOwnerCalculationUser.getContextKey());

        ChangeProjectOwnerDto changeProjectOwnerDto = new ChangeProjectOwnerDto().withNewOwnerId(invalidOwnerNumber);

        ChangeOwnerRequest changeOwnerRequest = new ChangeOwnerRequest(
                ENGINE_V2500_QUOTATION_ID,
                utils.convert.dtoToJsonString(changeProjectOwnerDto))
                .withBearerToken(nonOwnerToken);

        Response response = changeOwnerRequest.callAPI();
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_404, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        ErrorDto errorDto = utils.convert.jsonToDto(response, ErrorDto.class);
        var soft = new SoftAssert();
        soft.assertEquals(errorDto.getError().getStatus(), RESPONSE_STATUS_CODE_404);
        soft.assertEquals(errorDto.getError().getType(), ERROR_NOT_FOUND);
        soft.assertTrue(errorDto.getError().getDetail().contains(String.format(USER_WITH_ID_NOT_FOUND, invalidOwnerNumber)));
        soft.assertAll();
    }

    @Test(dataProvider = "expiredAccessTokenProvider")
    public void changeProjectOwner_WithExpiredAccessToken_Negative(String validExpiredToken) throws IOException {
        ChangeProjectOwnerDto changeProjectOwnerDto = new ChangeProjectOwnerDto().withNewOwnerId(11);

        Response response = new ChangeOwnerRequest(
                "2",
                utils.convert.dtoToJsonString(changeProjectOwnerDto))
                .withBearerToken(validExpiredToken).callAPI();
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_401, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

    }

    @Test()
    public void changeProjectOwner_WithoutAccessToken_Negative() throws IOException {
        ChangeProjectOwnerDto changeProjectOwnerDto = new ChangeProjectOwnerDto().withNewOwnerId(11);

        ChangeOwnerRequest changeOwnerRequest = new ChangeOwnerRequest(
                OFFER_NUMBER_V2500,
                utils.convert.dtoToJsonString(changeProjectOwnerDto));

        Response response = changeOwnerRequest.callAPI();
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_401, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        GenericErrorDto errorDto = utils.convert.jsonToDto(response, GenericErrorDto.class);
        var soft = new SoftAssert();
        soft.assertEquals(errorDto.getStatus(), "401");
        soft.assertEquals(errorDto.getError(), "Unauthorized");
        soft.assertEquals(errorDto.getMessage(), "Full authentication is required to access this resource");
        soft.assertAll();
    }

    @Test()
    public void changeProjectOwner_WithAdminToken_ForNonExistingOfferNumber_Negative() throws IOException {
        String nonExistingOwnerID = "1111111";

        ChangeProjectOwnerDto changeProjectOwnerDto = new ChangeProjectOwnerDto().withNewOwnerId(Integer.parseInt(nonExistingOwnerID));

        ChangeOwnerRequest changeOwnerRequest = new ChangeOwnerRequest(
                nonExistingOwnerID,
                utils.convert.dtoToJsonString(changeProjectOwnerDto))
                .withBearerToken(utils.context.getContextItem(ADMIN_TOKEN));

        Response response = changeOwnerRequest.callAPI();
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_404, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        ErrorDto errorDto = utils.convert.jsonToDto(response, ErrorDto.class);
        var soft = new SoftAssert();
        soft.assertEquals(errorDto.getError().getStatus(), RESPONSE_STATUS_CODE_404);
        soft.assertEquals(errorDto.getError().getType(), ERROR_NOT_FOUND);
        soft.assertTrue(errorDto.getError().getDetail().startsWith(String.format(USER_WITH_ID_NOT_FOUND, nonExistingOwnerID)));
        soft.assertAll();
    }

    @Test()
    public void changeProjectOwner_WithAdminToken_ForInvalidOfferNumber_Negative() throws IOException {
        String nonExistingOwnerID = "9090";

        ChangeProjectOwnerDto changeProjectOwnerDto = new ChangeProjectOwnerDto().withNewOwnerId(Integer.parseInt(nonExistingOwnerID));

        ChangeOwnerRequest changeOwnerRequest = new ChangeOwnerRequest(
                nonExistingOwnerID,
                utils.convert.dtoToJsonString(changeProjectOwnerDto))
                .withBearerToken(utils.context.getContextItem(ADMIN_TOKEN));

        Response response = changeOwnerRequest.callAPI();
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_404, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        ErrorDto errorDto = utils.convert.jsonToDto(response, ErrorDto.class);
        var soft = new SoftAssert();
        soft.assertEquals(errorDto.getError().getStatus(), RESPONSE_STATUS_CODE_404);
        soft.assertTrue(errorDto.getError().getDetail().startsWith(String.format(USER_WITH_ID_NOT_FOUND, nonExistingOwnerID)));
        soft.assertAll();
    }
}
