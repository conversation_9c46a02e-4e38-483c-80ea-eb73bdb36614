package com.lht.corecalculation.api.rfpmodule;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.lht.corecalculation.api.utils.RfpModuleUtil;
import com.lht.corecalculation.base.BaseCocaApiTest;
import org.testng.annotations.Test;


import static com.lht.corecalculation.api.constants.GlobalConstants.ADMIN_TOKEN;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_CFM56_5B_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_LEAP_1A_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_LEAP_1B_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_V2500_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.L3;
import static com.lht.corecalculation.api.constants.GlobalConstants.LEVEL_00;
import static com.lht.corecalculation.api.constants.GlobalConstants.LEVEL_03;

public class PutRfpModuleApiTests extends BaseCocaApiTest {

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "validDbiiValues")
    public void verifyRfpModule_ChangeGlobalDbii_CheckPriceCalculation_WithoutCiIncluded_CFM56_5B(
            Double defaultDbiiValue,
            Double targetWorkscopeDbiiValue
    ) throws JsonProcessingException {
        String accessToken = utils.context.getContextItem(ADMIN_TOKEN);
        RfpModuleUtil.verifyRfpModuleChangeGlobalDbii(ENGINE_CFM56_5B_QUOTATION_ID, L3, false, defaultDbiiValue, targetWorkscopeDbiiValue, accessToken);
    }

    @Test(groups = "workscopeFixedPriceAndNte", dataProvider = "validDbiiValues")
    public void verifyRfpModule_ChangeGlobalDbii_CheckPriceCalculation_WithoutCiIncluded_LEAP_1A_Level_03(
            Double defaultDbiiValue,
            Double targetWorkscopeDbiiValue
    ) throws JsonProcessingException {
        String accessToken = utils.context.getContextItem(ADMIN_TOKEN);
        RfpModuleUtil.verifyRfpModuleChangeGlobalDbii(ENGINE_LEAP_1A_QUOTATION_ID, LEVEL_03, false, defaultDbiiValue, targetWorkscopeDbiiValue, accessToken);
    }

    @Test(groups = "workscopeFixedPriceAndNte", dataProvider = "validDbiiValues")
    public void verifyRfpModule_ChangeGlobalDbii_CheckPriceCalculation_WithoutCiIncluded_LEAP_1A_Level_00(
            Double defaultDbiiValue,
            Double targetWorkscopeDbiiValue
    ) throws JsonProcessingException {
        String accessToken = utils.context.getContextItem(ADMIN_TOKEN);
        RfpModuleUtil.verifyRfpModuleChangeGlobalDbii(ENGINE_LEAP_1A_QUOTATION_ID, LEVEL_00, false, defaultDbiiValue, targetWorkscopeDbiiValue, accessToken);
    }

    @Test(groups = "workscopeFixedPriceAndNte", dataProvider = "validDbiiValues")
    public void verifyRfpModule_ChangeGlobalDbii_CheckPriceCalculation_WithoutCiIncluded_LEAP_1B(
            Double defaultDbiiValue,
            Double targetWorkscopeDbiiValue
    ) throws JsonProcessingException {
        String accessToken = utils.context.getContextItem(ADMIN_TOKEN);
        RfpModuleUtil.verifyRfpModuleChangeGlobalDbii(ENGINE_LEAP_1B_QUOTATION_ID, LEVEL_03, false, defaultDbiiValue, targetWorkscopeDbiiValue, accessToken);
    }

    @Test(groups = {"smoke", "workscopeFixedPriceAndNte"}, dataProvider = "validDbiiValues")
    public void verifyRfpModule_ChangeGlobalDbii_CheckPriceCalculation_WithoutCiIncluded_V2500(
            Double defaultDbiiValue,
            Double targetWorkscopeDbiiValue
    ) throws JsonProcessingException {
        String accessToken = utils.context.getContextItem(ADMIN_TOKEN);
        RfpModuleUtil.verifyRfpModuleChangeGlobalDbii(ENGINE_V2500_QUOTATION_ID, L3, false, defaultDbiiValue, targetWorkscopeDbiiValue, accessToken);
    }

    @Test(groups = "workscopeFixedPriceAndNte", dataProvider = "validDbiiValues")
    public void verifyRfpModule_ChangeGlobalDbii_CheckPriceCalculation_WithCiIncluded_CFM56_5B(
            Double defaultDbiiValue,
            Double targetWorkscopeDbiiValue
    ) throws JsonProcessingException {
        String accessToken = utils.context.getContextItem(ADMIN_TOKEN);
        RfpModuleUtil.verifyRfpModuleChangeGlobalDbii(ENGINE_CFM56_5B_QUOTATION_ID, L3, true, defaultDbiiValue, targetWorkscopeDbiiValue, accessToken);
    }

    @Test(groups = "workscopeFixedPriceAndNte", dataProvider = "validDbiiValues")
    public void verifyRfpModule_ChangeGlobalDbii_CheckPriceCalculation_WithCiIncluded_LEAP_1A(
            Double defaultDbiiValue,
            Double targetWorkscopeDbiiValue
    ) throws JsonProcessingException {
        String accessToken = utils.context.getContextItem(ADMIN_TOKEN);
        RfpModuleUtil.verifyRfpModuleChangeGlobalDbii(ENGINE_LEAP_1A_QUOTATION_ID, LEVEL_03, true, defaultDbiiValue, targetWorkscopeDbiiValue, accessToken);
    }

    @Test(groups = "workscopeFixedPriceAndNte", dataProvider = "validDbiiValues")
    public void verifyRfpModule_ChangeGlobalDbii_CheckPriceCalculation_WithCiIncluded_LEAP_1B(
            Double defaultDbiiValue,
            Double targetWorkscopeDbiiValue
    ) throws JsonProcessingException {
        String accessToken = utils.context.getContextItem(ADMIN_TOKEN);
        RfpModuleUtil.verifyRfpModuleChangeGlobalDbii(ENGINE_LEAP_1B_QUOTATION_ID, LEVEL_03, true, defaultDbiiValue, targetWorkscopeDbiiValue, accessToken);
    }

    @Test(groups = "workscopeFixedPriceAndNte", dataProvider = "validDbiiValues")
    public void verifyRfpModule_ChangeGlobalDbii_CheckPriceCalculation_WithCiIncluded_V2500_L3(
            Double defaultDbiiValue,
            Double targetWorkscopeDbiiValue
    ) throws JsonProcessingException {
        String accessToken = utils.context.getContextItem(ADMIN_TOKEN);
        RfpModuleUtil.verifyRfpModuleChangeGlobalDbii(ENGINE_V2500_QUOTATION_ID, L3, true, defaultDbiiValue, targetWorkscopeDbiiValue, accessToken);
    }

    @Test(groups = {"smoke", "workscopeFixedPriceAndNte"}, dataProvider = "validDbiiValues")
    public void verifyRfpModule_ChangeGlobalDbii_CheckPriceCalculation_WithCiIncluded_V2500_VC(
            Double defaultDbiiValue,
            Double targetWorkscopeDbiiValue
    ) throws JsonProcessingException {
        String accessToken = utils.context.getContextItem(ADMIN_TOKEN);
        RfpModuleUtil.verifyRfpModuleChangeGlobalDbii(ENGINE_V2500_QUOTATION_ID, "VC", true, defaultDbiiValue, targetWorkscopeDbiiValue, accessToken);
    }
}