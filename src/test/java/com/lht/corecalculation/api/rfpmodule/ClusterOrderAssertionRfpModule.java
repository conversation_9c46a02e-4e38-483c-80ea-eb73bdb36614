package com.lht.corecalculation.api.rfpmodule;

import static com.lht.corecalculation.api.constants.GlobalConstants.L3;
import static com.lht.corecalculation.api.constants.GlobalConstants.LEVEL_00;
import static com.lht.corecalculation.api.constants.GlobalConstants.LEVEL_01;
import static com.lht.corecalculation.api.constants.GlobalConstants.LEVEL_02;
import static com.lht.corecalculation.api.constants.GlobalConstants.LEVEL_03;

public class ClusterOrderAssertionRfpModule {

    private static final String[] CLUSTER_ORDER_LEAP_1B = {
            "SM21 - Fan and Booster Module",
            "SM22 - No. 1 and No. 2 Brg. Support Module",
            "SM23 - Fan Case Module",
            "SM24 - Fan Frame Module",
            "SM30 - HPC Module",
            "SM31 - HPC Rotor Module",
            "SM32 - HPC Fwd. Stator Module",
            "SM41 - Combustor Diffuser Nozzle Module",
            "SM42 - Combustion Chamber Module",
            "SM51 - Stg. 1 HPT Nozzle Module",
            "SM52 - HPT Rotor Module",
            "SM53 - Stg. 2 HPT Nozzle Module",
            "SM54 - Turbine Center Frame Module",
            "SM55 - No.5 Bearing Support Module",
            "SM56 - LPT Stg. 1 Nozzle Module",
            "SM57 - LPT Shaft Module",
            "SM58 - LPT Rotor and Stator Module",
            "SM59 - Turbine Rear Vane Module",
            "SM61 - IGB and No. 3 Brg. Module",
            "SM62 - TGB Module",
            "SM63 - AGB Module"
    };

    private static final String[] CLUSTER_ORDER_V2500 = {
            "M1 - Fan (ATA 31-00)",
            "M2I - IGB / FBC (ATA 32-10)",
            "M2L - LPC (ATA 32-80)",
            "M2S - Fan Case & Frame (ATA 32-00)",
            "M3R - HPC Rotor (ATA 41-00)",
            "M3S - HPC Stator (ATA 41-00)",
            "M4 - Diffusor Case (ATA 42-10)",
            "M4B - BRG 4 Comp. (ATA 43-11)",
            "M5 - Combustion Chamber (ATA 42/44-00)",
            "M6 - HPT Nozzle (ATA 44-30/50)",
            "M7 - HPT (ATA 45-00)",
            "M8 - LPT (ATA 50-00)",
            "M9 - TEC (ATA 50-50)",
            "M10 - GBX (ATA 60-00)"
    };

    private static final String[] CLUSTER_ORDER_CFM56_5B = {
            "M1 - LPC (ATA 21)",
            "M2B - BRG 2 Unit (ATA 22)",
            "M2I - IGB (ATA 61)",
            "M2S - Fan Frame (ATA 23)",
            "M3R - HPC Rotor (ATA 31)",
            "M3S F - HPC Front Stator (ATA 32)",
            "M3S R - HPC Rear Stator (ATA 33)",
            "M4 - Comb. Case (ATA 41)",
            "M5 - Comb. Chamber (ATA 42)",
            "M6 - HPT Nozzle (ATA 51)",
            "M7 - HPT Rotor (ATA 52)",
            "M8 - HPT Stator (ATA 53)",
            "M9B - BRG 4&5 Unit  (ATA 55)",
            "M9RS - LPT Rotor & Stator (ATA 54)",
            "M9T - Turbine Frame (ATA 56)",
            "AGB (ATA 63)",
            "TGB (ATA 62)"
    };

    private static final String[] CLUSTER_ORDER_LEAP_1A = {
            "SM21 - Fan and Booster Module",
            "SM22 - No. 1 and No. 2 Brg. Support Module",
            "SM23 - Fan Case Module",
            "SM24 - Fan Frame Module",
            "SM30 - HPC Module",
            "SM31 - HPC Rotor Module",
            "SM32 - HPC Fwd. Stator Module",
            "SM41 - Combustor Diffuser Nozzle Module",
            "SM42 - Combustion Chamber Module",
            "SM51 - Stg. 1 HPT Nozzle Module",
            "SM52 - HPT Rotor Module",
            "SM53 - Stg. 2 HPT Nozzle Module",
            "SM54 - Turbine Center Frame Module",
            "SM56 - LPT Stg. 1 Nozzle Module",
            "SM57 - LPT Shaft Module",
            "SM58 - LPT Rotor and Stator Module",
            "SM59 - Turbine Rear Frame Module",
            "SM61 - IGB and No. 3 Brg. Module",
            "SM62 - TGB Module",
            "SM63 - AGB Module"
    };

    private static final String[] WORKSCOPES_LEAP_1A = {
            LEVEL_00,
            LEVEL_01,
            LEVEL_02,
            LEVEL_03
    };

    private static final String[] WORKSCOPES_LEAP_1B = {
            LEVEL_00,
            LEVEL_01,
            LEVEL_02,
            LEVEL_03
    };

    private static final String[] WORKSCOPES_CFM56_5B = {
            "SC",
            "L1",
            "L2",
            L3
    };

    private static final String[] WORKSCOPES_V2500 = {
            "VC",
            "L1",
            "L2.1",
            "L2.2",
            "L2.3",
            "L2.4",
            "L2.5",
            "L2.9",
            L3
    };

    public static String[] getClusterOrder_LEAP_1B() {
        return CLUSTER_ORDER_LEAP_1B.clone();
    }

    public static String[] getClusterOrder_LEAP_1A() {
        return CLUSTER_ORDER_LEAP_1A.clone();
    }

    public static String[] getClusterOrder_V2500() {
        return CLUSTER_ORDER_V2500.clone();
    }

    public static String[] getClusterOrder_CFM56_5B() {
        return CLUSTER_ORDER_CFM56_5B.clone();
    }

    public static String[] getWorkscopes_LEAP_1A() {
        return WORKSCOPES_LEAP_1A.clone();
    }

    public static String[] getWorkscopes_LEAP_1B() {
        return WORKSCOPES_LEAP_1B.clone();
    }

    public static String[] getWorkscopes_LEAP_V2500() {
        return WORKSCOPES_V2500.clone();
    }

    public static String[] getWorkscopes_LEAP_CFM56_5B() {
        return WORKSCOPES_CFM56_5B.clone();
    }
}
