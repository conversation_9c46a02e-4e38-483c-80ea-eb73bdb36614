package com.lht.corecalculation.api.rfpmodule;

import com.lht.corecalculation.api.request.rfpmodule.GetRfpModuleRequest;
import com.lht.corecalculation.api.utils.RfpModuleUtil;
import com.lht.corecalculation.base.BaseCocaApiTest;
import io.restassured.response.Response;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import org.testng.Assert;
import org.testng.annotations.Test;


import static com.lht.corecalculation.api.constants.GlobalConstants.ADMIN_TOKEN;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_CFM56_5B_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_LEAP_1A_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_LEAP_1B_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_V2500_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.NO_ROLE_TOKEN;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_200;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_401;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_403;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_USERS_COUNT_MISMATCH_AS_MESSAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.USER_1_TOKEN;
import static com.lht.corecalculation.api.constants.GlobalConstants.VIEW_ONLY_TOKEN;

public class GetRfpModuleApiTests extends BaseCocaApiTest {

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"})
    public void verifyRfpModule_getEndpointReturn_StatusCode_200() {
        String adminToken = utils.context.getContextItem(ADMIN_TOKEN);
        GetRfpModuleRequest getRfpModuleRequest = new GetRfpModuleRequest(ENGINE_CFM56_5B_QUOTATION_ID).withBearerToken(adminToken);

        Response response = getRfpModuleRequest.callAPI();
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"})
    public void verifyRfpModule_getEndpointReturn_StatusCode_200_ViewOnlyUser() {
        String adminToken = utils.context.getContextItem(VIEW_ONLY_TOKEN);
        GetRfpModuleRequest getRfpModuleRequest = new GetRfpModuleRequest(ENGINE_CFM56_5B_QUOTATION_ID).withBearerToken(adminToken);

        Response response = getRfpModuleRequest.callAPI();
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"})
    public void verifyRfpModule_getEndpointReturn_StatusCode_403_NoOwnerUser() {
        String adminToken = utils.context.getContextItem(USER_1_TOKEN);
        GetRfpModuleRequest getRfpModuleRequest = new GetRfpModuleRequest(ENGINE_CFM56_5B_QUOTATION_ID).withBearerToken(adminToken);

        Response response = getRfpModuleRequest.callAPI();
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"})
    public void verifyRfpModule_getEndpointReturn_StatusCode_403_ForUsersWithoutRole() {
        String adminToken = utils.context.getContextItem(NO_ROLE_TOKEN);
        GetRfpModuleRequest getRfpModuleRequest = new GetRfpModuleRequest(ENGINE_CFM56_5B_QUOTATION_ID).withBearerToken(adminToken);

        Response response = getRfpModuleRequest.callAPI();
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_401, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = {"smoke", "workscopeFixedPriceAndNte", "modularFixedPriceAndNte"})
    public void verifyRfpModule_ClustersNames_LEAP_1A_255() throws IOException {
        String adminToken = utils.context.getContextItem(ADMIN_TOKEN);
        GetRfpModuleRequest getRfpModuleRequest = new GetRfpModuleRequest(ENGINE_LEAP_1A_QUOTATION_ID).withBearerToken(adminToken);

        Response rfpGetModuleResponse = getRfpModuleRequest.callAPI();
        Assert.assertEquals(rfpGetModuleResponse.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        List<String> actualRfpClusterNames = RfpModuleUtil.extractRfpClusterNamesFromResponse(rfpGetModuleResponse);
        List<String> expectedRfpClusterNames = Arrays.asList(ClusterOrderAssertionRfpModule.getClusterOrder_LEAP_1A());

        Assert.assertEquals(actualRfpClusterNames.size(), expectedRfpClusterNames.size(), RESPONSE_USERS_COUNT_MISMATCH_AS_MESSAGE);
        RfpModuleUtil.checkForListMismatch(actualRfpClusterNames, expectedRfpClusterNames);
    }

    @Test(groups = {"smoke", "workscopeFixedPriceAndNte", "modularFixedPriceAndNte"})
    public void verifyRfpModule_ClustersNames_LEAP_1B() throws IOException {
        String adminToken = utils.context.getContextItem(ADMIN_TOKEN);
        GetRfpModuleRequest getRfpModuleRequest = new GetRfpModuleRequest(ENGINE_LEAP_1B_QUOTATION_ID).withBearerToken(adminToken);

        Response rfpGetModuleResponse = getRfpModuleRequest.callAPI();
        Assert.assertEquals(rfpGetModuleResponse.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        List<String> actualRfpClusterNames = RfpModuleUtil.extractRfpClusterNamesFromResponse(rfpGetModuleResponse);
        List<String> expectedRfpClusterNames = Arrays.asList(ClusterOrderAssertionRfpModule.getClusterOrder_LEAP_1B());

        Assert.assertEquals(actualRfpClusterNames.size(), expectedRfpClusterNames.size(), RESPONSE_USERS_COUNT_MISMATCH_AS_MESSAGE);
        RfpModuleUtil.checkForListMismatch(actualRfpClusterNames, expectedRfpClusterNames);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"})
    public void verifyRfpModule_ClustersNames_CFM56_5B() throws IOException {
        String adminToken = utils.context.getContextItem(ADMIN_TOKEN);
        GetRfpModuleRequest getRfpModuleRequest = new GetRfpModuleRequest(ENGINE_CFM56_5B_QUOTATION_ID).withBearerToken(adminToken);

        Response rfpGetModuleResponse = getRfpModuleRequest.callAPI();
        Assert.assertEquals(rfpGetModuleResponse.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        List<String> actualRfpClusterNames = RfpModuleUtil.extractRfpClusterNamesFromResponse(rfpGetModuleResponse);
        List<String> expectedRfpClusterNames = Arrays.asList(ClusterOrderAssertionRfpModule.getClusterOrder_CFM56_5B());

        Assert.assertEquals(actualRfpClusterNames.size(), expectedRfpClusterNames.size(), RESPONSE_USERS_COUNT_MISMATCH_AS_MESSAGE);
        RfpModuleUtil.checkForListMismatch(actualRfpClusterNames, expectedRfpClusterNames);
    }

    @Test(groups = {"smoke", "workscopeFixedPriceAndNte", "modularFixedPriceAndNte"})
    public void verifyRfpModule_ClustersNames_V2500() throws IOException {
        String adminToken = utils.context.getContextItem(ADMIN_TOKEN);
        GetRfpModuleRequest getRfpModuleRequest = new GetRfpModuleRequest(ENGINE_V2500_QUOTATION_ID).withBearerToken(adminToken);

        Response rfpGetModuleResponse = getRfpModuleRequest.callAPI();
        Assert.assertEquals(rfpGetModuleResponse.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        List<String> actualRfpClusterNames = RfpModuleUtil.extractRfpClusterNamesFromResponse(rfpGetModuleResponse);
        List<String> expectedRfpClusterNames = Arrays.asList(ClusterOrderAssertionRfpModule.getClusterOrder_V2500());

        Assert.assertEquals(actualRfpClusterNames.size(), expectedRfpClusterNames.size(), RESPONSE_USERS_COUNT_MISMATCH_AS_MESSAGE);
        RfpModuleUtil.checkForListMismatch(actualRfpClusterNames, expectedRfpClusterNames);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"})
    public void verifyRfpModule_WorkscopesNames_LEAP_1A_255() throws IOException {
        String adminToken = utils.context.getContextItem(ADMIN_TOKEN);
        GetRfpModuleRequest getRfpModuleRequest = new GetRfpModuleRequest(ENGINE_LEAP_1A_QUOTATION_ID).withBearerToken(adminToken);

        Response rfpGetModuleResponse = getRfpModuleRequest.callAPI();
        Assert.assertEquals(rfpGetModuleResponse.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        List<String> actualRfpWorkscopesNames = RfpModuleUtil.extractRfpWorkscopesFromResponse(rfpGetModuleResponse);
        List<String> expectedRfpWorkscopesNames = Arrays.asList(ClusterOrderAssertionRfpModule.getWorkscopes_LEAP_1A());

        Assert.assertEquals(actualRfpWorkscopesNames.size(), expectedRfpWorkscopesNames.size(), RESPONSE_USERS_COUNT_MISMATCH_AS_MESSAGE);
        RfpModuleUtil.checkForListMismatch(actualRfpWorkscopesNames, expectedRfpWorkscopesNames);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"})
    public void verifyRfpModule_WorkscopesNames_LEAP_1B() throws IOException {
        String adminToken = utils.context.getContextItem(ADMIN_TOKEN);
        GetRfpModuleRequest getRfpModuleRequest = new GetRfpModuleRequest(ENGINE_LEAP_1B_QUOTATION_ID).withBearerToken(adminToken);

        Response rfpGetModuleResponse = getRfpModuleRequest.callAPI();
        Assert.assertEquals(rfpGetModuleResponse.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        List<String> actualRfpWorkscopesNames = RfpModuleUtil.extractRfpWorkscopesFromResponse(rfpGetModuleResponse);
        List<String> expectedRfpWorkscopesNames = Arrays.asList(ClusterOrderAssertionRfpModule.getWorkscopes_LEAP_1B());

        Assert.assertEquals(actualRfpWorkscopesNames.size(), expectedRfpWorkscopesNames.size(), RESPONSE_USERS_COUNT_MISMATCH_AS_MESSAGE);
        RfpModuleUtil.checkForListMismatch(actualRfpWorkscopesNames, expectedRfpWorkscopesNames);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"})
    public void verifyRfpModule_WorkscopesNames_CFM56_5B() throws IOException {
        String adminToken = utils.context.getContextItem(ADMIN_TOKEN);
        GetRfpModuleRequest getRfpModuleRequest = new GetRfpModuleRequest(ENGINE_CFM56_5B_QUOTATION_ID).withBearerToken(adminToken);

        Response rfpGetModuleResponse = getRfpModuleRequest.callAPI();
        Assert.assertEquals(rfpGetModuleResponse.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        List<String> actualRfpWorkscopesNames = RfpModuleUtil.extractRfpWorkscopesFromResponse(rfpGetModuleResponse);
        List<String> expectedRfpWorkscopesNames = Arrays.asList(ClusterOrderAssertionRfpModule.getWorkscopes_LEAP_CFM56_5B());

        Assert.assertEquals(actualRfpWorkscopesNames.size(), expectedRfpWorkscopesNames.size(), RESPONSE_USERS_COUNT_MISMATCH_AS_MESSAGE);
        RfpModuleUtil.checkForListMismatch(actualRfpWorkscopesNames, expectedRfpWorkscopesNames);
    }

    @Test(groups = {"smoke", "workscopeFixedPriceAndNte", "modularFixedPriceAndNte"})
    public void verifyRfpModule_WorkscopesNames_V2500() throws IOException {
        String adminToken = utils.context.getContextItem(ADMIN_TOKEN);
        GetRfpModuleRequest getRfpModuleRequest = new GetRfpModuleRequest(ENGINE_V2500_QUOTATION_ID).withBearerToken(adminToken);

        Response rfpGetModuleResponse = getRfpModuleRequest.callAPI();
        Assert.assertEquals(rfpGetModuleResponse.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        List<String> actualRfpWorkscopesNames = RfpModuleUtil.extractRfpWorkscopesFromResponse(rfpGetModuleResponse);
        List<String> expectedRfpWorkscopesNames = Arrays.asList(ClusterOrderAssertionRfpModule.getWorkscopes_LEAP_V2500());

        Assert.assertEquals(actualRfpWorkscopesNames.size(), expectedRfpWorkscopesNames.size(), RESPONSE_USERS_COUNT_MISMATCH_AS_MESSAGE);
        RfpModuleUtil.checkForListMismatch(actualRfpWorkscopesNames, expectedRfpWorkscopesNames);
    }
}