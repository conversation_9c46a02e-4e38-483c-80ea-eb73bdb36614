package com.lht.corecalculation.api.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.lht.corecalculation.api.pojo.dto.wssummary.WorkscopeSummaryItemDto;
import com.lht.corecalculation.api.enums.WorkscopeItemType;
import io.restassured.response.Response;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.IOException;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.lht.corecalculation.api.constants.GlobalConstants.*;
import static com.lht.corecalculation.api.enums.WorkscopeItemType.*;
import static com.lht.corecalculation.api.utils.WorkscopeUtil.extractValueFromResponse;

/**
 * Helper utility to automatically extract actual values from API responses and update DataProvider expected values.
 * This utility scans test classes in wssummarymodularntefixprice, wssummary, and wssummarynteandfixprice packages,
 * extracts actual values from their sharedWorkscopeSummaryResponse variables, and updates corresponding DataProvider files.
 */
@Slf4j
public class DataProviderUpdateHelper {

    private static final String BASE_TEST_PATH = "src/test/java/com/lht/corecalculation/api/";
    private static final String[] TARGET_PACKAGES = {
        "wssummarymodularntefixprice",
        "wssummary", 
        "wssummarynteandfixprice"
    };

    // Value extractors for different metrics
    private static final Map<String, Function<WorkscopeSummaryItemDto, Double>> VALUE_EXTRACTORS = Map.of(
        "ProductionCost", WorkscopeSummaryItemDto::getProductionCost,
        "Revenue", WorkscopeSummaryItemDto::getRevenue,
        "Discount", WorkscopeSummaryItemDto::getDiscount,
        "SurchargesCost", WorkscopeSummaryItemDto::getSurchargesCost,
        "ProdCostInclDiscountsAndSurcharges", WorkscopeSummaryItemDto::getProductionCostAfterDiscountAndSurcharges,
        "Db2", WorkscopeSummaryItemDto::getDb2,
        "Ebit", WorkscopeSummaryItemDto::getEbit,
        "EbitPercentage", WorkscopeSummaryItemDto::getEbitPercentage,
        "EatPercentage", WorkscopeSummaryItemDto::getEatPercentage,
        "NetMargin", WorkscopeSummaryItemDto::getNetMargin
    );

    // Component types used in some DataProviders
    private static final List<String> COMPONENT_TYPES = Arrays.asList(CPR, HPC, HPT_S1B, HPT_LLP, NSV);
    
    // Workscope names used in other DataProviders
    private static final List<String> WORKSCOPE_NAMES = Arrays.asList(
        WS1, WS2, WS01, WS02, WS03, WS03_1, WS04, WS05, WS06, WS07, WS08, WS09, WS10,
        C_N_A, C_N_B, C_N_A_K, C_N_B_K, LEVEL_00, LEVEL_01, LEVEL_02, LEVEL_03, L3
    );

    @Data
    public static class TestClassInfo {
        private String className;
        private String packageName;
        private String dataProviderClassName;
        private Response sharedWorkscopeSummaryResponse;
        private Object testInstance;
    }

    @Data
    public static class DataProviderMethodInfo {
        private String methodName;
        private String dataProviderName;
        private String metricType; // ProductionCost, Revenue, etc.
        private boolean isComponentBased; // true for CPR/HPC/etc., false for WS1/WS2/etc.
        private boolean hasItemTypes; // true if uses INCLUDED_ITEMS/EXCLUDED_ITEMS/TOTAL_ITEMS
        private String filePath;
        private int startLine;
        private int endLine;
        private String currentContent;
    }

    /**
     * Main method to update all DataProviders with current API response values
     */
    public static void updateAllDataProviders() throws Exception {
        log.info("Starting DataProvider update process...");
        
        // Step 1: Discover all test classes with sharedWorkscopeSummaryResponse
        List<TestClassInfo> testClasses = discoverTestClasses();
        log.info("Found {} test classes with sharedWorkscopeSummaryResponse", testClasses.size());
        
        // Step 2: For each test class, find its DataProvider and update it
        for (TestClassInfo testClass : testClasses) {
            updateDataProviderForTestClass(testClass);
        }
        
        log.info("DataProvider update process completed successfully!");
    }

    /**
     * Discover all test classes in target packages that have sharedWorkscopeSummaryResponse field
     */
    private static List<TestClassInfo> discoverTestClasses() throws Exception {
        List<TestClassInfo> testClasses = new ArrayList<>();
        
        for (String packageName : TARGET_PACKAGES) {
            String packagePath = BASE_TEST_PATH + packageName;
            File packageDir = new File(packagePath);
            
            if (!packageDir.exists()) {
                log.warn("Package directory not found: {}", packagePath);
                continue;
            }
            
            File[] javaFiles = packageDir.listFiles((dir, name) -> 
                name.endsWith(".java") && !name.startsWith("DataProviders"));
            
            if (javaFiles != null) {
                for (File javaFile : javaFiles) {
                    TestClassInfo testClassInfo = analyzeTestClass(javaFile, packageName);
                    if (testClassInfo != null) {
                        testClasses.add(testClassInfo);
                    }
                }
            }
        }
        
        return testClasses;
    }

    /**
     * Analyze a test class file to check if it has sharedWorkscopeSummaryResponse and extract info
     */
    private static TestClassInfo analyzeTestClass(File javaFile, String packageName) throws Exception {
        String content = Files.readString(javaFile.toPath());
        
        // Check if file contains sharedWorkscopeSummaryResponse
        if (!content.contains("sharedWorkscopeSummaryResponse")) {
            return null;
        }
        
        String className = javaFile.getName().replace(".java", "");
        String fullClassName = "com.lht.corecalculation.api." + packageName + "." + className;
        
        // Load the class and check for the field
        try {
            Class<?> testClass = Class.forName(fullClassName);
            Field responseField = testClass.getDeclaredField("sharedWorkscopeSummaryResponse");
            responseField.setAccessible(true);
            
            // Find DataProvider class name from extends clause
            Pattern extendsPattern = Pattern.compile("extends\\s+(\\w+)");
            Matcher matcher = extendsPattern.matcher(content);
            String dataProviderClassName = null;
            if (matcher.find()) {
                dataProviderClassName = matcher.group(1);
            }
            
            TestClassInfo info = new TestClassInfo();
            info.setClassName(className);
            info.setPackageName(packageName);
            info.setDataProviderClassName(dataProviderClassName);
            
            return info;
            
        } catch (ClassNotFoundException | NoSuchFieldException e) {
            log.warn("Could not analyze test class {}: {}", className, e.getMessage());
            return null;
        }
    }

    /**
     * Update DataProvider for a specific test class
     */
    private static void updateDataProviderForTestClass(TestClassInfo testClass) throws Exception {
        log.info("Updating DataProvider for test class: {}", testClass.getClassName());
        
        if (testClass.getDataProviderClassName() == null) {
            log.warn("No DataProvider class found for {}", testClass.getClassName());
            return;
        }
        
        // Load test class instance to get the response
        String fullClassName = "com.lht.corecalculation.api." + testClass.getPackageName() + "." + testClass.getClassName();
        Class<?> testClassType = Class.forName(fullClassName);
        Object testInstance = testClassType.getDeclaredConstructor().newInstance();
        
        // Get the sharedWorkscopeSummaryResponse field value
        Field responseField = testClassType.getDeclaredField("sharedWorkscopeSummaryResponse");
        responseField.setAccessible(true);
        Response response = (Response) responseField.get(testInstance);
        
        if (response == null) {
            log.warn("sharedWorkscopeSummaryResponse is null for {}, skipping...", testClass.getClassName());
            return;
        }
        
        testClass.setSharedWorkscopeSummaryResponse(response);
        testClass.setTestInstance(testInstance);
        
        // Find and update DataProvider file
        updateDataProviderFile(testClass);
    }

    /**
     * Update the DataProvider file for a test class
     */
    private static void updateDataProviderFile(TestClassInfo testClass) throws Exception {
        String dataProviderFilePath = BASE_TEST_PATH + testClass.getPackageName() + "/" + 
                                     testClass.getDataProviderClassName() + ".java";
        
        File dataProviderFile = new File(dataProviderFilePath);
        if (!dataProviderFile.exists()) {
            log.warn("DataProvider file not found: {}", dataProviderFilePath);
            return;
        }
        
        String content = Files.readString(dataProviderFile.toPath());
        
        // Find all DataProvider methods in the file
        List<DataProviderMethodInfo> methods = findDataProviderMethods(content, dataProviderFilePath);
        
        // Update each DataProvider method
        for (DataProviderMethodInfo method : methods) {
            updateDataProviderMethod(testClass, method);
        }
    }

    /**
     * Find all DataProvider methods in a file
     */
    private static List<DataProviderMethodInfo> findDataProviderMethods(String content, String filePath) {
        List<DataProviderMethodInfo> methods = new ArrayList<>();
        
        // Pattern to match @DataProvider methods
        Pattern methodPattern = Pattern.compile(
            "@DataProvider\\(name\\s*=\\s*\"([^\"]+)\"\\)\\s*\n\\s*public\\s+Object\\[\\]\\[\\]\\s+(\\w+)\\(\\)",
            Pattern.MULTILINE
        );
        
        Matcher matcher = methodPattern.matcher(content);
        while (matcher.find()) {
            String dataProviderName = matcher.group(1);
            String methodName = matcher.group(2);
            
            DataProviderMethodInfo methodInfo = new DataProviderMethodInfo();
            methodInfo.setDataProviderName(dataProviderName);
            methodInfo.setMethodName(methodName);
            methodInfo.setFilePath(filePath);
            
            // Determine metric type from method name
            methodInfo.setMetricType(extractMetricType(methodName));
            
            // Determine if it's component-based or workscope-based
            methodInfo.setComponentBased(isComponentBasedMethod(content, matcher.start()));
            
            // Determine if it uses item types (INCLUDED_ITEMS, etc.)
            methodInfo.setHasItemTypes(hasItemTypesInMethod(content, matcher.start()));
            
            methods.add(methodInfo);
        }
        
        return methods;
    }

    /**
     * Extract metric type from method name (ProductionCost, Revenue, etc.)
     */
    private static String extractMetricType(String methodName) {
        for (String metric : VALUE_EXTRACTORS.keySet()) {
            if (methodName.toLowerCase().contains(metric.toLowerCase())) {
                return metric;
            }
        }
        return "Unknown";
    }

    /**
     * Check if a DataProvider method is component-based (uses CPR, HPC, etc.)
     */
    private static boolean isComponentBasedMethod(String content, int methodStart) {
        // Look for component types in the method content
        int methodEnd = content.indexOf("}", methodStart);
        if (methodEnd == -1) methodEnd = content.length();
        
        String methodContent = content.substring(methodStart, methodEnd);
        
        for (String component : COMPONENT_TYPES) {
            if (methodContent.contains(component)) {
                return true;
            }
        }
        return false;
    }

    /**
     * Check if a DataProvider method uses item types (INCLUDED_ITEMS, EXCLUDED_ITEMS, TOTAL_ITEMS)
     */
    private static boolean hasItemTypesInMethod(String content, int methodStart) {
        int methodEnd = content.indexOf("}", methodStart);
        if (methodEnd == -1) methodEnd = content.length();
        
        String methodContent = content.substring(methodStart, methodEnd);
        
        // Look for patterns indicating multiple Lists per workscope (included/excluded/total)
        return methodContent.contains("Arrays.asList") && 
               (methodContent.split("Arrays.asList").length > 2); // More than 2 Lists suggests item types
    }

    /**
     * Update a specific DataProvider method with actual values from API response
     */
    private static void updateDataProviderMethod(TestClassInfo testClass, DataProviderMethodInfo method) throws Exception {
        log.info("Updating DataProvider method: {} in {}", method.getMethodName(), testClass.getDataProviderClassName());
        
        Function<WorkscopeSummaryItemDto, Double> valueExtractor = VALUE_EXTRACTORS.get(method.getMetricType());
        if (valueExtractor == null) {
            log.warn("No value extractor found for metric type: {}", method.getMetricType());
            return;
        }
        
        Response response = testClass.getSharedWorkscopeSummaryResponse();
        
        try {
            if (method.isComponentBased()) {
                updateComponentBasedMethod(response, method, valueExtractor);
            } else {
                updateWorkscopeBasedMethod(response, method, valueExtractor);
            }
        } catch (Exception e) {
            log.error("Failed to update method {}: {}", method.getMethodName(), e.getMessage(), e);
        }
    }

    /**
     * Update component-based DataProvider method (CPR, HPC, etc.)
     */
    private static void updateComponentBasedMethod(Response response, DataProviderMethodInfo method, 
                                                  Function<WorkscopeSummaryItemDto, Double> valueExtractor) throws Exception {
        
        StringBuilder newContent = new StringBuilder();
        newContent.append("        return new Object[][] {\n");
        
        for (String component : COMPONENT_TYPES) {
            try {
                List<Double> actualValues = extractValueFromResponse(response, valueExtractor, component);
                if (!actualValues.isEmpty()) {
                    newContent.append("                {").append(component).append(", Arrays.asList(");
                    newContent.append(actualValues.stream()
                        .map(val -> String.format("%.1f", val))
                        .collect(Collectors.joining(", ")));
                    newContent.append(")},\n");
                }
            } catch (JsonProcessingException e) {
                log.warn("Failed to extract values for component {}: {}", component, e.getMessage());
            }
        }
        
        // Remove last comma and newline
        if (newContent.length() > 0 && newContent.toString().endsWith(",\n")) {
            newContent.setLength(newContent.length() - 2);
            newContent.append("\n");
        }
        
        newContent.append("        };\n");
        
        // Update the file using str-replace-editor
        updateMethodInFile(method, newContent.toString());
    }

    /**
     * Update workscope-based DataProvider method (WS1, WS2, etc.)
     */
    private static void updateWorkscopeBasedMethod(Response response, DataProviderMethodInfo method,
                                                  Function<WorkscopeSummaryItemDto, Double> valueExtractor) throws Exception {
        
        StringBuilder newContent = new StringBuilder();
        newContent.append("        return new Object[][] {\n");
        
        for (String workscope : WORKSCOPE_NAMES) {
            try {
                if (method.isHasItemTypes()) {
                    // Extract values for included, excluded, and total items
                    List<Double> includedValues = extractValueFromResponse(response, valueExtractor, workscope, INCLUDED_ITEMS);
                    List<Double> excludedValues = extractValueFromResponse(response, valueExtractor, workscope, EXCLUDED_ITEMS);
                    List<Double> totalValues = extractValueFromResponse(response, valueExtractor, workscope, TOTAL_ITEMS);
                    
                    if (!includedValues.isEmpty() || !excludedValues.isEmpty() || !totalValues.isEmpty()) {
                        newContent.append("                {\n");
                        newContent.append("                        ").append(workscope).append(",\n");
                        
                        if (!includedValues.isEmpty()) {
                            newContent.append("                        Arrays.asList(");
                            newContent.append(includedValues.stream()
                                .map(val -> String.format("%.1f", val))
                                .collect(Collectors.joining(", ")));
                            newContent.append("),\n");
                        }
                        
                        if (!excludedValues.isEmpty()) {
                            newContent.append("                        Arrays.asList(");
                            newContent.append(excludedValues.stream()
                                .map(val -> String.format("%.1f", val))
                                .collect(Collectors.joining(", ")));
                            newContent.append("),\n");
                        }
                        
                        if (!totalValues.isEmpty()) {
                            newContent.append("                        Arrays.asList(");
                            newContent.append(totalValues.stream()
                                .map(val -> String.format("%.1f", val))
                                .collect(Collectors.joining(", ")));
                            newContent.append(")\n");
                        }
                        
                        newContent.append("                },\n");
                    }
                } else {
                    // Simple workscope-based method with single list
                    List<Double> actualValues = extractValueFromResponse(response, valueExtractor, workscope);
                    if (!actualValues.isEmpty()) {
                        newContent.append("                {").append(workscope).append(", Arrays.asList(");
                        newContent.append(actualValues.stream()
                            .map(val -> String.format("%.1f", val))
                            .collect(Collectors.joining(", ")));
                        newContent.append(")},\n");
                    }
                }
            } catch (JsonProcessingException e) {
                log.warn("Failed to extract values for workscope {}: {}", workscope, e.getMessage());
            }
        }
        
        // Remove last comma and newline
        if (newContent.length() > 0 && newContent.toString().endsWith(",\n")) {
            newContent.setLength(newContent.length() - 2);
            newContent.append("\n");
        }
        
        newContent.append("        };\n");
        
        // Update the file using str-replace-editor
        updateMethodInFile(method, newContent.toString());
    }

    /**
     * Update a method in its file using str-replace-editor
     */
    private static void updateMethodInFile(DataProviderMethodInfo method, String newContent) throws Exception {
        String filePath = method.getFilePath();
        String content = Files.readString(Paths.get(filePath));

        // Find the method in the file
        Pattern methodPattern = Pattern.compile(
            "(@DataProvider\\(name\\s*=\\s*\"" + Pattern.quote(method.getDataProviderName()) + "\"\\)\\s*\n" +
            "\\s*public\\s+Object\\[\\]\\[\\]\\s+" + Pattern.quote(method.getMethodName()) + "\\(\\)\\s*\\{\\s*\n)" +
            "(.*?)" +
            "(\\s*\\})",
            Pattern.DOTALL
        );

        Matcher matcher = methodPattern.matcher(content);
        if (matcher.find()) {
            String methodStart = matcher.group(1);
            String methodEnd = matcher.group(3);

            String newMethodContent = methodStart + newContent + methodEnd;
            String oldMethodContent = matcher.group(0);

            // Calculate line numbers for the replacement
            String beforeMethod = content.substring(0, matcher.start());
            int startLine = beforeMethod.split("\n").length;
            int endLine = startLine + oldMethodContent.split("\n").length - 1;

            log.info("Updating method {} in file {} (lines {}-{})",
                    method.getMethodName(), filePath, startLine, endLine);

            // Use ProcessBuilder to call str-replace-editor tool
            updateFileContent(filePath, oldMethodContent, newMethodContent, startLine, endLine);

        } else {
            log.warn("Could not find method {} in file {}", method.getMethodName(), filePath);
        }
    }

    /**
     * Update file content using external str-replace-editor tool
     */
    private static void updateFileContent(String filePath, String oldContent, String newContent,
                                        int startLine, int endLine) throws Exception {
        // Since we can't directly call the str-replace-editor tool from Java,
        // we'll write the content directly to the file
        String fullContent = Files.readString(Paths.get(filePath));
        String updatedContent = fullContent.replace(oldContent, newContent);
        Files.writeString(Paths.get(filePath), updatedContent);

        log.info("Successfully updated file: {}", filePath);
    }

    /**
     * Utility method to run the helper for a specific test class
     */
    public static void updateDataProviderForSpecificTest(String testClassName) throws Exception {
        log.info("Updating DataProvider for specific test: {}", testClassName);

        List<TestClassInfo> allTestClasses = discoverTestClasses();
        TestClassInfo targetTest = allTestClasses.stream()
            .filter(tc -> tc.getClassName().equals(testClassName))
            .findFirst()
            .orElse(null);

        if (targetTest == null) {
            log.error("Test class not found: {}", testClassName);
            return;
        }

        updateDataProviderForTestClass(targetTest);
    }

    /**
     * Utility method to run the helper for a specific package
     */
    public static void updateDataProvidersForPackage(String packageName) throws Exception {
        log.info("Updating DataProviders for package: {}", packageName);

        List<TestClassInfo> allTestClasses = discoverTestClasses();
        List<TestClassInfo> packageTests = allTestClasses.stream()
            .filter(tc -> tc.getPackageName().equals(packageName))
            .collect(Collectors.toList());

        if (packageTests.isEmpty()) {
            log.error("No test classes found in package: {}", packageName);
            return;
        }

        for (TestClassInfo testClass : packageTests) {
            updateDataProviderForTestClass(testClass);
        }
    }

    /**
     * Main entry point for command line usage
     */
    public static void main(String[] args) {
        try {
            if (args.length == 0) {
                updateAllDataProviders();
            } else if (args.length == 2 && "--test".equals(args[0])) {
                updateDataProviderForSpecificTest(args[1]);
            } else if (args.length == 2 && "--package".equals(args[0])) {
                updateDataProvidersForPackage(args[1]);
            } else {
                System.out.println("Usage:");
                System.out.println("  java DataProviderUpdateHelper                    # Update all DataProviders");
                System.out.println("  java DataProviderUpdateHelper --test <className> # Update specific test");
                System.out.println("  java DataProviderUpdateHelper --package <pkg>    # Update specific package");
            }
        } catch (Exception e) {
            log.error("Error updating DataProviders: {}", e.getMessage(), e);
            System.exit(1);
        }
    }
}
