package com.lht.corecalculation.api.utils;

import lombok.extern.slf4j.Slf4j;
import org.testng.annotations.Test;

/**
 * Test to verify that the DataProvider update utilities work with GetWorkscopeSummaryNteLeap1bApiTests
 */
@Slf4j
public class TestNteLeap1bCompatibility {

    @Test
    public void testNteLeap1bCompatibility() {
        log.info("=== Testing Compatibility with GetWorkscopeSummaryNteLeap1bApiTests ===");
        
        String testClassName = "GetWorkscopeSummaryNteLeap1bApiTests";
        String dataProviderClassName = "DataProvidersWorkscopeSummaryNteLeap1B";
        String packageName = "wssummarynteandfixprice";
        
        log.info("Test Class: {}", testClassName);
        log.info("DataProvider Class: {}", dataProviderClassName);
        log.info("Package: {}", packageName);
        
        // Test the discovery process
        DataProviderUpdateRunner.testUpdateProcess(testClassName);
    }

    @Test
    public void testSafeUpdateForNteLeap1b() {
        log.info("=== Testing Safe Update for GetWorkscopeSummaryNteLeap1bApiTests ===");
        
        try {
            String testClassName = "GetWorkscopeSummaryNteLeap1bApiTests";
            String packageName = "wssummarynteandfixprice";
            
            // Find the test class
            Class<?> testClass = Class.forName("com.lht.corecalculation.api." + packageName + "." + testClassName);
            Object testInstance = testClass.getDeclaredConstructor().newInstance();
            
            log.info("✅ Successfully found test class: {}", testClass.getName());
            
            // Check for sharedWorkscopeSummaryResponse field
            try {
                testClass.getDeclaredField("sharedWorkscopeSummaryResponse");
                log.info("✅ Found sharedWorkscopeSummaryResponse field");
            } catch (NoSuchFieldException e) {
                log.error("❌ sharedWorkscopeSummaryResponse field not found");
                return;
            }
            
            // Check for fetchWorkscopeSummary method
            try {
                testClass.getMethod("fetchWorkscopeSummary");
                log.info("✅ Found fetchWorkscopeSummary method");
            } catch (NoSuchMethodException e) {
                log.error("❌ fetchWorkscopeSummary method not found");
                return;
            }
            
            // Get DataProvider class info
            Class<?> superClass = testClass.getSuperclass();
            String dataProviderClassName = superClass.getSimpleName();
            log.info("✅ DataProvider class: {}", dataProviderClassName);
            
            // Check if DataProvider file exists
            String dataProviderFilePath = "src/test/java/com/lht/corecalculation/api/" + packageName + "/" + dataProviderClassName + ".java";
            java.io.File dataProviderFile = new java.io.File(dataProviderFilePath);
            if (dataProviderFile.exists()) {
                log.info("✅ DataProvider file exists: {}", dataProviderFilePath);
            } else {
                log.error("❌ DataProvider file not found: {}", dataProviderFilePath);
                return;
            }
            
            log.info("🎉 GetWorkscopeSummaryNteLeap1bApiTests is COMPATIBLE with the update utilities!");
            
        } catch (Exception e) {
            log.error("❌ Compatibility test failed: {}", e.getMessage(), e);
        }
    }

    @Test
    public void analyzeDataProviderStructure() {
        log.info("=== Analyzing DataProvider Structure for NteLeap1b ===");
        
        try {
            String filePath = "src/test/java/com/lht/corecalculation/api/wssummarynteandfixprice/DataProvidersWorkscopeSummaryNteLeap1B.java";
            String content = java.nio.file.Files.readString(java.nio.file.Paths.get(filePath));
            
            // Count DataProvider methods
            long dataProviderCount = content.lines()
                .filter(line -> line.trim().startsWith("@DataProvider"))
                .count();
            log.info("📊 Found {} DataProvider methods", dataProviderCount);
            
            // Check for workscope names
            boolean hasWorkscopeNames = content.contains("WS03") || content.contains("WS05") || content.contains("WS07");
            log.info("📊 Uses workscope names (WS03, WS05, etc.): {}", hasWorkscopeNames);
            
            // Check for component types
            boolean hasComponentTypes = content.contains("CPR") || content.contains("HPC") || content.contains("HPT_S1B");
            log.info("📊 Uses component types (CPR, HPC, etc.): {}", hasComponentTypes);
            
            // Check for multiple Arrays.asList per entry (indicates included/excluded/total structure)
            long arrayListCount = content.split("Arrays.asList").length - 1;
            long dataProviderEntries = content.split("\\{\\s*WS").length - 1;
            double avgArraysPerEntry = dataProviderEntries > 0 ? (double) arrayListCount / dataProviderEntries : 0;
            log.info("📊 Average Arrays.asList per entry: {:.1f} (indicates item types structure)", avgArraysPerEntry);
            
            if (hasWorkscopeNames && avgArraysPerEntry > 2) {
                log.info("✅ Structure: Workscope-based with item types (INCLUDED_ITEMS, EXCLUDED_ITEMS, TOTAL_ITEMS)");
                log.info("✅ This structure IS SUPPORTED by the utilities!");
            } else if (hasComponentTypes) {
                log.info("✅ Structure: Component-based (CPR, HPC, etc.)");
                log.info("✅ This structure IS SUPPORTED by the utilities!");
            } else {
                log.warn("⚠️ Unknown structure detected");
            }
            
        } catch (Exception e) {
            log.error("❌ Failed to analyze DataProvider structure: {}", e.getMessage());
        }
    }

    @Test
    public void showExpectedUpdateResult() {
        log.info("=== Expected Update Result for NteLeap1b ===");
        
        log.info("Current DataProvider format:");
        log.info("        {");
        log.info("                WS03,");
        log.info("                Arrays.asList(2742781.0, 2951185.0, 3154726.0, 3414707.0),");
        log.info("                Arrays.asList(6161644.0, 6480539.0, 6829804.0, 6959874.0),");
        log.info("                Arrays.asList(8904425.0, 9431725.0, 9984531.0, 1.0374581E7)");
        log.info("        },");
        
        log.info("\nAfter update with exact values:");
        log.info("        {");
        log.info("                WS03,");
        log.info("                Arrays.asList(2742781.123456789, 2951185.987654321, 3154726.555555555, 3414707.111111111),");
        log.info("                Arrays.asList(6161644.123456789, 6480539.987654321, 6829804.555555555, 6959874.111111111),");
        log.info("                Arrays.asList(8904425.123456789, 9431725.987654321, 9984531.555555555, 10374581.111111111)");
        log.info("        },");
        
        log.info("\nThe utilities will:");
        log.info("✅ Extract actual values from sharedWorkscopeSummaryResponse");
        log.info("✅ Use extractValueFromResponse with workscope names (WS03, WS05, etc.)");
        log.info("✅ Handle INCLUDED_ITEMS, EXCLUDED_ITEMS, and TOTAL_ITEMS");
        log.info("✅ Preserve exact values without rounding");
        log.info("✅ Update the DataProvidersWorkscopeSummaryNteLeap1B.java file");
    }

    @Test
    public void demonstrateUsage() {
        log.info("=== How to Use with GetWorkscopeSummaryNteLeap1bApiTests ===");
        
        log.info("1. Using DataProviderUpdateRunner:");
        log.info("   DataProviderUpdateRunner.updateDataProviderForTest(\"GetWorkscopeSummaryNteLeap1bApiTests\");");
        
        log.info("\n2. Using SafeDataProviderUpdater:");
        log.info("   // After running fetchWorkscopeSummary()");
        log.info("   SafeDataProviderUpdater.safeUpdateDataProvider(");
        log.info("       sharedWorkscopeSummaryResponse,");
        log.info("       \"DataProvidersWorkscopeSummaryNteLeap1B\",");
        log.info("       \"wssummarynteandfixprice\"");
        log.info("   );");
        
        log.info("\n3. Using from within the test class:");
        log.info("   @Test");
        log.info("   public void updateMyDataProvider() {");
        log.info("       DataProviderUpdater.updateMyDataProvider(this, sharedWorkscopeSummaryResponse);");
        log.info("   }");
        
        log.info("\n4. Update specific package:");
        log.info("   DataProviderUpdateRunner.updateDataProvidersForPackage(\"wssummarynteandfixprice\");");
    }
}
