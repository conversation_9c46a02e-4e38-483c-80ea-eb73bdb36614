package com.lht.corecalculation.api.utils;

import lombok.extern.slf4j.Slf4j;
import org.testng.annotations.Test;

import java.util.Arrays;
import java.util.List;

/**
 * Example showing the difference between rounded and exact values in DataProviders
 */
@Slf4j
public class ExactValueExample {

    @Test
    public void demonstrateValueFormatting() {
        log.info("=== Demonstrating Value Formatting Differences ===");
        
        // Example values that might come from API
        List<Double> apiValues = Arrays.asList(
            8004342.123456789,
            8454563.987654321,
            8946349.555555555,
            9250083.111111111,
            9509839.999999999,
            9724337.0
        );
        
        log.info("Original API Values:");
        apiValues.forEach(val -> log.info("  {}", val));
        
        log.info("\nRounded to 1 decimal place (OLD way):");
        apiValues.forEach(val -> log.info("  {}", String.format("%.1f", val)));
        
        log.info("\nExact values (NEW way):");
        apiValues.forEach(val -> log.info("  {}", val.toString()));
        
        log.info("\n=== DataProvider Format Comparison ===");
        
        log.info("\nOLD DataProvider format (rounded):");
        log.info("        return new Object[][] {");
        log.info("                {CPR, Arrays.asList({})},", 
                apiValues.stream()
                    .map(val -> String.format("%.1f", val))
                    .reduce((a, b) -> a + ", " + b)
                    .orElse(""));
        log.info("        };");
        
        log.info("\nNEW DataProvider format (exact values):");
        log.info("        return new Object[][] {");
        log.info("                {CPR, Arrays.asList({})},", 
                apiValues.stream()
                    .map(val -> val.toString())
                    .reduce((a, b) -> a + ", " + b)
                    .orElse(""));
        log.info("        };");
    }

    @Test
    public void demonstratePercentageValues() {
        log.info("=== Demonstrating Percentage Value Formatting ===");
        
        // Example percentage values that might come from API
        List<Double> percentageValues = Arrays.asList(
            12.345678901234567,
            17.012345678901234,
            19.198765432109876,
            14.923456789012345,
            16.534567890123456,
            18.045678901234567
        );
        
        log.info("Original API Percentage Values:");
        percentageValues.forEach(val -> log.info("  {}%", val));
        
        log.info("\nRounded to 1 decimal place (OLD way):");
        percentageValues.forEach(val -> log.info("  {}%", String.format("%.1f", val)));
        
        log.info("\nExact percentage values (NEW way):");
        percentageValues.forEach(val -> log.info("  {}%", val.toString()));
        
        log.info("\n=== DataProvider Format Comparison for Percentages ===");
        
        log.info("\nOLD DataProvider format (rounded percentages):");
        log.info("        return new Object[][] {");
        log.info("                {CPR, Arrays.asList({})},", 
                percentageValues.stream()
                    .map(val -> String.format("%.1f", val))
                    .reduce((a, b) -> a + ", " + b)
                    .orElse(""));
        log.info("        };");
        
        log.info("\nNEW DataProvider format (exact percentages):");
        log.info("        return new Object[][] {");
        log.info("                {CPR, Arrays.asList({})},", 
                percentageValues.stream()
                    .map(val -> val.toString())
                    .reduce((a, b) -> a + ", " + b)
                    .orElse(""));
        log.info("        };");
    }

    @Test
    public void demonstrateNegativeValues() {
        log.info("=== Demonstrating Negative Value Formatting ===");
        
        // Example negative values that might come from API
        List<Double> negativeValues = Arrays.asList(
            -105629.123456789,
            -107018.987654321,
            -106189.555555555,
            -116895.111111111,
            -118196.999999999,
            -129844.0
        );
        
        log.info("Original API Negative Values:");
        negativeValues.forEach(val -> log.info("  {}", val));
        
        log.info("\nRounded to 1 decimal place (OLD way):");
        negativeValues.forEach(val -> log.info("  {}", String.format("%.1f", val)));
        
        log.info("\nExact negative values (NEW way):");
        negativeValues.forEach(val -> log.info("  {}", val.toString()));
        
        log.info("\n=== DataProvider Format Comparison for Negative Values ===");
        
        log.info("\nOLD DataProvider format (rounded negatives):");
        log.info("        return new Object[][] {");
        log.info("                {HPC, Arrays.asList({})},", 
                negativeValues.stream()
                    .map(val -> String.format("%.1f", val))
                    .reduce((a, b) -> a + ", " + b)
                    .orElse(""));
        log.info("        };");
        
        log.info("\nNEW DataProvider format (exact negatives):");
        log.info("        return new Object[][] {");
        log.info("                {HPC, Arrays.asList({})},", 
                negativeValues.stream()
                    .map(val -> val.toString())
                    .reduce((a, b) -> a + ", " + b)
                    .orElse(""));
        log.info("        };");
    }

    @Test
    public void demonstrateSmallValues() {
        log.info("=== Demonstrating Small Value Formatting ===");
        
        // Example small values that might come from API
        List<Double> smallValues = Arrays.asList(
            0.33801615123456789,
            5.327393987654321,
            7.989555555555555,
            2.590776211111111,
            4.612649499999999,
            6.145858000000001
        );
        
        log.info("Original API Small Values:");
        smallValues.forEach(val -> log.info("  {}", val));
        
        log.info("\nRounded to 1 decimal place (OLD way):");
        smallValues.forEach(val -> log.info("  {}", String.format("%.1f", val)));
        
        log.info("\nExact small values (NEW way):");
        smallValues.forEach(val -> log.info("  {}", val.toString()));
        
        log.info("\n=== DataProvider Format Comparison for Small Values ===");
        
        log.info("\nOLD DataProvider format (rounded small values):");
        log.info("        return new Object[][] {");
        log.info("                {HPT_S1B, Arrays.asList({})},", 
                smallValues.stream()
                    .map(val -> String.format("%.1f", val))
                    .reduce((a, b) -> a + ", " + b)
                    .orElse(""));
        log.info("        };");
        
        log.info("\nNEW DataProvider format (exact small values):");
        log.info("        return new Object[][] {");
        log.info("                {HPT_S1B, Arrays.asList({})},", 
                smallValues.stream()
                    .map(val -> val.toString())
                    .reduce((a, b) -> a + ", " + b)
                    .orElse(""));
        log.info("        };");
    }

    @Test
    public void showBenefitsOfExactValues() {
        log.info("=== Benefits of Using Exact Values ===");
        
        log.info("1. PRECISION: No loss of precision from API response");
        log.info("   - API returns: 8004342.123456789");
        log.info("   - Rounded:    8004342.1");
        log.info("   - Exact:      8004342.123456789");
        
        log.info("\n2. ACCURACY: Tests will match exact API values");
        log.info("   - Reduces false test failures due to rounding differences");
        log.info("   - More reliable test results");
        
        log.info("\n3. CONSISTENCY: Same precision as API responses");
        log.info("   - DataProvider values match exactly what API returns");
        log.info("   - No discrepancies between expected and actual");
        
        log.info("\n4. DEBUGGING: Easier to identify real API changes");
        log.info("   - When tests fail, you know it's a real API change");
        log.info("   - Not a rounding artifact");
        
        log.info("\n5. MAINTENANCE: Less frequent DataProvider updates needed");
        log.info("   - Exact values are more stable");
        log.info("   - Fewer false positives requiring updates");
    }
}
