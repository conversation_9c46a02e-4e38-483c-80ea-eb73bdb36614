package com.lht.corecalculation.api.utils;

import io.restassured.response.Response;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.List;

/**
 * Runner utility to execute DataProvider updates for test classes.
 * This class can be used to run the fetchWorkscopeSummary method of test classes
 * and then update their DataProviders with the actual API response values.
 */
@Slf4j
public class DataProviderUpdateRunner {

    private static final List<String> TARGET_PACKAGES = Arrays.asList(
        "com.lht.corecalculation.api.wssummarymodularntefixprice",
        "com.lht.corecalculation.api.wssummary",
        "com.lht.corecalculation.api.wssummarynteandfixprice"
    );

    /**
     * Update DataProvider for a specific test class by running its fetchWorkscopeSummary method
     * and extracting the actual values from the API response.
     */
    public static void updateDataProviderForTest(String testClassName) {
        try {
            log.info("Updating DataProvider for test class: {}", testClassName);
            
            // Find the test class in target packages
            Class<?> testClass = findTestClass(testClassName);
            if (testClass == null) {
                log.error("Test class not found: {}", testClassName);
                return;
            }
            
            // Create instance of test class
            Object testInstance = testClass.getDeclaredConstructor().newInstance();
            
            // Run fetchWorkscopeSummary method
            Method fetchMethod = testClass.getMethod("fetchWorkscopeSummary");
            fetchMethod.invoke(testInstance);
            
            // Get the sharedWorkscopeSummaryResponse field
            Field responseField = testClass.getDeclaredField("sharedWorkscopeSummaryResponse");
            responseField.setAccessible(true);
            Response response = (Response) responseField.get(testInstance);
            
            if (response == null) {
                log.error("sharedWorkscopeSummaryResponse is null for {}", testClassName);
                return;
            }
            
            // Update the DataProvider using the response
            DataProviderUpdater.updateMyDataProvider(testInstance, response);
            
            log.info("Successfully updated DataProvider for test class: {}", testClassName);
            
        } catch (Exception e) {
            log.error("Failed to update DataProvider for test class {}: {}", testClassName, e.getMessage(), e);
        }
    }

    /**
     * Update DataProviders for all test classes in a specific package
     */
    public static void updateDataProvidersForPackage(String packageSuffix) {
        try {
            log.info("Updating DataProviders for package: {}", packageSuffix);
            
            String fullPackageName = "com.lht.corecalculation.api." + packageSuffix;
            
            // Get all test classes in the package
            List<String> testClasses = getTestClassesInPackage(fullPackageName);
            
            for (String testClassName : testClasses) {
                updateDataProviderForTest(testClassName);
            }
            
            log.info("Successfully updated DataProviders for package: {}", packageSuffix);
            
        } catch (Exception e) {
            log.error("Failed to update DataProviders for package {}: {}", packageSuffix, e.getMessage(), e);
        }
    }

    /**
     * Update DataProviders for all test classes in all target packages
     */
    public static void updateAllDataProviders() {
        log.info("Starting update of all DataProviders...");
        
        for (String packageName : TARGET_PACKAGES) {
            String packageSuffix = packageName.substring(packageName.lastIndexOf('.') + 1);
            updateDataProvidersForPackage(packageSuffix);
        }
        
        log.info("Completed update of all DataProviders!");
    }

    /**
     * Find a test class by name in the target packages
     */
    private static Class<?> findTestClass(String testClassName) {
        for (String packageName : TARGET_PACKAGES) {
            try {
                String fullClassName = packageName + "." + testClassName;
                Class<?> testClass = Class.forName(fullClassName);
                
                // Check if it has sharedWorkscopeSummaryResponse field
                try {
                    testClass.getDeclaredField("sharedWorkscopeSummaryResponse");
                    return testClass;
                } catch (NoSuchFieldException e) {
                    // This class doesn't have the field, continue searching
                }
                
            } catch (ClassNotFoundException e) {
                // Class not found in this package, continue searching
            }
        }
        return null;
    }

    /**
     * Get all test class names in a package that have sharedWorkscopeSummaryResponse field
     */
    private static List<String> getTestClassesInPackage(String packageName) {
        // This is a simplified implementation - in a real scenario you might want to
        // scan the classpath or use reflection utilities to find all classes
        
        String packageSuffix = packageName.substring(packageName.lastIndexOf('.') + 1);
        
        switch (packageSuffix) {
            case "wssummarymodularntefixprice":
                return Arrays.asList(
                    "GetWorkscopeModularSummaryNteCfm567bApiTests",
                    "GetModularWorkscopeSummaryNteLeap1bApiTests",
                    "GetWorkscopeSummaryModularNteCfm565bPmaApiTests",
                    "GetWsModularFixedAndNteLeap1aApiTests",
                    "GetWSModularFixedPriceV2500ApiTest"
                );
            case "wssummary":
                return Arrays.asList(
                    "GetWorkscopeSummaryEngineLeap1ATests",
                    "GetWorkscopeSummaryEngineLeap1BTests",
                    "GetWorkscopeSummaryEngineV2500Tests",
                    "GetWorkscopeSummaryEngineCfm567BTests",
                    "GetWorkscopeSummaryEngineCfm565BTests"
                );
            case "wssummarynteandfixprice":
                return Arrays.asList(
                    "GetWorkscopeSummaryWsFixedAndNteLeap1aApiTests",
                    "GetWorkscopeSummaryNteCfm567bApiTests",
                    "GetWorkscopeSummaryNteLeap1bApiTests",
                    "GetWorkscopeSummaryNteCfm565bPmaApiTests",
                    "GetWorkscopeSummaryWsFixedPriceV2500ApiTests"
                );
            default:
                return Arrays.asList();
        }
    }

    /**
     * Main method for command line usage
     */
    public static void main(String[] args) {
        try {
            if (args.length == 0) {
                updateAllDataProviders();
            } else if (args.length == 2 && "--test".equals(args[0])) {
                updateDataProviderForTest(args[1]);
            } else if (args.length == 2 && "--package".equals(args[0])) {
                updateDataProvidersForPackage(args[1]);
            } else {
                System.out.println("Usage:");
                System.out.println("  java DataProviderUpdateRunner                           # Update all DataProviders");
                System.out.println("  java DataProviderUpdateRunner --test <className>       # Update specific test");
                System.out.println("  java DataProviderUpdateRunner --package <packageName>  # Update specific package");
                System.out.println("");
                System.out.println("Examples:");
                System.out.println("  java DataProviderUpdateRunner --test GetWorkscopeSummaryEngineLeap1ATests");
                System.out.println("  java DataProviderUpdateRunner --package wssummary");
            }
        } catch (Exception e) {
            log.error("Error running DataProvider update: {}", e.getMessage(), e);
            System.exit(1);
        }
    }

    /**
     * Utility method to test the update process for a single test class without actually updating files
     */
    public static void testUpdateProcess(String testClassName) {
        try {
            log.info("Testing update process for test class: {}", testClassName);
            
            Class<?> testClass = findTestClass(testClassName);
            if (testClass == null) {
                log.error("Test class not found: {}", testClassName);
                return;
            }
            
            Object testInstance = testClass.getDeclaredConstructor().newInstance();
            
            // Run fetchWorkscopeSummary method
            Method fetchMethod = testClass.getMethod("fetchWorkscopeSummary");
            fetchMethod.invoke(testInstance);
            
            // Get the sharedWorkscopeSummaryResponse field
            Field responseField = testClass.getDeclaredField("sharedWorkscopeSummaryResponse");
            responseField.setAccessible(true);
            Response response = (Response) responseField.get(testInstance);
            
            if (response == null) {
                log.error("sharedWorkscopeSummaryResponse is null for {}", testClassName);
                return;
            }
            
            log.info("Successfully fetched API response for test class: {}", testClassName);
            log.info("Response status code: {}", response.getStatusCode());
            log.info("Response body length: {}", response.getBody().asString().length());
            
            // Get DataProvider class info
            Class<?> superClass = testClass.getSuperclass();
            String dataProviderClassName = superClass.getSimpleName();
            log.info("DataProvider class: {}", dataProviderClassName);
            
        } catch (Exception e) {
            log.error("Failed to test update process for test class {}: {}", testClassName, e.getMessage(), e);
        }
    }
}
