package com.lht.corecalculation.api.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.lht.corecalculation.api.pojo.dto.wssummary.WorkscopeSummaryItemDto;
import io.restassured.response.Response;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.lht.corecalculation.api.constants.GlobalConstants.*;
import static com.lht.corecalculation.api.enums.WorkscopeItemType.*;
import static com.lht.corecalculation.api.utils.WorkscopeUtil.extractValueFromResponse;

/**
 * Safe DataProvider updater that creates backup files and provides rollback functionality.
 * This utility is designed to safely update DataProvider files with actual API response values
 * while providing the ability to rollback changes if something goes wrong.
 */
@Slf4j
public class SafeDataProviderUpdater {

    // Value extractors for different metrics
    private static final Map<String, Function<WorkscopeSummaryItemDto, Double>> VALUE_EXTRACTORS = Map.of(
        "ProductionCost", WorkscopeSummaryItemDto::getProductionCost,
        "Revenue", WorkscopeSummaryItemDto::getRevenue,
        "Discount", WorkscopeSummaryItemDto::getDiscount,
        "SurchargesCost", WorkscopeSummaryItemDto::getSurchargesCost,
        "ProdCostInclDiscountsAndSurcharges", WorkscopeSummaryItemDto::getProductionCostAfterDiscountAndSurcharges,
        "Db2", WorkscopeSummaryItemDto::getDb2,
        "Ebit", WorkscopeSummaryItemDto::getEbit,
        "EbitPercentage", WorkscopeSummaryItemDto::getEbitPercentage,
        "EatPercentage", WorkscopeSummaryItemDto::getEatPercentage,
        "NetMargin", WorkscopeSummaryItemDto::getNetMargin
    );

    // Component types used in some DataProviders
    private static final List<String> COMPONENT_TYPES = Arrays.asList(CPR, HPC, HPT_S1B, HPT_LLP, NSV);

    /**
     * Safely update a DataProvider file with backup and rollback capability
     */
    public static boolean safeUpdateDataProvider(Response response, String dataProviderClassName, String packageName) {
        String filePath = "src/test/java/com/lht/corecalculation/api/" + packageName + "/" + dataProviderClassName + ".java";
        String backupPath = filePath + ".backup." + System.currentTimeMillis();
        
        try {
            // Step 1: Create backup
            String originalContent = Files.readString(Paths.get(filePath));
            Files.writeString(Paths.get(backupPath), originalContent);
            log.info("Created backup: {}", backupPath);
            
            // Step 2: Generate new content
            String newContent = generateUpdatedContent(originalContent, response);
            
            // Step 3: Validate new content
            if (!validateGeneratedContent(newContent, originalContent)) {
                log.error("Generated content validation failed for {}", dataProviderClassName);
                Files.deleteIfExists(Paths.get(backupPath));
                return false;
            }
            
            // Step 4: Write new content
            Files.writeString(Paths.get(filePath), newContent);
            log.info("Successfully updated DataProvider: {}", dataProviderClassName);
            
            // Step 5: Keep backup for manual verification
            log.info("Backup file created at: {} (delete manually after verification)", backupPath);
            
            return true;
            
        } catch (Exception e) {
            log.error("Failed to update DataProvider {}: {}", dataProviderClassName, e.getMessage(), e);
            
            // Attempt to restore from backup if it exists
            try {
                if (Files.exists(Paths.get(backupPath))) {
                    String backupContent = Files.readString(Paths.get(backupPath));
                    Files.writeString(Paths.get(filePath), backupContent);
                    Files.deleteIfExists(Paths.get(backupPath));
                    log.info("Restored original content from backup");
                }
            } catch (IOException restoreException) {
                log.error("Failed to restore from backup: {}", restoreException.getMessage());
            }
            
            return false;
        }
    }

    /**
     * Generate updated content for a DataProvider file
     */
    private static String generateUpdatedContent(String originalContent, Response response) throws JsonProcessingException {
        StringBuilder result = new StringBuilder();
        String[] lines = originalContent.split("\n");
        
        boolean inDataProviderMethod = false;
        boolean inReturnStatement = false;
        int braceCount = 0;
        String currentMethodName = "";
        StringBuilder methodContent = new StringBuilder();
        
        for (int i = 0; i < lines.length; i++) {
            String line = lines[i];
            
            // Check if we're starting a DataProvider method
            if (line.trim().startsWith("@DataProvider")) {
                inDataProviderMethod = true;
                result.append(line).append("\n");
                continue;
            }
            
            // Check if we're in the method signature line
            if (inDataProviderMethod && line.contains("public Object[][] ") && line.contains("()")) {
                currentMethodName = extractMethodName(line);
                result.append(line).append("\n");
                continue;
            }
            
            // Check if we're starting the return statement
            if (inDataProviderMethod && line.trim().startsWith("return new Object[][]")) {
                inReturnStatement = true;
                braceCount = 0;
                methodContent = new StringBuilder();
                
                // Generate new method content
                try {
                    String newMethodBody = generateNewMethodBodyForMethod(response, currentMethodName);
                    result.append(newMethodBody);
                    
                    // Skip until we find the end of this method
                    i = skipToMethodEnd(lines, i);
                    inDataProviderMethod = false;
                    inReturnStatement = false;
                    continue;
                } catch (Exception e) {
                    log.warn("Failed to generate new content for method {}, keeping original", currentMethodName);
                    // Fall through to keep original content
                }
            }
            
            // If we're not in a DataProvider method or failed to update, keep original line
            if (!inDataProviderMethod) {
                result.append(line).append("\n");
            } else {
                // We're in a DataProvider method but not updating it, keep original
                result.append(line).append("\n");
                if (line.trim().equals("}")) {
                    inDataProviderMethod = false;
                }
            }
        }
        
        return result.toString();
    }

    /**
     * Extract method name from method signature line
     */
    private static String extractMethodName(String line) {
        int start = line.indexOf("Object[][] ") + "Object[][] ".length();
        int end = line.indexOf("(");
        return line.substring(start, end).trim();
    }

    /**
     * Skip lines until we find the end of the current method
     */
    private static int skipToMethodEnd(String[] lines, int startIndex) {
        int braceCount = 0;
        boolean foundFirstBrace = false;
        
        for (int i = startIndex; i < lines.length; i++) {
            String line = lines[i];
            
            for (char c : line.toCharArray()) {
                if (c == '{') {
                    braceCount++;
                    foundFirstBrace = true;
                } else if (c == '}') {
                    braceCount--;
                    if (foundFirstBrace && braceCount == 0) {
                        return i;
                    }
                }
            }
        }
        
        return lines.length - 1;
    }

    /**
     * Generate new method body for a specific method
     */
    private static String generateNewMethodBodyForMethod(Response response, String methodName) throws JsonProcessingException {
        // Determine metric type from method name
        String metricType = extractMetricType(methodName);
        Function<WorkscopeSummaryItemDto, Double> valueExtractor = VALUE_EXTRACTORS.get(metricType);
        
        if (valueExtractor == null) {
            throw new IllegalArgumentException("No value extractor found for metric type: " + metricType);
        }
        
        StringBuilder content = new StringBuilder();
        content.append("        return new Object[][] {\n");
        
        // Generate content for component types
        boolean hasContent = false;
        for (String component : COMPONENT_TYPES) {
            try {
                List<Double> actualValues = extractValueFromResponse(response, valueExtractor, component);
                if (!actualValues.isEmpty()) {
                    content.append("                {").append(component).append(", Arrays.asList(");
                    content.append(actualValues.stream()
                        .map(val -> String.format("%.1f", val))
                        .collect(Collectors.joining(", ")));
                    content.append(")},\n");
                    hasContent = true;
                }
            } catch (Exception e) {
                log.warn("Failed to extract values for component {}: {}", component, e.getMessage());
            }
        }
        
        // Remove last comma if we have content
        if (hasContent) {
            String contentStr = content.toString();
            if (contentStr.endsWith(",\n")) {
                content.setLength(content.length() - 2);
                content.append("\n");
            }
        }
        
        content.append("        };\n");
        content.append("    }\n");
        
        return content.toString();
    }

    /**
     * Extract metric type from method name
     */
    private static String extractMetricType(String methodName) {
        String lowerMethodName = methodName.toLowerCase();
        
        // Check for specific patterns
        if (lowerMethodName.contains("productioncost") && lowerMethodName.contains("discount")) {
            return "ProdCostInclDiscountsAndSurcharges";
        }
        
        for (String metric : VALUE_EXTRACTORS.keySet()) {
            if (lowerMethodName.contains(metric.toLowerCase())) {
                return metric;
            }
        }
        
        // Fallback patterns
        if (lowerMethodName.contains("production")) return "ProductionCost";
        if (lowerMethodName.contains("revenue")) return "Revenue";
        if (lowerMethodName.contains("discount")) return "Discount";
        if (lowerMethodName.contains("surcharge")) return "SurchargesCost";
        if (lowerMethodName.contains("db2")) return "Db2";
        if (lowerMethodName.contains("ebit")) return "Ebit";
        if (lowerMethodName.contains("margin")) return "NetMargin";
        
        return "ProductionCost"; // Default fallback
    }

    /**
     * Validate that the generated content is reasonable
     */
    private static boolean validateGeneratedContent(String newContent, String originalContent) {
        // Basic validation checks
        if (newContent == null || newContent.trim().isEmpty()) {
            log.error("Generated content is empty");
            return false;
        }
        
        // Check that we still have the same number of @DataProvider annotations
        long originalDataProviders = originalContent.lines()
            .filter(line -> line.trim().startsWith("@DataProvider"))
            .count();
        
        long newDataProviders = newContent.lines()
            .filter(line -> line.trim().startsWith("@DataProvider"))
            .count();
        
        if (originalDataProviders != newDataProviders) {
            log.error("DataProvider count mismatch: original={}, new={}", originalDataProviders, newDataProviders);
            return false;
        }
        
        // Check that we still have proper class structure
        if (!newContent.contains("public class ") || !newContent.contains("extends BaseCocaApiTest")) {
            log.error("Class structure validation failed");
            return false;
        }
        
        // Check for basic syntax issues
        long openBraces = newContent.chars().filter(ch -> ch == '{').count();
        long closeBraces = newContent.chars().filter(ch -> ch == '}').count();
        
        if (openBraces != closeBraces) {
            log.error("Brace mismatch: open={}, close={}", openBraces, closeBraces);
            return false;
        }
        
        return true;
    }

    /**
     * Rollback a DataProvider file from its backup
     */
    public static boolean rollbackDataProvider(String dataProviderClassName, String packageName) {
        String filePath = "src/test/java/com/lht/corecalculation/api/" + packageName + "/" + dataProviderClassName + ".java";
        
        try {
            // Find the most recent backup file
            String backupPath = findMostRecentBackup(filePath);
            if (backupPath == null) {
                log.error("No backup file found for {}", dataProviderClassName);
                return false;
            }
            
            // Restore from backup
            String backupContent = Files.readString(Paths.get(backupPath));
            Files.writeString(Paths.get(filePath), backupContent);
            
            log.info("Successfully rolled back {} from backup: {}", dataProviderClassName, backupPath);
            return true;
            
        } catch (Exception e) {
            log.error("Failed to rollback {}: {}", dataProviderClassName, e.getMessage(), e);
            return false;
        }
    }

    /**
     * Find the most recent backup file for a given file path
     */
    private static String findMostRecentBackup(String filePath) throws IOException {
        String backupPrefix = filePath + ".backup.";
        
        return Files.list(Paths.get(filePath).getParent())
            .map(path -> path.toString())
            .filter(path -> path.startsWith(backupPrefix))
            .max(Comparator.naturalOrder())
            .orElse(null);
    }

    /**
     * Clean up old backup files (keep only the most recent 3)
     */
    public static void cleanupBackups(String dataProviderClassName, String packageName) {
        String filePath = "src/test/java/com/lht/corecalculation/api/" + packageName + "/" + dataProviderClassName + ".java";
        String backupPrefix = filePath + ".backup.";
        
        try {
            List<String> backupFiles = Files.list(Paths.get(filePath).getParent())
                .map(path -> path.toString())
                .filter(path -> path.startsWith(backupPrefix))
                .sorted(Comparator.reverseOrder())
                .collect(Collectors.toList());
            
            // Keep only the 3 most recent backups
            for (int i = 3; i < backupFiles.size(); i++) {
                Files.deleteIfExists(Paths.get(backupFiles.get(i)));
                log.info("Deleted old backup: {}", backupFiles.get(i));
            }
            
        } catch (Exception e) {
            log.warn("Failed to cleanup backups for {}: {}", dataProviderClassName, e.getMessage());
        }
    }
}
