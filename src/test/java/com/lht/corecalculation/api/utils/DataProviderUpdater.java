package com.lht.corecalculation.api.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.lht.corecalculation.api.pojo.dto.wssummary.WorkscopeSummaryItemDto;
import com.lht.corecalculation.api.enums.WorkscopeItemType;
import io.restassured.response.Response;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.lht.corecalculation.api.constants.GlobalConstants.*;
import static com.lht.corecalculation.api.enums.WorkscopeItemType.*;
import static com.lht.corecalculation.api.utils.WorkscopeUtil.extractValueFromResponse;

/**
 * Simplified DataProvider updater that can be called from test classes to update their DataProviders
 * with actual values from API responses.
 */
@Slf4j
public class DataProviderUpdater {

    // Value extractors for different metrics
    private static final Map<String, Function<WorkscopeSummaryItemDto, Double>> VALUE_EXTRACTORS = Map.of(
        "ProductionCost", WorkscopeSummaryItemDto::getProductionCost,
        "Revenue", WorkscopeSummaryItemDto::getRevenue,
        "Discount", WorkscopeSummaryItemDto::getDiscount,
        "SurchargesCost", WorkscopeSummaryItemDto::getSurchargesCost,
        "ProdCostInclDiscountsAndSurcharges", WorkscopeSummaryItemDto::getProductionCostAfterDiscountAndSurcharges,
        "Db2", WorkscopeSummaryItemDto::getDb2,
        "Ebit", WorkscopeSummaryItemDto::getEbit,
        "EbitPercentage", WorkscopeSummaryItemDto::getEbitPercentage,
        "EatPercentage", WorkscopeSummaryItemDto::getEatPercentage,
        "NetMargin", WorkscopeSummaryItemDto::getNetMargin
    );

    // Component types used in some DataProviders
    private static final List<String> COMPONENT_TYPES = Arrays.asList(CPR, HPC, HPT_S1B, HPT_LLP, NSV);
    
    // Workscope names used in other DataProviders
    private static final List<String> WORKSCOPE_NAMES = Arrays.asList(
        WS1, WS2, WS01, WS02, WS03, WS03_1, WS04, WS05, WS06, WS07, WS08, WS09, WS10,
        C_N_A, C_N_B, C_N_A_K, C_N_B_K, LEVEL_00, LEVEL_01, LEVEL_02, LEVEL_03, L3
    );

    /**
     * Update a specific DataProvider file with actual values from API response
     * 
     * @param response The API response containing actual values
     * @param dataProviderClassName The name of the DataProvider class to update
     * @param packageName The package name (wssummarymodularntefixprice, wssummary, or wssummarynteandfixprice)
     */
    public static void updateDataProvider(Response response, String dataProviderClassName, String packageName) {
        try {
            String filePath = "src/test/java/com/lht/corecalculation/api/" + packageName + "/" + dataProviderClassName + ".java";
            String content = Files.readString(Paths.get(filePath));
            
            log.info("Updating DataProvider: {} in package: {}", dataProviderClassName, packageName);
            
            // Find all DataProvider methods and update them
            String updatedContent = updateAllDataProviderMethods(content, response);
            
            // Write the updated content back to the file
            Files.writeString(Paths.get(filePath), updatedContent);
            
            log.info("Successfully updated DataProvider file: {}", filePath);
            
        } catch (Exception e) {
            log.error("Failed to update DataProvider {}: {}", dataProviderClassName, e.getMessage(), e);
        }
    }

    /**
     * Update all DataProvider methods in the file content
     */
    private static String updateAllDataProviderMethods(String content, Response response) {
        // Pattern to match @DataProvider methods with proper boundaries
        Pattern methodPattern = Pattern.compile(
            "(@DataProvider\\(name\\s*=\\s*\"([^\"]+)\"\\)\\s*\n" +
            "\\s*public\\s+Object\\[\\]\\[\\]\\s+(\\w+)\\(\\)\\s*\\{\\s*\n)" +
            "(.*?)" +
            "(\\n\\s*\\})",
            Pattern.DOTALL
        );

        Matcher matcher = methodPattern.matcher(content);
        StringBuffer sb = new StringBuffer();

        while (matcher.find()) {
            String methodHeader = matcher.group(1);
            String dataProviderName = matcher.group(2);
            String methodName = matcher.group(3);
            String methodBody = matcher.group(4);
            String methodFooter = matcher.group(5);

            log.info("Processing DataProvider method: {}", methodName);

            try {
                String newMethodBody = generateNewMethodBody(response, methodName, methodBody);
                String replacement = methodHeader + newMethodBody + methodFooter;
                matcher.appendReplacement(sb, Matcher.quoteReplacement(replacement));
            } catch (Exception e) {
                log.warn("Failed to update method {}: {}", methodName, e.getMessage());
                // Keep original method if update fails
                matcher.appendReplacement(sb, Matcher.quoteReplacement(matcher.group(0)));
            }
        }
        matcher.appendTail(sb);

        return sb.toString();
    }

    /**
     * Generate new method body with actual values from API response
     */
    private static String generateNewMethodBody(Response response, String methodName, String originalBody) throws JsonProcessingException {
        // Determine metric type from method name
        String metricType = extractMetricType(methodName);
        Function<WorkscopeSummaryItemDto, Double> valueExtractor = VALUE_EXTRACTORS.get(metricType);
        
        if (valueExtractor == null) {
            log.warn("No value extractor found for metric type: {} in method: {}", metricType, methodName);
            return originalBody; // Return original if we can't determine the metric
        }
        
        // Determine the structure type
        boolean isComponentBased = isComponentBasedMethod(originalBody);
        boolean hasItemTypes = hasItemTypesInMethod(originalBody);
        
        StringBuilder newBody = new StringBuilder();
        newBody.append("        return new Object[][] {\n");
        
        if (isComponentBased) {
            generateComponentBasedContent(response, valueExtractor, newBody);
        } else {
            generateWorkscopeBasedContent(response, valueExtractor, hasItemTypes, newBody);
        }
        
        // Remove last comma and newline if present
        String bodyStr = newBody.toString();
        if (bodyStr.endsWith(",\n")) {
            newBody.setLength(newBody.length() - 2);
            newBody.append("\n");
        }
        
        newBody.append("        };\n");
        
        return newBody.toString();
    }

    /**
     * Generate content for component-based DataProvider methods (CPR, HPC, etc.)
     */
    private static void generateComponentBasedContent(Response response, Function<WorkscopeSummaryItemDto, Double> valueExtractor, StringBuilder content) throws JsonProcessingException {
        for (String component : COMPONENT_TYPES) {
            try {
                List<Double> actualValues = extractValueFromResponse(response, valueExtractor, component);
                if (!actualValues.isEmpty()) {
                    content.append("                {").append(component).append(", Arrays.asList(");
                    content.append(actualValues.stream()
                        .map(val -> val.toString())
                        .collect(Collectors.joining(", ")));
                    content.append(")},\n");
                }
            } catch (Exception e) {
                log.warn("Failed to extract values for component {}: {}", component, e.getMessage());
            }
        }
    }

    /**
     * Generate content for workscope-based DataProvider methods (WS1, WS2, etc.)
     */
    private static void generateWorkscopeBasedContent(Response response, Function<WorkscopeSummaryItemDto, Double> valueExtractor, 
                                                     boolean hasItemTypes, StringBuilder content) throws JsonProcessingException {
        for (String workscope : WORKSCOPE_NAMES) {
            try {
                if (hasItemTypes) {
                    // Extract values for included, excluded, and total items
                    List<Double> includedValues = extractValueFromResponse(response, valueExtractor, workscope, INCLUDED_ITEMS);
                    List<Double> excludedValues = extractValueFromResponse(response, valueExtractor, workscope, EXCLUDED_ITEMS);
                    List<Double> totalValues = extractValueFromResponse(response, valueExtractor, workscope, TOTAL_ITEMS);
                    
                    if (!includedValues.isEmpty() || !excludedValues.isEmpty() || !totalValues.isEmpty()) {
                        content.append("                {\n");
                        content.append("                        ").append(workscope).append(",\n");
                        
                        if (!includedValues.isEmpty()) {
                            content.append("                        Arrays.asList(");
                            content.append(includedValues.stream()
                                .map(val -> val.toString())
                                .collect(Collectors.joining(", ")));
                            content.append("),\n");
                        }
                        
                        if (!excludedValues.isEmpty()) {
                            content.append("                        Arrays.asList(");
                            content.append(excludedValues.stream()
                                .map(val -> val.toString())
                                .collect(Collectors.joining(", ")));
                            content.append("),\n");
                        }
                        
                        if (!totalValues.isEmpty()) {
                            content.append("                        Arrays.asList(");
                            content.append(totalValues.stream()
                                .map(val -> val.toString())
                                .collect(Collectors.joining(", ")));
                            content.append(")\n");
                        }
                        
                        content.append("                },\n");
                    }
                } else {
                    // Simple workscope-based method with single list
                    List<Double> actualValues = extractValueFromResponse(response, valueExtractor, workscope);
                    if (!actualValues.isEmpty()) {
                        content.append("                {").append(workscope).append(", Arrays.asList(");
                        content.append(actualValues.stream()
                            .map(val -> val.toString())
                            .collect(Collectors.joining(", ")));
                        content.append(")},\n");
                    }
                }
            } catch (Exception e) {
                log.warn("Failed to extract values for workscope {}: {}", workscope, e.getMessage());
            }
        }
    }

    /**
     * Extract metric type from method name (ProductionCost, Revenue, etc.)
     */
    private static String extractMetricType(String methodName) {
        String lowerMethodName = methodName.toLowerCase();
        
        // Check for specific patterns
        if (lowerMethodName.contains("productioncost") && lowerMethodName.contains("discount")) {
            return "ProdCostInclDiscountsAndSurcharges";
        }
        
        for (String metric : VALUE_EXTRACTORS.keySet()) {
            if (lowerMethodName.contains(metric.toLowerCase())) {
                return metric;
            }
        }
        
        // Fallback patterns
        if (lowerMethodName.contains("production")) return "ProductionCost";
        if (lowerMethodName.contains("revenue")) return "Revenue";
        if (lowerMethodName.contains("discount")) return "Discount";
        if (lowerMethodName.contains("surcharge")) return "SurchargesCost";
        if (lowerMethodName.contains("db2")) return "Db2";
        if (lowerMethodName.contains("ebit")) return "Ebit";
        if (lowerMethodName.contains("margin")) return "NetMargin";
        
        return "ProductionCost"; // Default fallback
    }

    /**
     * Check if a DataProvider method is component-based (uses CPR, HPC, etc.)
     */
    private static boolean isComponentBasedMethod(String methodContent) {
        for (String component : COMPONENT_TYPES) {
            if (methodContent.contains(component)) {
                return true;
            }
        }
        return false;
    }

    /**
     * Check if a DataProvider method uses item types (INCLUDED_ITEMS, EXCLUDED_ITEMS, TOTAL_ITEMS)
     */
    private static boolean hasItemTypesInMethod(String methodContent) {
        // Look for patterns indicating multiple Lists per workscope (included/excluded/total)
        long arrayListCount = methodContent.split("Arrays.asList").length - 1;
        return arrayListCount > 2; // More than 2 Lists suggests item types structure
    }

    /**
     * Convenience method for test classes to update their DataProvider
     */
    public static void updateMyDataProvider(Object testInstance, Response response) {
        try {
            Class<?> testClass = testInstance.getClass();
            String className = testClass.getSimpleName();
            String packageName = testClass.getPackage().getName();
            
            // Extract package suffix (last part after the last dot)
            String[] packageParts = packageName.split("\\.");
            String packageSuffix = packageParts[packageParts.length - 1];
            
            // Get the superclass name (which should be the DataProvider class)
            Class<?> superClass = testClass.getSuperclass();
            String dataProviderClassName = superClass.getSimpleName();
            
            log.info("Auto-detected DataProvider: {} for test: {} in package: {}", 
                    dataProviderClassName, className, packageSuffix);
            
            updateDataProvider(response, dataProviderClassName, packageSuffix);
            
        } catch (Exception e) {
            log.error("Failed to auto-update DataProvider: {}", e.getMessage(), e);
        }
    }
}
