package com.lht.corecalculation.api.utils;

import lombok.extern.slf4j.Slf4j;
import org.testng.annotations.Test;

/**
 * Example class showing how to use the DataProvider update utilities.
 * This class demonstrates different ways to update DataProviders with actual API response values.
 */
@Slf4j
public class DataProviderUpdateExample {

    /**
     * Example 1: Update DataProvider for a specific test class
     */
    @Test
    public void updateSpecificTestDataProvider() {
        log.info("=== Example 1: Update DataProvider for specific test ===");
        
        // Update DataProvider for a specific test class
        String testClassName = "GetWorkscopeSummaryEngineLeap1ATests";
        DataProviderUpdateRunner.updateDataProviderForTest(testClassName);
        
        log.info("Completed updating DataProvider for: {}", testClassName);
    }

    /**
     * Example 2: Update DataProviders for all tests in a specific package
     */
    @Test
    public void updatePackageDataProviders() {
        log.info("=== Example 2: Update DataProviders for package ===");
        
        // Update all DataProviders in the wssummary package
        String packageName = "wssummary";
        DataProviderUpdateRunner.updateDataProvidersForPackage(packageName);
        
        log.info("Completed updating DataProviders for package: {}", packageName);
    }

    /**
     * Example 3: Update all DataProviders across all target packages
     */
    @Test
    public void updateAllDataProviders() {
        log.info("=== Example 3: Update all DataProviders ===");
        
        // Update all DataProviders in all target packages
        DataProviderUpdateRunner.updateAllDataProviders();
        
        log.info("Completed updating all DataProviders");
    }

    /**
     * Example 4: Test the update process without actually updating files
     */
    @Test
    public void testUpdateProcess() {
        log.info("=== Example 4: Test update process ===");
        
        // Test the update process for a specific test class
        String testClassName = "GetWorkscopeSummaryEngineLeap1ATests";
        DataProviderUpdateRunner.testUpdateProcess(testClassName);
        
        log.info("Completed testing update process for: {}", testClassName);
    }

    /**
     * Example 5: Update DataProviders for modular pricing tests
     */
    @Test
    public void updateModularPricingDataProviders() {
        log.info("=== Example 5: Update modular pricing DataProviders ===");
        
        // Update DataProviders for modular pricing package
        String packageName = "wssummarymodularntefixprice";
        DataProviderUpdateRunner.updateDataProvidersForPackage(packageName);
        
        log.info("Completed updating modular pricing DataProviders");
    }

    /**
     * Example 6: Update DataProviders for NTE and fixed price tests
     */
    @Test
    public void updateNteAndFixedPriceDataProviders() {
        log.info("=== Example 6: Update NTE and fixed price DataProviders ===");
        
        // Update DataProviders for NTE and fixed price package
        String packageName = "wssummarynteandfixprice";
        DataProviderUpdateRunner.updateDataProvidersForPackage(packageName);
        
        log.info("Completed updating NTE and fixed price DataProviders");
    }

    /**
     * Example 7: Update specific test classes one by one
     */
    @Test
    public void updateSpecificTestClasses() {
        log.info("=== Example 7: Update specific test classes ===");
        
        // List of specific test classes to update
        String[] testClasses = {
            "GetWorkscopeSummaryEngineLeap1ATests",
            "GetWorkscopeModularSummaryNteCfm567bApiTests",
            "GetWorkscopeSummaryWsFixedAndNteLeap1aApiTests"
        };
        
        for (String testClassName : testClasses) {
            log.info("Updating DataProvider for: {}", testClassName);
            DataProviderUpdateRunner.updateDataProviderForTest(testClassName);
        }
        
        log.info("Completed updating specific test classes");
    }

    /**
     * Example 8: Show how to use the utility from within a test class
     * This would be called from within an actual test class that extends a DataProvider
     */
    public void exampleUsageFromTestClass() {
        log.info("=== Example 8: Usage from within test class ===");
        
        // This example shows how you would use the utility from within a test class
        // that has a sharedWorkscopeSummaryResponse field
        
        /*
         * In your test class, after fetchWorkscopeSummary() has been called:
         * 
         * @Test
         * public void updateMyDataProvider() {
         *     // This will automatically detect the DataProvider class and update it
         *     DataProviderUpdater.updateMyDataProvider(this, sharedWorkscopeSummaryResponse);
         * }
         * 
         * Or manually specify the DataProvider:
         * 
         * @Test
         * public void updateMyDataProviderManually() {
         *     String dataProviderClassName = "DataProvidersWorkscopeSummaryLeap1A";
         *     String packageName = "wssummary";
         *     DataProviderUpdater.updateDataProvider(sharedWorkscopeSummaryResponse, dataProviderClassName, packageName);
         * }
         */
        
        log.info("See code comments for usage examples from within test classes");
    }

    /**
     * Example 9: Batch update with error handling
     */
    @Test
    public void batchUpdateWithErrorHandling() {
        log.info("=== Example 9: Batch update with error handling ===");
        
        String[] packages = {"wssummary", "wssummarymodularntefixprice", "wssummarynteandfixprice"};
        
        for (String packageName : packages) {
            try {
                log.info("Starting update for package: {}", packageName);
                DataProviderUpdateRunner.updateDataProvidersForPackage(packageName);
                log.info("Successfully updated package: {}", packageName);
            } catch (Exception e) {
                log.error("Failed to update package {}: {}", packageName, e.getMessage());
                // Continue with next package even if one fails
            }
        }
        
        log.info("Completed batch update with error handling");
    }

    /**
     * Example 10: Selective update based on test results
     */
    @Test
    public void selectiveUpdateBasedOnTestResults() {
        log.info("=== Example 10: Selective update based on test results ===");
        
        // This example shows how you might selectively update DataProviders
        // based on which tests are failing
        
        String[] failingTests = {
            "GetWorkscopeSummaryEngineLeap1ATests",
            "GetWorkscopeModularSummaryNteCfm567bApiTests"
        };
        
        log.info("Updating DataProviders for failing tests only...");
        
        for (String testClassName : failingTests) {
            try {
                log.info("Updating DataProvider for failing test: {}", testClassName);
                DataProviderUpdateRunner.updateDataProviderForTest(testClassName);
                log.info("Successfully updated DataProvider for: {}", testClassName);
            } catch (Exception e) {
                log.error("Failed to update DataProvider for {}: {}", testClassName, e.getMessage());
            }
        }
        
        log.info("Completed selective update for failing tests");
    }
}
