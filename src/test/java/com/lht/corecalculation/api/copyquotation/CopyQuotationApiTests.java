package com.lht.corecalculation.api.copyquotation;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.lht.corecalculation.api.enums.TestUser;
import com.lht.corecalculation.api.pojo.dto.inner.QuotationCover;
import com.lht.corecalculation.api.request.copyquotation.CopyQuotationRequest;
import com.lht.corecalculation.api.request.quotation.GetQuotationByIdRequest;
import com.lht.corecalculation.api.utils.BaseQuotationUtil;
import com.lht.corecalculation.api.utils.MessageFormater;
import com.lht.corecalculation.base.BaseCocaApiTest;
import com.lht.corecalculation.exceptions.CopyQuotationLimitException;
import io.restassured.response.Response;
import java.io.IOException;
import org.testng.Assert;
import org.testng.annotations.Test;


import static com.lht.corecalculation.api.constants.GlobalConstants.ADMIN_TOKEN;
import static com.lht.corecalculation.api.constants.GlobalConstants.BAD_REQUEST_ERROR_MESSAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.COPY_QUOTATION_NOT_ALLOWED;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_V2500_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ERROR_DETAIL;
import static com.lht.corecalculation.api.constants.GlobalConstants.ERROR_DETAILS_MISMATCH_AS_MESSAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.ERROR_NOT_FOUND;
import static com.lht.corecalculation.api.constants.GlobalConstants.ERROR_QUOTATION_NOT_FOUND;
import static com.lht.corecalculation.api.constants.GlobalConstants.ERROR_TYPE;
import static com.lht.corecalculation.api.constants.GlobalConstants.ERROR_TYPE_MISMATCH_AS_MESSAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.FORBIDDEN;
import static com.lht.corecalculation.api.constants.GlobalConstants.GENERIC_ERROR_DESCRIPTION_MISMATCH_AS_MESSAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_400;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_403;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_404;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.USER_DOESNT_HAVE_REQUIRED_PERMISSIONS;
import static com.lht.corecalculation.api.utils.BaseQuotationUtil.getAndAssertQuotation;
import static com.lht.corecalculation.api.utils.CopyQuotationUtil.compareOriginalAndCopyQuotation;

public class CopyQuotationApiTests extends BaseCocaApiTest {

    @Test(groups = "workscopeFixedPriceAndNte", dataProvider = "quotationIdsByEngineProvider")
    public void copyQuotationWithValidAccessToken_Positive(String quotationId) throws IOException, CopyQuotationLimitException {
        GetQuotationByIdRequest getQuotationByIdRequest = new GetQuotationByIdRequest(quotationId)
                .withBearerToken(utils.context.getContextItem(ADMIN_TOKEN));
        QuotationCover originalQuotation = getAndAssertQuotation(getQuotationByIdRequest);

        CopyQuotationRequest copyQuotationRequest = new CopyQuotationRequest(quotationId)
                .withBearerToken(utils.context.getContextItem(ADMIN_TOKEN));
        QuotationCover copyQuotation = getAndAssertQuotation(copyQuotationRequest);

        compareOriginalAndCopyQuotation(originalQuotation, copyQuotation);
    }

    @Test(groups = "workscopeFixedPriceAndNte", dataProvider = "viewOnlyAccessTokenProvider")
    public void copyQuotationWithViewOnlyUserIsForbidden_Negative(String accessToken) {
        CopyQuotationRequest copyQuotationRequest = new CopyQuotationRequest(ENGINE_V2500_QUOTATION_ID).withBearerToken(accessToken);

        Response response = copyQuotationRequest.callAPI();

        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_400, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
        Assert.assertEquals(response.jsonPath().get(ERROR_TYPE).toString(), BAD_REQUEST_ERROR_MESSAGE, ERROR_TYPE_MISMATCH_AS_MESSAGE);
        Assert.assertTrue(response.jsonPath().get(ERROR_DETAIL).toString().contains(MessageFormater.viewOnlyUserCannotModifyData(TestUser.VIEW_ONLY,ENGINE_V2500_QUOTATION_ID)), ERROR_DETAILS_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = "workscopeFixedPriceAndNte", dataProvider = "calcAccessTokenProvider")
    public void copyNonExistingQuotationWithTestUsersIsNotFound_Negative(String accessToken) throws JsonProcessingException , CopyQuotationLimitException {
        var nonExistingQuotationId = BaseQuotationUtil.getCurrentMaxQuotationsCount(accessToken) + 1;
        CopyQuotationRequest copyQuotationRequest = new CopyQuotationRequest(String.valueOf(nonExistingQuotationId)).withBearerToken(accessToken);
        Response response = copyQuotationRequest.callAPI();

        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_404, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
        Assert.assertEquals(response.jsonPath().get(ERROR_TYPE).toString(), ERROR_NOT_FOUND, GENERIC_ERROR_DESCRIPTION_MISMATCH_AS_MESSAGE);
        Assert.assertTrue(response.jsonPath().get(ERROR_DETAIL).toString().contains(String.format(ERROR_QUOTATION_NOT_FOUND, nonExistingQuotationId)), ERROR_DETAILS_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = "workscopeFixedPriceAndNte", dependsOnMethods = "copyQuotationWithValidAccessToken_Positive")
    public void copyQuotationAboveLimitIsBadRequest_Negative() throws JsonProcessingException, CopyQuotationLimitException {
        CopyQuotationRequest copyQuotationRequest = new CopyQuotationRequest(ENGINE_V2500_QUOTATION_ID)
                .withBearerToken(utils.context.getContextItem(ADMIN_TOKEN));

        for (int i = 0; i < 9; i++) {
            getAndAssertQuotation(copyQuotationRequest);
        }

        Response response = copyQuotationRequest.callAPI();

        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_400, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
        Assert.assertEquals(response.jsonPath().get(ERROR_TYPE).toString(), BAD_REQUEST_ERROR_MESSAGE, GENERIC_ERROR_DESCRIPTION_MISMATCH_AS_MESSAGE);
        Assert.assertTrue(response.jsonPath().get(ERROR_DETAIL).toString().contains(COPY_QUOTATION_NOT_ALLOWED), ERROR_DETAILS_MISMATCH_AS_MESSAGE);
    }
}
