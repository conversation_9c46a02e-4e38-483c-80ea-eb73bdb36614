package com.lht.corecalculation.api.filters;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.lht.corecalculation.api.pojo.dto.common.ErrorDto;
import com.lht.corecalculation.api.pojo.dto.common.GenericErrorDto;
import com.lht.corecalculation.api.pojo.dto.filters.QuotationFiltersDto;
import com.lht.corecalculation.api.pojo.dto.inner.User;
import com.lht.corecalculation.api.pojo.entity.user.UserEntity;
import com.lht.corecalculation.api.request.filters.GetQuotationFiltersRequest;
import com.lht.corecalculation.api.request.user.GetAllUsersRequest;
import com.lht.corecalculation.base.BaseCocaApiTest;
import io.restassured.response.Response;
import org.testng.Assert;
import org.testng.annotations.Test;
import org.testng.asserts.SoftAssert;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;


import static com.lht.corecalculation.api.constants.GlobalConstants.FORBIDDEN;
import static com.lht.corecalculation.api.constants.GlobalConstants.NO_ROLE_TOKEN;
import static com.lht.corecalculation.api.constants.GlobalConstants.PROPERTY_DOES_NOT_MATCH_AS_MESSAGE_FORMAT;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.UNAUTHORIZED;
import static com.lht.corecalculation.api.constants.GlobalConstants.USER_DOESNT_HAVE_REQUIRED_PERMISSIONS;

public class GetFiltersApiTests extends BaseCocaApiTest {

    @Test(groups = "smoke", dataProvider = "adminAccessTokenProvider")
    public void getAllFilters_WithAdminToken_ReturnsCompleteAndCorrectData_positive(String token) throws IOException {
        GetQuotationFiltersRequest getQuotationFiltersRequest =
                new GetQuotationFiltersRequest()
                        .withBearerToken(token);

        Response response = getQuotationFiltersRequest.callAPI();
        QuotationFiltersDto quotationFiltersDto = utils.convert.jsonToDto(response, QuotationFiltersDto.class);
        Assert.assertEquals(response.statusCode(), 200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        var soft = new SoftAssert();
        soft.assertTrue(quotationFiltersDto.getFilters().getQuotationStatuses().containsAll(getAllStatuses()), String.format(PROPERTY_DOES_NOT_MATCH_AS_MESSAGE_FORMAT, "Status"));
        soft.assertTrue(quotationFiltersDto.getFilters().getEngineTypes().containsAll(getAllEngineTypes()), String.format(PROPERTY_DOES_NOT_MATCH_AS_MESSAGE_FORMAT, "Engine type"));
        soft.assertTrue(quotationFiltersDto.getFilters().getOwners().containsAll(getAllOwners()), String.format(PROPERTY_DOES_NOT_MATCH_AS_MESSAGE_FORMAT, "Owner"));
        soft.assertNull(quotationFiltersDto.getError());
        soft.assertAll();
    }

    @Test()
    public void getFiltersWithNoRoleTokenIsForbidden() throws JsonProcessingException {
        GetQuotationFiltersRequest getQuotationFiltersRequest =
                new GetQuotationFiltersRequest()
                        .withBearerToken(utils.context.getContextItem(NO_ROLE_TOKEN));

        Response response = getQuotationFiltersRequest.callAPI();
        Assert.assertEquals(response.statusCode(), 401);

        ErrorDto errorDto = utils.convert.jsonToDto(response, ErrorDto.class);
        var soft = new SoftAssert();
        soft.assertEquals(errorDto.getError().getStatus(), 401);
        soft.assertEquals(errorDto.getError().getType(), UNAUTHORIZED);
        soft.assertTrue(errorDto.getError().getDetail().startsWith(USER_DOESNT_HAVE_REQUIRED_PERMISSIONS));
        soft.assertAll();
    }

    @Test()
    public void getFiltersWithoutTokenIsUnauthorized() throws JsonProcessingException {
        GetAllUsersRequest getAllUsersRequest = new GetAllUsersRequest();

        Response response = getAllUsersRequest.callAPI();
        Assert.assertEquals(response.statusCode(), 401);

        GenericErrorDto errorDto = utils.convert.jsonToDto(response, GenericErrorDto.class);
        var soft = new SoftAssert();
        soft.assertEquals(errorDto.getStatus(), "401");
        soft.assertEquals(errorDto.getError(), "Unauthorized");
        soft.assertEquals(errorDto.getMessage(), "Full authentication is required to access this resource");
        soft.assertAll();
    }

    private List<String> getAllStatuses() {
        return List.of("ANKA_VALIDATED", "IN_PROGRESS", "TRANSFERRED", "COMPLETED");
    }

    private List<String> getAllEngineTypes() {
        return List.of("CFM56-5B", "CFM56-7B", "LEAP-1A", "LEAP-1B", "V2500");
    }

    private List<User> getAllOwners() {
        List<UserEntity> userEntities = dbGetCurrentOwners();
        List<User> owners = new ArrayList<>();
        userEntities.forEach(ue ->
                owners.add(new User()
                        .withId(ue.getId())
                        .withName(ue.getFullName())
                        .withUsername(ue.getUNumber())
                        .withEmail(ue.getEmail())));
        return owners;
    }
}
