package com.lht.corecalculation.api.quotation;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.lht.corecalculation.api.pojo.dto.quotation.BeginQuotationProgressDto;
import com.lht.corecalculation.api.request.quotation.PutQuotationByIdRequest;
import com.lht.corecalculation.base.BaseCocaApiTest;
import io.restassured.response.Response;
import org.testng.Assert;
import org.testng.annotations.Test;


import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_CFM56_5B_PMA_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_CFM56_5B_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_CFM56_7B_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_LEAP_1A_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_LEAP_1B_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_V2500_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_200;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE;

public class PutQuotationBeginNteAndWfpTests extends BaseCocaApiTest {

    @Test(groups = "workscopeFixedPriceAndNte", dataProvider = "adminAccessTokenProvider")
    public void startAnkaValidatedQuotationByAdminWithWfpContractType_Leap1A(String accessToken) throws JsonProcessingException {
        BeginQuotationProgressDto beginQuotationProgressDto = new BeginQuotationProgressDto().withWorkscopeFixedPrice(true);

        PutQuotationByIdRequest putQuotationByIdRequest =
                new PutQuotationByIdRequest(ENGINE_LEAP_1A_QUOTATION_ID, utils.convert.dtoToJsonString(beginQuotationProgressDto))
                        .withBearerToken(accessToken);

        Response response = putQuotationByIdRequest.callAPI();
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = "workscopeFixedPriceAndNte", dataProvider = "adminAccessTokenProvider")
    public void startAnkaValidatedQuotationByAdminWithNteContractType_Leap1B(String accessToken) throws JsonProcessingException {
        BeginQuotationProgressDto beginQuotationProgressDto = new BeginQuotationProgressDto().withWorkscopeNTE(true);

        PutQuotationByIdRequest putQuotationByIdRequest =
                new PutQuotationByIdRequest(ENGINE_LEAP_1B_QUOTATION_ID, utils.convert.dtoToJsonString(beginQuotationProgressDto))
                        .withBearerToken(accessToken);

        Response response = putQuotationByIdRequest.callAPI();
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = "workscopeFixedPriceAndNte", dataProvider = "adminAccessTokenProvider")
    public void startAnkaValidatedQuotationByAdminWithWfpContractType_Cfm565B(String accessToken) throws JsonProcessingException {
        BeginQuotationProgressDto beginQuotationProgressDto = new BeginQuotationProgressDto().withWorkscopeFixedPrice(true);

        PutQuotationByIdRequest putQuotationByIdRequest =
                new PutQuotationByIdRequest(ENGINE_CFM56_5B_QUOTATION_ID, utils.convert.dtoToJsonString(beginQuotationProgressDto))
                        .withBearerToken(accessToken);

        Response response = putQuotationByIdRequest.callAPI();
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = "workscopeFixedPriceAndNte", dataProvider = "adminAccessTokenProvider")
    public void startAnkaValidatedQuotationByAdminWithNteContractType_Cfm567B(String accessToken) throws JsonProcessingException {
        BeginQuotationProgressDto beginQuotationProgressDto = new BeginQuotationProgressDto().withWorkscopeNTE(true);

        PutQuotationByIdRequest putQuotationByIdRequest =
                new PutQuotationByIdRequest(ENGINE_CFM56_7B_QUOTATION_ID, utils.convert.dtoToJsonString(beginQuotationProgressDto))
                        .withBearerToken(accessToken);

        Response response = putQuotationByIdRequest.callAPI();
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = "workscopeFixedPriceAndNte", dataProvider = "adminAccessTokenProvider")
    public void startAnkaValidatedQuotationByAdminWithWfpContractType_V2500(String accessToken) throws JsonProcessingException {
        BeginQuotationProgressDto beginQuotationProgressDto = new BeginQuotationProgressDto().withWorkscopeFixedPrice(true);

        PutQuotationByIdRequest putQuotationByIdRequest =
                new PutQuotationByIdRequest(ENGINE_V2500_QUOTATION_ID, utils.convert.dtoToJsonString(beginQuotationProgressDto))
                        .withBearerToken(accessToken);

        Response response = putQuotationByIdRequest.callAPI();
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = "workscopeFixedPriceAndNte", dataProvider = "adminAccessTokenProvider")
    public void startAnkaValidatedQuotationByAdminWithNteContractType_Cfm565B_PMA(String accessToken) throws JsonProcessingException {
        BeginQuotationProgressDto beginQuotationProgressDto = new BeginQuotationProgressDto().withWorkscopeNTE(true);

        PutQuotationByIdRequest putQuotationByIdRequest =
                new PutQuotationByIdRequest(ENGINE_CFM56_5B_PMA_QUOTATION_ID, utils.convert.dtoToJsonString(beginQuotationProgressDto))
                        .withBearerToken(accessToken);

        Response response = putQuotationByIdRequest.callAPI();
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
    }
}
