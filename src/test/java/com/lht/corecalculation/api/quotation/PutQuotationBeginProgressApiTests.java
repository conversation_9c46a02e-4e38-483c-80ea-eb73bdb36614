package com.lht.corecalculation.api.quotation;

import antlr.debug.MessageAdapter;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.lht.corecalculation.api.enums.TestUser;
import com.lht.corecalculation.api.pojo.dto.quotation.BeginQuotationProgressDto;
import com.lht.corecalculation.api.request.quotation.PutQuotationByIdRequest;
import com.lht.corecalculation.api.utils.BaseQuotationUtil;
import com.lht.corecalculation.api.utils.MessageFormater;
import com.lht.corecalculation.base.BaseCocaApiTest;
import com.lht.corecalculation.exceptions.CopyQuotationLimitException;
import io.restassured.response.Response;
import java.io.IOException;
import org.testng.Assert;
import org.testng.annotations.Test;


import static com.lht.corecalculation.api.constants.GlobalConstants.BAD_REQUEST_ERROR_MESSAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_CFM56_5B_PMA_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_LEAP_1A_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_LEAP_1B_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_V2500_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ERROR_DETAIL;
import static com.lht.corecalculation.api.constants.GlobalConstants.ERROR_DETAILS_MISMATCH_AS_MESSAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.ERROR_NOT_FOUND;
import static com.lht.corecalculation.api.constants.GlobalConstants.ERROR_QUOTATION_NOT_FOUND;
import static com.lht.corecalculation.api.constants.GlobalConstants.ERROR_TYPE;
import static com.lht.corecalculation.api.constants.GlobalConstants.ERROR_TYPE_MISMATCH_AS_MESSAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.FORBIDDEN;
import static com.lht.corecalculation.api.constants.GlobalConstants.GENERIC_ERROR_DESCRIPTION_MISMATCH_AS_MESSAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.INVALID_REQUEST_CONTENT;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_200;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_400;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_401;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_403;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_404;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE;

public class PutQuotationBeginProgressApiTests extends BaseCocaApiTest {

    /**
     * PUT begin quotation -----------------------------------------------------------------------------------------
     */

    @Test(groups = "smoke", dataProvider = "adminAccessTokenProvider")
    public void startAnkaValidatedQuotationByAdminWithRfpContractType_Positive(String accessToken) throws JsonProcessingException {
        BeginQuotationProgressDto beginQuotationProgressDto = new BeginQuotationProgressDto().withRoutineFixedPrice(true);

        PutQuotationByIdRequest putQuotationByIdRequest =
                new PutQuotationByIdRequest(ENGINE_LEAP_1B_QUOTATION_ID, utils.convert.dtoToJsonString(beginQuotationProgressDto))
                        .withBearerToken(accessToken);

        Response response = putQuotationByIdRequest.callAPI();
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

    }

    @Test(dataProvider = "adminAccessTokenProvider")
    public void startAnkaValidatedQuotationByAdminWithoutRfpContractType_Positive(String accessToken) throws JsonProcessingException {
        BeginQuotationProgressDto beginQuotationProgressDto = new BeginQuotationProgressDto().withRoutineFixedPrice(false);

        PutQuotationByIdRequest putQuotationByIdRequest =
                new PutQuotationByIdRequest(ENGINE_CFM56_5B_PMA_QUOTATION_ID, utils.convert.dtoToJsonString(beginQuotationProgressDto))
                        .withBearerToken(accessToken);

        Response response = putQuotationByIdRequest.callAPI();
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

    }

    @Test(dataProvider = "viewOnlyAccessTokenProvider")
    public void startAnkaValidatedQuotationByNonOwnerViewOnlyUserOrNoRoleUserIsForbidden_Negative(String accessToken) throws IOException {
        BeginQuotationProgressDto beginQuotationProgressDto = new BeginQuotationProgressDto().withRoutineFixedPrice(true);

        PutQuotationByIdRequest putQuotationByIdRequest =
                new PutQuotationByIdRequest(ENGINE_V2500_QUOTATION_ID, utils.convert.dtoToJsonString(beginQuotationProgressDto))
                        .withBearerToken(accessToken);

        Response response = putQuotationByIdRequest.callAPI();
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_400, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
        Assert.assertEquals(response.jsonPath().get(ERROR_TYPE).toString(), BAD_REQUEST_ERROR_MESSAGE, ERROR_TYPE_MISMATCH_AS_MESSAGE);
        Assert.assertTrue(response.jsonPath().get(ERROR_DETAIL).toString().contains(MessageFormater.viewOnlyUserCannotModifyData(TestUser.VIEW_ONLY,ENGINE_V2500_QUOTATION_ID)), ERROR_DETAILS_MISMATCH_AS_MESSAGE);
    }

    @Test()
    public void startAnkaValidatedQuotationWithoutAccessTokenIsUnauthorized_Negative() throws IOException {
        BeginQuotationProgressDto beginQuotationProgressDto = new BeginQuotationProgressDto().withRoutineFixedPrice(true);

        PutQuotationByIdRequest putQuotationByIdRequest =
                new PutQuotationByIdRequest(ENGINE_V2500_QUOTATION_ID, utils.convert.dtoToJsonString(beginQuotationProgressDto))
                        .withBearerToken("");

        Response response = putQuotationByIdRequest.callAPI();
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_401, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
        Assert.assertEquals(response.jsonPath().get("error").toString(), "Unauthorized", ERROR_TYPE_MISMATCH_AS_MESSAGE);
        Assert.assertTrue(response.jsonPath().get("message").toString().contains("Full authentication is required to access this resource"), ERROR_DETAILS_MISMATCH_AS_MESSAGE);
    }

    @Test(dataProvider = "adminAccessTokenProvider", dependsOnMethods = "startAnkaValidatedQuotationByAdminWithRfpContractType_Positive")
    public void startAlreadyStartedQuotationIsBadRequest_Negative(String accessToken) throws IOException {
        BeginQuotationProgressDto beginQuotationProgressDto = new BeginQuotationProgressDto().withRoutineFixedPrice(true);

        PutQuotationByIdRequest putQuotationByIdRequest =
                new PutQuotationByIdRequest(ENGINE_LEAP_1B_QUOTATION_ID, utils.convert.dtoToJsonString(beginQuotationProgressDto))
                        .withBearerToken(accessToken);

        Response response = putQuotationByIdRequest.callAPI();
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_400, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
        Assert.assertEquals(response.jsonPath().get(ERROR_TYPE).toString(), BAD_REQUEST_ERROR_MESSAGE, ERROR_TYPE_MISMATCH_AS_MESSAGE);
        Assert.assertTrue(response.jsonPath().get(ERROR_DETAIL).toString().contains("Quotation with id: [3] already had begun"), ERROR_DETAILS_MISMATCH_AS_MESSAGE);
    }

    @Test(dataProvider = "adminAccessTokenProvider")
    public void startQuotationWithNonExistentIdIsNotFound_Negative(String accessToken) throws IOException, CopyQuotationLimitException {
        Integer currentMaxQuotations = BaseQuotationUtil.getCurrentMaxQuotationsCount(accessToken);

        BeginQuotationProgressDto beginQuotationProgressDto = new BeginQuotationProgressDto()
                .withRoutineFixedPrice(true);

        PutQuotationByIdRequest putQuotationByIdRequest =
                new PutQuotationByIdRequest(String.valueOf(currentMaxQuotations + 1), utils.convert.dtoToJsonString(beginQuotationProgressDto))
                        .withBearerToken(accessToken);

        Response responseNonExistingQuotation = putQuotationByIdRequest.callAPI();
        Assert.assertEquals(responseNonExistingQuotation.statusCode(), RESPONSE_STATUS_CODE_404, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
        Assert.assertEquals(responseNonExistingQuotation.jsonPath().get(ERROR_TYPE).toString(), ERROR_NOT_FOUND, GENERIC_ERROR_DESCRIPTION_MISMATCH_AS_MESSAGE);
        Assert.assertTrue(responseNonExistingQuotation.jsonPath().get(ERROR_DETAIL).toString().contains(String.format(ERROR_QUOTATION_NOT_FOUND, currentMaxQuotations + 1)), ERROR_DETAILS_MISMATCH_AS_MESSAGE);
    }

    @Test(dataProvider = "adminAccessTokenProvider")
    public void startQuotationWithoutRftPropertyIsBadRequest_Negative(String accessToken) throws IOException {
        BeginQuotationProgressDto beginQuotationProgressDto = new BeginQuotationProgressDto();

        PutQuotationByIdRequest putQuotationByIdRequest =
                new PutQuotationByIdRequest(ENGINE_LEAP_1A_QUOTATION_ID, utils.convert.dtoToJsonString(beginQuotationProgressDto))
                        .withBearerToken(accessToken);

        Response response = putQuotationByIdRequest.callAPI();
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_400, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
        Assert.assertEquals(response.jsonPath().get(ERROR_TYPE).toString(), BAD_REQUEST_ERROR_MESSAGE, ERROR_TYPE_MISMATCH_AS_MESSAGE);
        Assert.assertTrue(response.jsonPath().get(ERROR_DETAIL).toString().contains(INVALID_REQUEST_CONTENT), ERROR_DETAILS_MISMATCH_AS_MESSAGE);
    }
}
