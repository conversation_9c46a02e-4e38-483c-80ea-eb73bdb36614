package com.lht.corecalculation.api.quotation;

import com.lht.corecalculation.api.pojo.dto.quotation.QuotationDetailsDto;
import com.lht.corecalculation.api.request.quotation.GetQuotationByIdRequest;
import com.lht.corecalculation.api.utils.BaseQuotationUtil;
import com.lht.corecalculation.base.BaseCocaApiTest;
import com.lht.corecalculation.exceptions.CopyQuotationLimitException;
import io.restassured.response.Response;
import java.io.IOException;
import org.testng.Assert;
import org.testng.annotations.Test;
import org.testng.asserts.SoftAssert;


import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_CFM56_5B_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_V2500_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ERROR_DETAIL;
import static com.lht.corecalculation.api.constants.GlobalConstants.ERROR_DETAILS_MISMATCH_AS_MESSAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.ERROR_NOT_FOUND;
import static com.lht.corecalculation.api.constants.GlobalConstants.ERROR_QUOTATION_NOT_FOUND_UPPERCASE;
import static com.lht.corecalculation.api.constants.GlobalConstants.ERROR_TYPE;
import static com.lht.corecalculation.api.constants.GlobalConstants.ERROR_TYPE_MISMATCH_AS_MESSAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.FORBIDDEN;
import static com.lht.corecalculation.api.constants.GlobalConstants.GENERIC_ERROR_DESCRIPTION_MISMATCH_AS_MESSAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.OFFER_NUMBER_V2500;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_200;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_401;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_403;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_404;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.UNAUTHORIZED;
import static com.lht.corecalculation.api.constants.GlobalConstants.USER_DOESNT_HAVE_REQUIRED_PERMISSIONS;

public class GetQuotationByIdApiTests extends BaseCocaApiTest {

    /**
     * GET quotations by ID -----------------------------------------------------------------------------------------
     */
    @Test(groups = "smoke", dataProvider = "adminAccessTokenProvider")
    public void getExistingQuotationByIdWithValidAccessToken_Positive(String accessToken) throws IOException {
        GetQuotationByIdRequest getQuotationByIdRequest = new GetQuotationByIdRequest(ENGINE_V2500_QUOTATION_ID)
                .withBearerToken(accessToken);

        Response response = getQuotationByIdRequest.callAPI();
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        QuotationDetailsDto quotationDetailsDto = utils.convert.jsonToDto(response, QuotationDetailsDto.class);

        var soft = new SoftAssert();
        soft.assertEquals(quotationDetailsDto.getQuotation().getEngine().getName(), "V2500");
        soft.assertEquals(quotationDetailsDto.getQuotation().getOfferNumber(), OFFER_NUMBER_V2500);
        soft.assertEquals(quotationDetailsDto.getQuotation().getUsdExchangeRate().toString(), "1.09505");
        soft.assertNull(quotationDetailsDto.getError());
        soft.assertAll();
    }

    @Test(dataProvider = "adminAccessTokenProvider")
    public void getNonExistingQuotationIdWithValidAccessTokenIsNotFound_Negative(String accessToken) throws IOException , CopyQuotationLimitException {
        Integer currentMaxQuotations = BaseQuotationUtil.getCurrentMaxQuotationsCount(accessToken);

        GetQuotationByIdRequest getQuotationByIdRequest = new GetQuotationByIdRequest(String.valueOf(currentMaxQuotations + 1))
                .withBearerToken(accessToken);
        Response responseNonExistingQuotation = getQuotationByIdRequest.callAPI();

        Assert.assertEquals(responseNonExistingQuotation.statusCode(), RESPONSE_STATUS_CODE_404, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
        Assert.assertEquals(responseNonExistingQuotation.jsonPath().get(ERROR_TYPE).toString(), ERROR_NOT_FOUND, GENERIC_ERROR_DESCRIPTION_MISMATCH_AS_MESSAGE);
        Assert.assertTrue(responseNonExistingQuotation.jsonPath().get(ERROR_DETAIL).toString().contains(String.format(ERROR_QUOTATION_NOT_FOUND_UPPERCASE, currentMaxQuotations + 1)), ERROR_DETAILS_MISMATCH_AS_MESSAGE);
    }

    @Test(dataProvider = "noRoleAccessTokenProvider")
    public void getValidQuotationIdWithNoRoleAccessTokenIsForbidden_Negative(String accessToken) {
        GetQuotationByIdRequest getQuotationByIdRequest = new GetQuotationByIdRequest(ENGINE_CFM56_5B_QUOTATION_ID)
                .withBearerToken(accessToken);

        Response response = getQuotationByIdRequest.callAPI();

        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_401, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
        Assert.assertEquals(response.jsonPath().get(ERROR_TYPE).toString(), UNAUTHORIZED, ERROR_TYPE_MISMATCH_AS_MESSAGE);
        Assert.assertTrue(response.jsonPath().get(ERROR_DETAIL).toString().contains(USER_DOESNT_HAVE_REQUIRED_PERMISSIONS), ERROR_DETAILS_MISMATCH_AS_MESSAGE);
    }
}
