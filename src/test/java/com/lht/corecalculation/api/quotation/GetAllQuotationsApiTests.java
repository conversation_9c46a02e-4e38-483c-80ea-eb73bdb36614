package com.lht.corecalculation.api.quotation;

import com.lht.corecalculation.api.enums.EngineType;
import com.lht.corecalculation.api.enums.QuotationComparator;
import com.lht.corecalculation.api.enums.QuotationSortOption;
import com.lht.corecalculation.api.enums.QuotationStatus;
import com.lht.corecalculation.api.pojo.dto.common.ErrorDto;
import com.lht.corecalculation.api.pojo.dto.quotation.QuotationResultDto;
import com.lht.corecalculation.api.pojo.dto.quotation.QuotationRowDto;
import com.lht.corecalculation.api.pojo.dto.quotation.QuotationWrapperDto;
import com.lht.corecalculation.api.request.quotation.GetQuotationsRequest;
import com.lht.corecalculation.base.BaseCocaApiTest;
import io.restassured.response.Response;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import org.testng.Assert;
import org.testng.annotations.Test;
import org.testng.asserts.SoftAssert;


import static com.lht.corecalculation.api.constants.GlobalConstants.BAD_REQUEST_ERROR_MESSAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.FORBIDDEN;
import static com.lht.corecalculation.api.constants.GlobalConstants.NO_ROLE_TOKEN;
import static com.lht.corecalculation.api.constants.GlobalConstants.PROPERTY_DOES_NOT_MATCH_AS_MESSAGE_FORMAT;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_QUOTATIONS_LIST_SIZE_MISMATCH_AS_MESSAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_QUOTATIONS_ORDERING_MISMATCH_AS_MESSAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_200;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_400;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_401;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_403;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_TOTAL_PAGES_MISMATCH_AS_MESSAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_TOTAL_QUOTATIONS_COUNT_MISMATCH_AS_MESSAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.UNAUTHORIZED;
import static com.lht.corecalculation.api.constants.GlobalConstants.USER_DOESNT_HAVE_REQUIRED_PERMISSIONS;
import static com.lht.corecalculation.api.enums.EngineType.CFM56_5A;
import static com.lht.corecalculation.api.enums.EngineType.CFM56_5B;
import static com.lht.corecalculation.api.enums.QuotationStatus.IN_PROGRESS;
import static com.lht.corecalculation.api.enums.QuotationStatus.TRANSFERRED;

public class GetAllQuotationsApiTests extends BaseCocaApiTest {

    private static List<QuotationRowDto> orderQuotations(QuotationResultDto quotationResultDto, QuotationComparator byStatusAndDate) {
        return quotationResultDto.getQuotations()
                .stream()
                .sorted(byStatusAndDate.get())
                .collect(Collectors.toList());
    }

    private static boolean checkVersion(int expectedVersion, QuotationResultDto quotationResultDto) {
        return quotationResultDto.getQuotations()
                .stream()
                .allMatch(q -> q.getVersion() == expectedVersion);
    }

    private static boolean checkPosition(int expectedPosition, QuotationResultDto quotationResultDto) {
        return quotationResultDto.getQuotations()
                .stream()
                .allMatch(q -> q.getPosition() == expectedPosition);
    }

    private static boolean checkScenario(int expectedScenario, QuotationResultDto quotationResultDto) {
        return quotationResultDto.getQuotations()
                .stream()
                .allMatch(q -> q.getScenario() == expectedScenario);
    }

    private static boolean checkCustomer(String expectedCustomer, QuotationResultDto quotationResultDto) {
        return quotationResultDto.getQuotations()
                .stream()
                .allMatch(q -> q.getCustomer().equals(expectedCustomer));
    }

    private static boolean checkEngineType(EngineType expectedEngineType, QuotationResultDto quotationResultDto) {
        return quotationResultDto.getQuotations()
                .stream()
                .allMatch(q -> q.getEngineType().equals(expectedEngineType.asString()));
    }

    private static boolean checkOfferNumber(QuotationResultDto quotationResultDto, String... expectedOfferNumber) {
        return quotationResultDto.getQuotations()
                .stream()
                .allMatch(q -> Arrays.stream(expectedOfferNumber).anyMatch(on -> on.equals(q.getOfferNumber())));
    }

    private static boolean checkStatus(QuotationStatus expectedStatus, QuotationResultDto quotationResultDto) {
        return quotationResultDto.getQuotations()
                .stream()
                .allMatch(q -> q.getStatus().equals(expectedStatus.asString()));
    }

    private static boolean checkOwner(String expectedOwner, QuotationResultDto quotationResultDto) {
        return quotationResultDto.getQuotations()
                .stream()
                .allMatch(q -> q.getOwner().getName().equals(expectedOwner));
    }

    /**
     * GET all quotations -----------------------------------------------------------------------------------------
     */

    @Test(dataProvider = "adminAccessTokenProvider")
    public void getQuotationsWithoutParamsAndValidAccessTokenReturnsTheFirstTwentyQuotationsInDefaultSortOrder(String accessToken) throws IOException {
        GetQuotationsRequest getQuotationsRequest = new GetQuotationsRequest().withAccessToken(accessToken);

        Response response = getQuotationsRequest.callAPI();
        QuotationWrapperDto quotationWrapperDto = utils.convert.jsonToDto(response, QuotationWrapperDto.class);

        List<QuotationRowDto> orderedCorrectly = orderQuotations(quotationWrapperDto.getQuotationResult(), QuotationComparator.BY_DATE);

        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        var soft = new SoftAssert();
        soft.assertEquals(quotationWrapperDto.getQuotationResult().getTotalItems(), Integer.valueOf(6), RESPONSE_TOTAL_QUOTATIONS_COUNT_MISMATCH_AS_MESSAGE);
        soft.assertEquals(quotationWrapperDto.getQuotationResult().getTotalPages(), Integer.valueOf(1), RESPONSE_TOTAL_PAGES_MISMATCH_AS_MESSAGE);
        soft.assertEquals(quotationWrapperDto.getQuotationResult().getQuotations().size(), 6, RESPONSE_QUOTATIONS_LIST_SIZE_MISMATCH_AS_MESSAGE);
        soft.assertTrue(quotationWrapperDto.getQuotationResult().getQuotations().equals(orderedCorrectly), RESPONSE_QUOTATIONS_ORDERING_MISMATCH_AS_MESSAGE);
        soft.assertNull(quotationWrapperDto.getError());
        soft.assertAll();
    }

    @Test()
    public void getQuotationsWithNoRoleAccessTokenIsForbidden() throws IOException {
        GetQuotationsRequest getQuotationsRequest = new GetQuotationsRequest().withAccessToken(utils.context.getContextItem(NO_ROLE_TOKEN));

        Response response = getQuotationsRequest.callAPI();
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_401, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        ErrorDto errorDto = utils.convert.jsonToDto(response, ErrorDto.class);
        var soft = new SoftAssert();
        soft.assertEquals(errorDto.getError().getStatus(), RESPONSE_STATUS_CODE_401);
        soft.assertEquals(errorDto.getError().getType(), UNAUTHORIZED);
        soft.assertTrue(errorDto.getError().getDetail().contains(USER_DOESNT_HAVE_REQUIRED_PERMISSIONS));
        soft.assertAll();
    }

    @Test()
    public void getQuotationsWithoutAccessTokenIsUnauthorized() {
        GetQuotationsRequest getQuotationsRequest = new GetQuotationsRequest();

        Response response = getQuotationsRequest.callAPI();
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_401, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        var soft = new SoftAssert();
        soft.assertEquals(response.getStatusCode(), RESPONSE_STATUS_CODE_401);
        soft.assertEquals(response.jsonPath().get("error"), "Unauthorized");
        soft.assertTrue(response.jsonPath().get("message").toString().contains("Full authentication is required to access this resource"));
        soft.assertAll();
    }

    @Test(enabled = false)
    public void getQuotationsWithOwnerIdAndMaxPageSizeParamsReturnsAllQuotationsForOwnerInDefaultSortOrder() throws IOException {
        int ownerId = 1;

        GetQuotationsRequest getQuotationsRequest = new GetQuotationsRequest()
                .withPageSize(Integer.MAX_VALUE)
                .withOwnerId(ownerId);

        Response response = getQuotationsRequest.callAPI();
        QuotationResultDto quotationResultDto = utils.convert.jsonToDto(response, QuotationResultDto.class);

        List<QuotationRowDto> orderedCorrectly = orderQuotations(quotationResultDto, QuotationComparator.BY_STATUS_AND_DATE);

        boolean doesOfferNumberMatchForAllQuotations = checkOfferNumber(quotationResultDto, "5100207", "5100213");

        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        var soft = new SoftAssert();
        soft.assertEquals(quotationResultDto.getTotalItems(), Integer.valueOf(8), RESPONSE_TOTAL_QUOTATIONS_COUNT_MISMATCH_AS_MESSAGE);
        soft.assertEquals(quotationResultDto.getTotalPages(), Integer.valueOf(1), RESPONSE_TOTAL_PAGES_MISMATCH_AS_MESSAGE);
        soft.assertEquals(quotationResultDto.getQuotations().size(), 8, RESPONSE_QUOTATIONS_LIST_SIZE_MISMATCH_AS_MESSAGE);
        soft.assertTrue(quotationResultDto.getQuotations().equals(orderedCorrectly), RESPONSE_QUOTATIONS_ORDERING_MISMATCH_AS_MESSAGE);
        soft.assertTrue(doesOfferNumberMatchForAllQuotations);
        soft.assertAll();
    }

    @Test(enabled = false)
    public void getQuotationsWithOfferNumberAndMaxPageSizeParamsReturnsAllQuotationsForOfferNumberInDefaultSortOrder() throws IOException {
        String expectedOfferNumber = "5100209";

        GetQuotationsRequest getQuotationsRequest = new GetQuotationsRequest()
                .withPageSize(Integer.MAX_VALUE)
                .withOfferNumber(expectedOfferNumber);

        Response response = getQuotationsRequest.callAPI();
        QuotationResultDto quotationResultDto = utils.convert.jsonToDto(response, QuotationResultDto.class);

        List<QuotationRowDto> orderedCorrectly = orderQuotations(quotationResultDto, QuotationComparator.BY_STATUS_AND_DATE);

        boolean doesOfferNumberMatchForAllQuotations = checkOfferNumber(quotationResultDto, expectedOfferNumber);

        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        var soft = new SoftAssert();
        soft.assertEquals(quotationResultDto.getTotalItems(), Integer.valueOf(7), RESPONSE_TOTAL_QUOTATIONS_COUNT_MISMATCH_AS_MESSAGE);
        soft.assertEquals(quotationResultDto.getTotalPages(), Integer.valueOf(1), RESPONSE_TOTAL_PAGES_MISMATCH_AS_MESSAGE);
        soft.assertEquals(quotationResultDto.getQuotations().size(), 7, RESPONSE_QUOTATIONS_LIST_SIZE_MISMATCH_AS_MESSAGE);
        soft.assertTrue(quotationResultDto.getQuotations().equals(orderedCorrectly), RESPONSE_QUOTATIONS_ORDERING_MISMATCH_AS_MESSAGE);
        soft.assertTrue(doesOfferNumberMatchForAllQuotations);
        soft.assertAll();
    }

    @Test(enabled = false)
    public void getQuotationsWithVersionAndMaxPageSizeParamsReturnsAllQuotationsForVersionInDefaultSortOrder() throws IOException {
        int expectedVersion = 2;

        GetQuotationsRequest getQuotationsRequest = new GetQuotationsRequest()
                .withPageSize(Integer.MAX_VALUE)
                .withVersion(expectedVersion);

        Response response = getQuotationsRequest.callAPI();
        QuotationResultDto quotationResultDto = utils.convert.jsonToDto(response, QuotationResultDto.class);

        List<QuotationRowDto> orderedCorrectly = orderQuotations(quotationResultDto, QuotationComparator.BY_STATUS_AND_DATE);

        boolean doesVersionMatchForAllQuotations = checkVersion(expectedVersion, quotationResultDto);

        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        var soft = new SoftAssert();
        soft.assertEquals(quotationResultDto.getTotalItems(), Integer.valueOf(9), RESPONSE_TOTAL_QUOTATIONS_COUNT_MISMATCH_AS_MESSAGE);
        soft.assertEquals(quotationResultDto.getTotalPages(), Integer.valueOf(1), RESPONSE_TOTAL_PAGES_MISMATCH_AS_MESSAGE);
        soft.assertEquals(quotationResultDto.getQuotations().size(), 9, RESPONSE_QUOTATIONS_LIST_SIZE_MISMATCH_AS_MESSAGE);
        soft.assertTrue(quotationResultDto.getQuotations().equals(orderedCorrectly), RESPONSE_QUOTATIONS_ORDERING_MISMATCH_AS_MESSAGE);
        soft.assertTrue(doesVersionMatchForAllQuotations);
        soft.assertAll();
    }

    @Test(enabled = false)
    public void getQuotationsWithPositionAndMaxPageSizeParamsReturnsAllQuotationsForPositionInDefaultSortOrder() throws IOException {
        int expectedPosition = 3;

        GetQuotationsRequest getQuotationsRequest = new GetQuotationsRequest()
                .withPageSize(Integer.MAX_VALUE)
                .withPosition(expectedPosition);

        Response response = getQuotationsRequest.callAPI();
        QuotationResultDto quotationResultDto = utils.convert.jsonToDto(response, QuotationResultDto.class);

        List<QuotationRowDto> orderedCorrectly = orderQuotations(quotationResultDto, QuotationComparator.BY_STATUS_AND_DATE);

        boolean doesPositionMatchForAllQuotations = checkPosition(expectedPosition, quotationResultDto);

        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        var soft = new SoftAssert();
        soft.assertEquals(quotationResultDto.getTotalItems(), Integer.valueOf(5), RESPONSE_TOTAL_QUOTATIONS_COUNT_MISMATCH_AS_MESSAGE);
        soft.assertEquals(quotationResultDto.getTotalPages(), Integer.valueOf(1), RESPONSE_TOTAL_PAGES_MISMATCH_AS_MESSAGE);
        soft.assertEquals(quotationResultDto.getQuotations().size(), 5, RESPONSE_QUOTATIONS_LIST_SIZE_MISMATCH_AS_MESSAGE);
        soft.assertTrue(quotationResultDto.getQuotations().equals(orderedCorrectly), RESPONSE_QUOTATIONS_ORDERING_MISMATCH_AS_MESSAGE);
        soft.assertTrue(doesPositionMatchForAllQuotations);
        soft.assertAll();
    }

    @Test(enabled = false)
    public void getQuotationsWithScenarioAndMaxPageSizeParamsReturnsAllQuotationsForScenarioInDefaultSortOrder() throws IOException {
        int expectedScenario = 4;

        GetQuotationsRequest getQuotationsRequest = new GetQuotationsRequest()
                .withPageSize(Integer.MAX_VALUE)
                .withScenario(expectedScenario);

        Response response = getQuotationsRequest.callAPI();
        QuotationResultDto quotationResultDto = utils.convert.jsonToDto(response, QuotationResultDto.class);

        List<QuotationRowDto> orderedCorrectly = orderQuotations(quotationResultDto, QuotationComparator.BY_STATUS_AND_DATE);

        boolean doesScenarioMatchForAllQuotations = checkScenario(expectedScenario, quotationResultDto);

        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        var soft = new SoftAssert();
        soft.assertEquals(quotationResultDto.getTotalItems(), Integer.valueOf(0), RESPONSE_TOTAL_QUOTATIONS_COUNT_MISMATCH_AS_MESSAGE);
        soft.assertEquals(quotationResultDto.getTotalPages(), Integer.valueOf(0), RESPONSE_TOTAL_PAGES_MISMATCH_AS_MESSAGE);
        soft.assertEquals(quotationResultDto.getQuotations().size(), 0, RESPONSE_QUOTATIONS_LIST_SIZE_MISMATCH_AS_MESSAGE);
        soft.assertTrue(quotationResultDto.getQuotations().equals(orderedCorrectly), RESPONSE_QUOTATIONS_ORDERING_MISMATCH_AS_MESSAGE);
        soft.assertTrue(doesScenarioMatchForAllQuotations);
        soft.assertAll();
    }

    @Test(enabled = false)
    public void getQuotationsWithCustomerAndMaxPageSizeParamsReturnsAllQuotationsForCustomerInDefaultSortOrder() throws IOException {
        String expectedCustomer = "DLH";

        GetQuotationsRequest getQuotationsRequest = new GetQuotationsRequest()
                .withPageSize(Integer.MAX_VALUE)
                .withCustomer(expectedCustomer);

        Response response = getQuotationsRequest.callAPI();
        QuotationResultDto quotationResultDto = utils.convert.jsonToDto(response, QuotationResultDto.class);

        List<QuotationRowDto> orderedCorrectly = orderQuotations(quotationResultDto, QuotationComparator.BY_STATUS_AND_DATE);

        boolean doesCustomerMatchForAllQuotations = checkCustomer(expectedCustomer, quotationResultDto);

        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        var soft = new SoftAssert();
        soft.assertEquals(quotationResultDto.getTotalItems(), Integer.valueOf(6), RESPONSE_TOTAL_QUOTATIONS_COUNT_MISMATCH_AS_MESSAGE);
        soft.assertEquals(quotationResultDto.getTotalPages(), Integer.valueOf(1), RESPONSE_TOTAL_PAGES_MISMATCH_AS_MESSAGE);
        soft.assertEquals(quotationResultDto.getQuotations().size(), 6, RESPONSE_QUOTATIONS_LIST_SIZE_MISMATCH_AS_MESSAGE);
        soft.assertTrue(quotationResultDto.getQuotations().equals(orderedCorrectly), RESPONSE_QUOTATIONS_ORDERING_MISMATCH_AS_MESSAGE);
        soft.assertTrue(doesCustomerMatchForAllQuotations);
        soft.assertAll();
    }

    @Test(enabled = false)
    public void getQuotationsWithEngineTypeAndMaxPageSizeParamsReturnsAllQuotationsForEngineTypeInDefaultSortOrder() throws IOException {
        EngineType expectedEngineType = CFM56_5A;

        GetQuotationsRequest getQuotationsRequest = new GetQuotationsRequest()
                .withPageSize(Integer.MAX_VALUE)
                .withEngineType(expectedEngineType);

        Response response = getQuotationsRequest.callAPI();
        QuotationResultDto quotationResultDto = utils.convert.jsonToDto(response, QuotationResultDto.class);

        List<QuotationRowDto> orderedCorrectly = orderQuotations(quotationResultDto, QuotationComparator.BY_STATUS_AND_DATE);

        boolean doesEngineTypeMatchForAllQuotations = checkEngineType(expectedEngineType, quotationResultDto);

        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        var soft = new SoftAssert();
        soft.assertEquals(quotationResultDto.getTotalItems(), Integer.valueOf(5), RESPONSE_TOTAL_QUOTATIONS_COUNT_MISMATCH_AS_MESSAGE);
        soft.assertEquals(quotationResultDto.getTotalPages(), Integer.valueOf(1), RESPONSE_TOTAL_PAGES_MISMATCH_AS_MESSAGE);
        soft.assertEquals(quotationResultDto.getQuotations().size(), 5, RESPONSE_QUOTATIONS_LIST_SIZE_MISMATCH_AS_MESSAGE);
        soft.assertTrue(quotationResultDto.getQuotations().equals(orderedCorrectly), RESPONSE_QUOTATIONS_ORDERING_MISMATCH_AS_MESSAGE);
        soft.assertTrue(doesEngineTypeMatchForAllQuotations);
        soft.assertAll();
    }

    @Test(enabled = false)
    public void getQuotationsWithStatusAndMaxPageSizeParamsReturnsAllQuotationsForStatusInDefaultSortOrder() throws IOException {
        QuotationStatus expectedStatus = TRANSFERRED;

        GetQuotationsRequest getQuotationsRequest = new GetQuotationsRequest()
                .withPageSize(Integer.MAX_VALUE)
                .withStatus(expectedStatus);

        Response response = getQuotationsRequest.callAPI();
        QuotationResultDto quotationResultDto = utils.convert.jsonToDto(response, QuotationResultDto.class);

        List<QuotationRowDto> orderedCorrectly = orderQuotations(quotationResultDto, QuotationComparator.BY_STATUS_AND_DATE);

        boolean doesStatusMatchForAllQuotations = checkStatus(expectedStatus, quotationResultDto);

        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        var soft = new SoftAssert();
        soft.assertEquals(quotationResultDto.getTotalItems(), Integer.valueOf(6), RESPONSE_TOTAL_QUOTATIONS_COUNT_MISMATCH_AS_MESSAGE);
        soft.assertEquals(quotationResultDto.getTotalPages(), Integer.valueOf(1), RESPONSE_TOTAL_PAGES_MISMATCH_AS_MESSAGE);
        soft.assertEquals(quotationResultDto.getQuotations().size(), 6, RESPONSE_QUOTATIONS_LIST_SIZE_MISMATCH_AS_MESSAGE);
        soft.assertTrue(quotationResultDto.getQuotations().equals(orderedCorrectly), RESPONSE_QUOTATIONS_ORDERING_MISMATCH_AS_MESSAGE);
        soft.assertTrue(doesStatusMatchForAllQuotations);
        soft.assertAll();
    }

    @Test(enabled = false)
    public void getQuotationsWithAllFiltersAndMaxPageSizeParamsReturnsAllRelevantQuotationsInDefaultSortOrder() throws IOException {
        String expectedOfferNumber = "5100207";
        String expectedOwner = "Nikolas Barkow";
        int expectedVersion = 1;
        int expectedPosition = 2;
        int expectedScenario = 1;
        String expectedCustomer = "LHT";
        EngineType expectedEngineType = CFM56_5B;
        QuotationStatus expectedStatus = IN_PROGRESS;

        GetQuotationsRequest getQuotationsRequest = new GetQuotationsRequest()
                .withPageSize(Integer.MAX_VALUE)
                .withOfferNumber(expectedOfferNumber)
                .withOwnerId(1)
                .withVersion(expectedVersion)
                .withPosition(expectedPosition)
                .withScenario(expectedScenario)
                .withCustomer(expectedCustomer)
                .withEngineType(expectedEngineType)
                .withStatus(expectedStatus);
        Response response = getQuotationsRequest.callAPI();
        QuotationResultDto quotationResultDto = utils.convert.jsonToDto(response, QuotationResultDto.class);

        List<QuotationRowDto> orderedCorrectly = orderQuotations(quotationResultDto, QuotationComparator.BY_STATUS_AND_DATE);

        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        var soft = new SoftAssert();
        soft.assertEquals(quotationResultDto.getTotalItems(), Integer.valueOf(1), RESPONSE_TOTAL_QUOTATIONS_COUNT_MISMATCH_AS_MESSAGE);
        soft.assertEquals(quotationResultDto.getTotalPages(), Integer.valueOf(1), RESPONSE_TOTAL_PAGES_MISMATCH_AS_MESSAGE);
        soft.assertEquals(quotationResultDto.getQuotations().size(), 1, RESPONSE_QUOTATIONS_LIST_SIZE_MISMATCH_AS_MESSAGE);
        soft.assertTrue(quotationResultDto.getQuotations().equals(orderedCorrectly), RESPONSE_QUOTATIONS_ORDERING_MISMATCH_AS_MESSAGE);
        soft.assertAll();

        soft = new SoftAssert();
        soft.assertTrue(checkOfferNumber(quotationResultDto, expectedOfferNumber), String.format(PROPERTY_DOES_NOT_MATCH_AS_MESSAGE_FORMAT, "Offer number"));
        soft.assertTrue(checkOwner(expectedOwner, quotationResultDto), String.format(PROPERTY_DOES_NOT_MATCH_AS_MESSAGE_FORMAT, "Owner"));
        soft.assertTrue(checkVersion(expectedVersion, quotationResultDto), String.format(PROPERTY_DOES_NOT_MATCH_AS_MESSAGE_FORMAT, "Version"));
        soft.assertTrue(checkPosition(expectedPosition, quotationResultDto), String.format(PROPERTY_DOES_NOT_MATCH_AS_MESSAGE_FORMAT, "Position"));
        soft.assertTrue(checkScenario(expectedScenario, quotationResultDto), String.format(PROPERTY_DOES_NOT_MATCH_AS_MESSAGE_FORMAT, "Scenario"));
        soft.assertTrue(checkEngineType(expectedEngineType, quotationResultDto), String.format(PROPERTY_DOES_NOT_MATCH_AS_MESSAGE_FORMAT, "Engine type"));
        soft.assertTrue(checkCustomer(expectedCustomer, quotationResultDto), String.format(PROPERTY_DOES_NOT_MATCH_AS_MESSAGE_FORMAT, "Customer"));
        soft.assertTrue(checkStatus(expectedStatus, quotationResultDto), String.format(PROPERTY_DOES_NOT_MATCH_AS_MESSAGE_FORMAT, "Status"));
        soft.assertAll();
    }

    @Test(enabled = false)
    public void getQuotationsWithInvalidPaginationParams() throws IOException {
        GetQuotationsRequest getQuotationsRequest = new GetQuotationsRequest()
                .withPageSize(-1)
                .withPageIndex(-1);

        Response response = getQuotationsRequest.callAPI();

        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_400, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
    }

    @Test(enabled = false)
    public void getQuotationsWithInvalidInputFieldParams() throws IOException {
        GetQuotationsRequest getQuotationsRequest = new GetQuotationsRequest()
                .withOfferNumber("111111111")
                .withVersion(-1)
                .withPosition(-1)
                .withScenario(-1);

        Response response = getQuotationsRequest.callAPI();
        ErrorDto errorDto = utils.convert.jsonToDto(response, ErrorDto.class);

        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_400, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        var soft = new SoftAssert();
        soft.assertEquals(errorDto.getError(), BAD_REQUEST_ERROR_MESSAGE);
        soft.assertAll();
    }

    @Test(enabled = false)
    public void getQuotationsWithInvalidDropDownFilterParams() throws IOException {
        GetQuotationsRequest getQuotationsRequest = new GetQuotationsRequest()
                .withOwnerId(0)
                .withCustomer("XXXX")
                .withEngineType(EngineType.INVALID)
                .withStatus(QuotationStatus.INVALID);

        Response response = getQuotationsRequest.callAPI();
        ErrorDto errorDto = utils.convert.jsonToDto(response, ErrorDto.class);

        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_400, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        var soft = new SoftAssert();
        soft.assertEquals(errorDto.getError(), BAD_REQUEST_ERROR_MESSAGE);
        soft.assertAll();
    }

    @Test(enabled = false)
    public void getQuotationsWithInvalidSortByParam() throws IOException {
        GetQuotationsRequest getQuotationsRequest = new GetQuotationsRequest()
                .sortedBy(QuotationSortOption.INVALID);

        Response response = getQuotationsRequest.callAPI();
        ErrorDto errorDto = utils.convert.jsonToDto(response, ErrorDto.class);

        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_400, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        var soft = new SoftAssert();
        soft.assertEquals(errorDto.getError(), BAD_REQUEST_ERROR_MESSAGE);
        soft.assertAll();
    }
}
