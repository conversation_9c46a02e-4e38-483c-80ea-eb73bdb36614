package com.lht.corecalculation.api.subcontractpricing;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.lht.corecalculation.api.handlingcharges.ClusterOrderAssertion;
import com.lht.corecalculation.api.utils.SubcontractPricingUtil;
import com.lht.corecalculation.base.BaseCocaApiTest;
import io.restassured.response.Response;
import java.util.Arrays;
import java.util.List;
import org.testng.Assert;
import org.testng.annotations.Test;


import static com.lht.corecalculation.api.constants.GlobalConstants.ADMIN_TOKEN;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_CFM56_5B_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_LEAP_1A_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_LEAP_1B_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_V2500_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_200;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_USERS_COUNT_MISMATCH_AS_MESSAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.VIEW_ONLY_TOKEN;

public class GetSubcontractPricingApiTests extends BaseCocaApiTest {

    @Test(groups = {"smoke", "workscopeFixedPriceAndNte", "modularFixedPriceAndNte"})
    public void verifySubcontractPricing_getEndpointReturn_StatusCode_200_AdminUser() {
        String adminToken = utils.context.getContextItem(ADMIN_TOKEN);
        Response response = SubcontractPricingUtil.getSubcontractPriceResponseWithBearerToken(ENGINE_LEAP_1A_QUOTATION_ID, adminToken);
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
    }

    @Test()
    public void verifySubcontractPricing_getEndpointReturn_StatusCode_200_ForViewOnlyUser() {
        String adminToken = utils.context.getContextItem(VIEW_ONLY_TOKEN);
        Response response = SubcontractPricingUtil.getSubcontractPriceResponseWithBearerToken(ENGINE_LEAP_1A_QUOTATION_ID, adminToken);
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = {"smoke", "workscopeFixedPriceAndNte", "modularFixedPriceAndNte"})
    public void verifySubcontractPricing_ClusterNames_LEAP_1A_255() throws JsonProcessingException {
        String adminToken = utils.context.getContextItem(ADMIN_TOKEN);
        Response subcontractPricingGetResponse = SubcontractPricingUtil.getSubcontractPriceResponseWithBearerToken(ENGINE_LEAP_1A_QUOTATION_ID, adminToken);
        Assert.assertEquals(subcontractPricingGetResponse.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        List<String> actualSubcontractPriceClusterNames = SubcontractPricingUtil.extractSubcontractPriceClusterNamesFromResponse(subcontractPricingGetResponse);
        List<String> expectedSubcontractPriceClusterNames = Arrays.asList(ClusterOrderAssertion.getSubcontractPriceClusterOrderLeap1a());

        Assert.assertEquals(actualSubcontractPriceClusterNames.size(), expectedSubcontractPriceClusterNames.size(), RESPONSE_USERS_COUNT_MISMATCH_AS_MESSAGE);
        SubcontractPricingUtil.checkForListMismatch(actualSubcontractPriceClusterNames, expectedSubcontractPriceClusterNames);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"})
    public void verifySubcontractPricing_ClusterNames_LEAP_1B() throws JsonProcessingException {
        String adminToken = utils.context.getContextItem(ADMIN_TOKEN);
        Response subcontractPricingGetResponse = SubcontractPricingUtil.getSubcontractPriceResponseWithBearerToken(ENGINE_LEAP_1B_QUOTATION_ID, adminToken);
        Assert.assertEquals(subcontractPricingGetResponse.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        List<String> actualSubcontractPriceClusterNames = SubcontractPricingUtil.extractSubcontractPriceClusterNamesFromResponse(subcontractPricingGetResponse);
        List<String> expectedSubcontractPriceClusterNames = Arrays.asList(SubcontractClusterOrderAssertion.getSubcontractPriceClusterOrderLeap1b());

        Assert.assertEquals(actualSubcontractPriceClusterNames.size(), expectedSubcontractPriceClusterNames.size(), RESPONSE_USERS_COUNT_MISMATCH_AS_MESSAGE);
        SubcontractPricingUtil.checkForListMismatch(actualSubcontractPriceClusterNames, expectedSubcontractPriceClusterNames);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"})
    public void verifySubcontractPricing_ClusterNames_CFM56_5B() throws JsonProcessingException {
        String adminToken = utils.context.getContextItem(ADMIN_TOKEN);
        Response subcontractPricingGetResponse = SubcontractPricingUtil.getSubcontractPriceResponseWithBearerToken(ENGINE_CFM56_5B_QUOTATION_ID, adminToken);
        Assert.assertEquals(subcontractPricingGetResponse.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        List<String> actualSubcontractPriceClusterNames = SubcontractPricingUtil.extractSubcontractPriceClusterNamesFromResponse(subcontractPricingGetResponse);
        List<String> expectedSubcontractPriceClusterNames = Arrays.asList(ClusterOrderAssertion.getSubcontractPriceClusterOrderCfm565B());

        Assert.assertEquals(actualSubcontractPriceClusterNames.size(), expectedSubcontractPriceClusterNames.size(), RESPONSE_USERS_COUNT_MISMATCH_AS_MESSAGE);
        SubcontractPricingUtil.checkForListMismatch(actualSubcontractPriceClusterNames, expectedSubcontractPriceClusterNames);
    }

    @Test(groups = {"smoke", "workscopeFixedPriceAndNte", "modularFixedPriceAndNte"})
    public void verifySubcontractPricing_ClusterNames_V2500() throws JsonProcessingException {
        String adminToken = utils.context.getContextItem(ADMIN_TOKEN);
        Response subcontractPricingGetResponse = SubcontractPricingUtil.getSubcontractPriceResponseWithBearerToken(ENGINE_V2500_QUOTATION_ID, adminToken);
        Assert.assertEquals(subcontractPricingGetResponse.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        List<String> actualSubcontractPriceClusterNames = SubcontractPricingUtil.extractSubcontractPriceClusterNamesFromResponse(subcontractPricingGetResponse);
        List<String> expectedSubcontractPriceClusterNames = Arrays.asList(ClusterOrderAssertion.getSubcontractPriceClusterOrderV2500());

        Assert.assertEquals(actualSubcontractPriceClusterNames.size(), expectedSubcontractPriceClusterNames.size(), RESPONSE_USERS_COUNT_MISMATCH_AS_MESSAGE);
        SubcontractPricingUtil.checkForListMismatch(actualSubcontractPriceClusterNames, expectedSubcontractPriceClusterNames);
    }
}
