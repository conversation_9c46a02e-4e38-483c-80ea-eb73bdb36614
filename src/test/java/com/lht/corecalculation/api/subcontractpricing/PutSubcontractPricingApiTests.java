package com.lht.corecalculation.api.subcontractpricing;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.lht.corecalculation.api.enums.TestUser;
import com.lht.corecalculation.api.pojo.dto.subcontractpricing.SubcontractResponseDto;
import com.lht.corecalculation.api.utils.MessageFormater;
import com.lht.corecalculation.api.utils.SubcontractPricingUtil;
import com.lht.corecalculation.base.BaseCocaApiTest;
import io.restassured.response.Response;
import org.testng.Assert;
import org.testng.annotations.Test;


import static com.lht.corecalculation.api.constants.GlobalConstants.ADMIN_TOKEN;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_CFM56_5B_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_LEAP_1A_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_LEAP_1B_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_V2500_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ERROR_DETAIL;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_200;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_400;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_403;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.USER_DOESNT_HAVE_REQUIRED_PERMISSIONS;
import static com.lht.corecalculation.api.constants.GlobalConstants.VIEW_ONLY_TOKEN;

public class PutSubcontractPricingApiTests extends BaseCocaApiTest {

    private static final Double EXPECTED_MARGIN_NULL = null;
    private static final Integer EXPECTED_CAP_NULL = null;
    private static final Double EXPECTED_VALID_MARGIN_VALUE = 33.3;
    private static final Double INVALID_MARGIN_VALUE = -12.3;
    private static final Integer VALID_CAP_VALUE = 333333333;
    private static final Integer INVALID_CAP_VALUE = -1111;

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "validSubcontractPricesValues")
    public void verifySubcontractPricing_updateMarginAndCap_CFM56_5B(
            Double expectedMargin,
            Integer expectedCap
    ) throws JsonProcessingException {
        String accessToken = utils.context.getContextItem(ADMIN_TOKEN);
        SubcontractPricingUtil.verifySubcontractMarginAndCapForQuotation(ENGINE_CFM56_5B_QUOTATION_ID, expectedMargin, expectedCap, accessToken);
    }

    @Test(groups = {"smoke", "workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "validSubcontractPricesValues")
    public void verifySubcontractPricing_updateMarginAndCap_LEAP_1A(
            Double expectedMargin,
            Integer expectedCap
    ) throws JsonProcessingException {
        String accessToken = utils.context.getContextItem(ADMIN_TOKEN);
        SubcontractPricingUtil.verifySubcontractMarginAndCapForQuotation(ENGINE_LEAP_1A_QUOTATION_ID, expectedMargin, expectedCap, accessToken);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "validSubcontractPricesValues")
    public void verifySubcontractPricing_updateMarginAndCap_LEAP_1B(
            Double expectedMargin,
            Integer expectedCap
    ) throws JsonProcessingException {
        String accessToken = utils.context.getContextItem(ADMIN_TOKEN);
        SubcontractPricingUtil.verifySubcontractMarginAndCapForQuotation(ENGINE_LEAP_1B_QUOTATION_ID, expectedMargin, expectedCap, accessToken);
    }

    @Test(groups = {"smoke", "modularFixedPriceAndNte"}, dataProvider = "validSubcontractPricesValues")
    public void verifySubcontractPricing_updateMarginAndCap_V2500(
            Double expectedMargin,
            Integer expectedCap
    ) throws JsonProcessingException {
        String accessToken = utils.context.getContextItem(ADMIN_TOKEN);
        SubcontractPricingUtil.verifySubcontractMarginAndCapForQuotation(ENGINE_V2500_QUOTATION_ID, expectedMargin, expectedCap, accessToken);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"})
    public void verifySubcontractPricing_updateMarginToNull_LEAP_1A() throws JsonProcessingException {
        String accessToken = utils.context.getContextItem(ADMIN_TOKEN);
        SubcontractPricingUtil.verifySubcontractMarginAndCapForQuotation(ENGINE_LEAP_1A_QUOTATION_ID, EXPECTED_MARGIN_NULL, VALID_CAP_VALUE, accessToken);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"})
    public void verifySubcontractPricing_updateCapToNull_LEAP_1A() throws JsonProcessingException {
        String accessToken = utils.context.getContextItem(ADMIN_TOKEN);
        SubcontractPricingUtil.verifySubcontractMarginAndCapForQuotation(ENGINE_LEAP_1A_QUOTATION_ID, EXPECTED_VALID_MARGIN_VALUE, EXPECTED_CAP_NULL, accessToken);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"})
    public void verifySubcontractPricing_updateMarginToNull_LEAP_1B() throws JsonProcessingException {
        String accessToken = utils.context.getContextItem(ADMIN_TOKEN);
        SubcontractPricingUtil.verifySubcontractMarginAndCapForQuotation(ENGINE_LEAP_1B_QUOTATION_ID, EXPECTED_MARGIN_NULL, VALID_CAP_VALUE, accessToken);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"})
    public void verifySubcontractPricing_updateCapToNull_LEAP_1B() throws JsonProcessingException {
        String accessToken = utils.context.getContextItem(ADMIN_TOKEN);
        SubcontractPricingUtil.verifySubcontractMarginAndCapForQuotation(ENGINE_LEAP_1B_QUOTATION_ID, EXPECTED_VALID_MARGIN_VALUE, EXPECTED_CAP_NULL, accessToken);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"})
    public void verifySubcontractPricing_updateMarginToNull_CFM56_5B() throws JsonProcessingException {
        String accessToken = utils.context.getContextItem(ADMIN_TOKEN);
        SubcontractPricingUtil.verifySubcontractMarginAndCapForQuotation(ENGINE_CFM56_5B_QUOTATION_ID, EXPECTED_MARGIN_NULL, VALID_CAP_VALUE, accessToken);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"})
    public void verifySubcontractPricing_updateCapToNull_CFM56_5B() throws JsonProcessingException {
        String accessToken = utils.context.getContextItem(ADMIN_TOKEN);
        SubcontractPricingUtil.verifySubcontractMarginAndCapForQuotation(ENGINE_CFM56_5B_QUOTATION_ID, EXPECTED_VALID_MARGIN_VALUE, EXPECTED_CAP_NULL, accessToken);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"})
    public void verifySubcontractPricing_updateMarginToNull_V2500() throws JsonProcessingException {
        String accessToken = utils.context.getContextItem(ADMIN_TOKEN);
        SubcontractPricingUtil.verifySubcontractMarginAndCapForQuotation(ENGINE_V2500_QUOTATION_ID, EXPECTED_MARGIN_NULL, VALID_CAP_VALUE, accessToken);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"})
    public void verifySubcontractPricing_updateCapToNull_V2500() throws JsonProcessingException {
        String accessToken = utils.context.getContextItem(ADMIN_TOKEN);
        SubcontractPricingUtil.verifySubcontractMarginAndCapForQuotation(ENGINE_V2500_QUOTATION_ID, EXPECTED_VALID_MARGIN_VALUE, EXPECTED_CAP_NULL, accessToken);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"})
    public void verifySubcontractPricing_updateMarginCannotBeNegativeValue_LEAP_1A() throws JsonProcessingException {
        String adminToken = utils.context.getContextItem(ADMIN_TOKEN);
        Response response = SubcontractPricingUtil.getSubcontractPriceResponseWithBearerToken(ENGINE_LEAP_1A_QUOTATION_ID, adminToken);
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        SubcontractResponseDto subcontractResponseDto = utils.convert.jsonToDto(response, SubcontractResponseDto.class);
        SubcontractPricingUtil.updateSubcontractPricing(ENGINE_LEAP_1A_QUOTATION_ID, subcontractResponseDto, adminToken, INVALID_MARGIN_VALUE, VALID_CAP_VALUE, RESPONSE_STATUS_CODE_400);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"})
    public void verifySubcontractPricing_updateCapCannotBeNegativeValue_V2500() throws JsonProcessingException {
        String adminToken = utils.context.getContextItem(ADMIN_TOKEN);
        Response response = SubcontractPricingUtil.getSubcontractPriceResponseWithBearerToken(ENGINE_V2500_QUOTATION_ID, adminToken);
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        SubcontractResponseDto subcontractResponseDto = utils.convert.jsonToDto(response, SubcontractResponseDto.class);
        SubcontractPricingUtil.updateSubcontractPricing(ENGINE_V2500_QUOTATION_ID, subcontractResponseDto, adminToken, EXPECTED_VALID_MARGIN_VALUE, INVALID_CAP_VALUE, RESPONSE_STATUS_CODE_400);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"})
    public void verifySubcontractPricing_viewOnlyUserCannotUpdateValues_V2500() throws JsonProcessingException {
        String adminToken = utils.context.getContextItem(VIEW_ONLY_TOKEN);
        Response response = SubcontractPricingUtil.getSubcontractPriceResponseWithBearerToken(ENGINE_V2500_QUOTATION_ID, adminToken);
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        SubcontractResponseDto subcontractResponseDto = utils.convert.jsonToDto(response, SubcontractResponseDto.class);
        Response responseAfterUpdate = SubcontractPricingUtil.updateSubcontractPricing(ENGINE_V2500_QUOTATION_ID, subcontractResponseDto, adminToken, EXPECTED_VALID_MARGIN_VALUE, INVALID_CAP_VALUE, RESPONSE_STATUS_CODE_400);
        Assert.assertTrue(responseAfterUpdate.getBody().jsonPath().get(ERROR_DETAIL).toString().contains(MessageFormater.viewOnlyUserCannotModifyData(TestUser.VIEW_ONLY,ENGINE_V2500_QUOTATION_ID)));
    }
}
