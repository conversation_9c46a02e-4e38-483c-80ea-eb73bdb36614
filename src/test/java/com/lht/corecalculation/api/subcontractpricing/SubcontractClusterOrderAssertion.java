package com.lht.corecalculation.api.subcontractpricing;

import static com.lht.corecalculation.api.constants.GlobalConstants.COMPONENTS;
import static com.lht.corecalculation.api.constants.GlobalConstants.FAN_BLADE;
import static com.lht.corecalculation.api.constants.GlobalConstants.HPC_VANE;
import static com.lht.corecalculation.api.constants.GlobalConstants.KITS;
import static com.lht.corecalculation.api.constants.GlobalConstants.ROUTINE_MATERIAL;
import static com.lht.corecalculation.api.handlingcharges.ClusterOrderAssertion.getFilteredClusterOrder;

public class SubcontractClusterOrderAssertion {

    private static final String[] clusterOrder_LEAP_1B = {
            FAN_BLADE,
            "LPC Blade",
            "LPC Vane",
            "LPC OGV",
            HPC_VANE,
            "HPC Blade",
            //"HPC Shroud",
            "CC Liner",
            "CC Dome",
            "HPT Nozzle Stg. 1",
            "HPT Nozzle Stg. 2",
            "HPT Blade Stg. 1",
            "HPT Blade Stg. 2",
            "HPT Shroud",
            "LPT Nozzle Stg. 1",
            "LPT Blade",
            "LPT Nozzle Stg. 2 - 7",
            "LPT Shroud Segments",
            "Main Bearing",
            "Case/Frame",
            "LLP",
            "Other",
            KITS,
            COMPONENTS,
            "Parts Package",
            ROUTINE_MATERIAL,
            "Repair Material"
    };

    public static String[] getSubcontractPriceClusterOrderLeap1b() {
        return getFilteredClusterOrder(clusterOrder_LEAP_1B);
    }
}
