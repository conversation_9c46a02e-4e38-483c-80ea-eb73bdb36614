package com.lht.corecalculation.api.pricingescalation;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.lht.corecalculation.api.pojo.dto.pricingescalation.EscalationInputRequestDto;
import com.lht.corecalculation.api.pojo.dto.pricingescalation.EscalationResponseDto;
import com.lht.corecalculation.api.utils.PricingEscalationUtil;
import com.lht.corecalculation.base.BaseCocaApiTest;
import io.restassured.response.Response;
import org.testng.Assert;
import org.testng.annotations.Test;


import static com.lht.corecalculation.api.constants.GlobalConstants.ADMIN_TOKEN;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_CFM56_5B_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_CFM56_7B_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_LEAP_1A_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_LEAP_1B_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_V2500_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_200;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE;

public class PutPricingEscalationWsFixedAndNteApiTests extends BaseCocaApiTest {

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "validPricingEscalationValuesWithNtePrices")
    public void verifyPricingEscalation_putEndpoint_canEditAllValues_LEAP_1A(
            Double eparPrices,
            Double hcMaterialPrices,
            Double hcSubcontractPrices,
            Double labourPrices,
            Double rfpLabour,
            Double fpNtePrices
    ) throws JsonProcessingException {
        String adminToken = utils.context.getContextItem(ADMIN_TOKEN);
        Response response = PricingEscalationUtil.getEscalationPricingResponseWithBearerToken(ENGINE_LEAP_1A_QUOTATION_ID, adminToken);
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        EscalationResponseDto escalationResponseDto = utils.convert.jsonToDto(response, EscalationResponseDto.class);

        EscalationInputRequestDto escalationInputDto = new EscalationInputRequestDto();

        PricingEscalationUtil.extractIdsAndYears(escalationResponseDto, escalationInputDto);

        PricingEscalationUtil.modifyValuesInEscalationInput(escalationInputDto, eparPrices, hcMaterialPrices, hcSubcontractPrices, labourPrices, rfpLabour, fpNtePrices);

        PricingEscalationUtil.assertExpectedAndActualPricingEscalationValues(ENGINE_LEAP_1A_QUOTATION_ID, adminToken, escalationInputDto, eparPrices, hcMaterialPrices, hcSubcontractPrices, labourPrices, rfpLabour, fpNtePrices);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "validPricingEscalationValuesWithNtePrices")
    public void verifyPricingEscalation_putEndpoint_canEditAllValues_LEAP_1B(
            Double eparPrices,
            Double hcMaterialPrices,
            Double hcSubcontractPrices,
            Double labourPrices,
            Double rfpLabour,
            Double fpNtePrices
    ) throws JsonProcessingException {
        String adminToken = utils.context.getContextItem(ADMIN_TOKEN);
        Response response = PricingEscalationUtil.getEscalationPricingResponseWithBearerToken(ENGINE_LEAP_1B_QUOTATION_ID, adminToken);
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        EscalationResponseDto escalationResponseDto = utils.convert.jsonToDto(response, EscalationResponseDto.class);

        EscalationInputRequestDto escalationInputDto = new EscalationInputRequestDto();

        PricingEscalationUtil.extractIdsAndYears(escalationResponseDto, escalationInputDto);

        PricingEscalationUtil.modifyValuesInEscalationInput(escalationInputDto, eparPrices, hcMaterialPrices, hcSubcontractPrices, labourPrices, rfpLabour, fpNtePrices);

        PricingEscalationUtil.assertExpectedAndActualPricingEscalationValues(ENGINE_LEAP_1B_QUOTATION_ID, adminToken, escalationInputDto, eparPrices, hcMaterialPrices, hcSubcontractPrices, labourPrices, rfpLabour, fpNtePrices);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "validPricingEscalationValuesWithNtePrices")
    public void verifyPricingEscalation_putEndpoint_canEditAllValues_V2500(
            Double eparPrices,
            Double hcMaterialPrices,
            Double hcSubcontractPrices,
            Double labourPrices,
            Double rfpLabour,
            Double fpNtePrices
    ) throws JsonProcessingException {
        String adminToken = utils.context.getContextItem(ADMIN_TOKEN);
        Response response = PricingEscalationUtil.getEscalationPricingResponseWithBearerToken(ENGINE_V2500_QUOTATION_ID, adminToken);
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        EscalationResponseDto escalationResponseDto = utils.convert.jsonToDto(response, EscalationResponseDto.class);

        EscalationInputRequestDto escalationInputDto = new EscalationInputRequestDto();

        PricingEscalationUtil.extractIdsAndYears(escalationResponseDto, escalationInputDto);

        PricingEscalationUtil.modifyValuesInEscalationInput(escalationInputDto, eparPrices, hcMaterialPrices, hcSubcontractPrices, labourPrices, rfpLabour, fpNtePrices);

        PricingEscalationUtil.assertExpectedAndActualPricingEscalationValues(ENGINE_V2500_QUOTATION_ID, adminToken, escalationInputDto, eparPrices, hcMaterialPrices, hcSubcontractPrices, labourPrices, rfpLabour, fpNtePrices);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "validPricingEscalationValuesWithNtePrices")
    public void verifyPricingEscalation_putEndpoint_canEditAllValues_CFM56_5B(
            Double eparPrices,
            Double hcMaterialPrices,
            Double hcSubcontractPrices,
            Double labourPrices,
            Double rfpLabour,
            Double fpNtePrices
    ) throws JsonProcessingException {
        String adminToken = utils.context.getContextItem(ADMIN_TOKEN);
        Response response = PricingEscalationUtil.getEscalationPricingResponseWithBearerToken(ENGINE_CFM56_5B_QUOTATION_ID, adminToken);
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        EscalationResponseDto escalationResponseDto = utils.convert.jsonToDto(response, EscalationResponseDto.class);

        EscalationInputRequestDto escalationInputDto = new EscalationInputRequestDto();

        PricingEscalationUtil.extractIdsAndYears(escalationResponseDto, escalationInputDto);

        PricingEscalationUtil.modifyValuesInEscalationInput(escalationInputDto, eparPrices, hcMaterialPrices, hcSubcontractPrices, labourPrices, rfpLabour, fpNtePrices);

        PricingEscalationUtil.assertExpectedAndActualPricingEscalationValues(ENGINE_CFM56_5B_QUOTATION_ID, adminToken, escalationInputDto, eparPrices, hcMaterialPrices, hcSubcontractPrices, labourPrices, rfpLabour, fpNtePrices);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "validPricingEscalationValuesWithNtePrices")
    public void verifyPricingEscalation_putEndpoint_canEditAllValues_CFM56_7B(
            Double eparPrices,
            Double hcMaterialPrices,
            Double hcSubcontractPrices,
            Double labourPrices,
            Double rfpLabour,
            Double fpNtePrices
    ) throws JsonProcessingException {
        String adminToken = utils.context.getContextItem(ADMIN_TOKEN);
        Response response = PricingEscalationUtil.getEscalationPricingResponseWithBearerToken(ENGINE_CFM56_7B_QUOTATION_ID, adminToken);
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        EscalationResponseDto escalationResponseDto = utils.convert.jsonToDto(response, EscalationResponseDto.class);

        EscalationInputRequestDto escalationInputDto = new EscalationInputRequestDto();

        PricingEscalationUtil.extractIdsAndYears(escalationResponseDto, escalationInputDto);

        PricingEscalationUtil.modifyValuesInEscalationInput(escalationInputDto, eparPrices, hcMaterialPrices, hcSubcontractPrices, labourPrices, rfpLabour, fpNtePrices);

        PricingEscalationUtil.assertExpectedAndActualPricingEscalationValues(ENGINE_CFM56_7B_QUOTATION_ID, adminToken, escalationInputDto, eparPrices, hcMaterialPrices, hcSubcontractPrices, labourPrices, rfpLabour, fpNtePrices);
    }
}
