package com.lht.corecalculation.api.pricingescalation;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.lht.corecalculation.api.enums.TestUser;
import com.lht.corecalculation.api.pojo.dto.pricingescalation.EscalationInputRequestDto;
import com.lht.corecalculation.api.pojo.dto.pricingescalation.EscalationResponseDto;
import com.lht.corecalculation.api.request.pricingescalation.PutPricingEscalationRequest;
import com.lht.corecalculation.api.utils.MessageFormater;
import com.lht.corecalculation.api.utils.PricingEscalationUtil;
import com.lht.corecalculation.base.BaseCocaApiTest;
import io.restassured.response.Response;
import org.testng.Assert;
import org.testng.annotations.Test;


import static com.lht.corecalculation.api.constants.GlobalConstants.ADMIN_TOKEN;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_CFM56_5B_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_LEAP_1A_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_LEAP_1B_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_V2500_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ERROR_DETAIL;
import static com.lht.corecalculation.api.constants.GlobalConstants.QUOTATION_OWNERSHIP_CHANGED;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_200;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_400;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_403;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.USER_1_TOKEN;
import static com.lht.corecalculation.api.constants.GlobalConstants.USER_DOES_NOT_MATCH_PROJECT_OWNER;
import static com.lht.corecalculation.api.constants.GlobalConstants.VIEW_ONLY_TOKEN;

public class PutPricingEscalationApiTests extends BaseCocaApiTest {

    @Test()
    public void verifyPricingEscalation_putEndpointReturn_StatusCode_400_WithoutExpectedBody() throws JsonProcessingException {
        String adminToken = utils.context.getContextItem(ADMIN_TOKEN);
        Response response = PricingEscalationUtil.getEscalationPricingResponseWithBearerToken(ENGINE_LEAP_1A_QUOTATION_ID, adminToken);
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        PutPricingEscalationRequest putPricingEscalationRequest = new PutPricingEscalationRequest(
                ENGINE_LEAP_1A_QUOTATION_ID,
                utils.convert.dtoToJsonString("body of request"))
                .withBearerToken(adminToken);
        Response responsePutPricingEscalation = putPricingEscalationRequest.callAPI();
        Assert.assertEquals(responsePutPricingEscalation.statusCode(), RESPONSE_STATUS_CODE_400, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
    }

    @Test(dataProvider = "validPricingEscalationValues")
    public void verifyPricingEscalation_putEndpoint_nonOwnerUser_cannotEditValues_LEAP_1A(
            Double eparPrices,
            Double hcMaterialPrices,
            Double hcSubcontractPrices,
            Double labourPrices,
            Double rfpLabour
    ) throws JsonProcessingException {
        String adminToken = utils.context.getContextItem(USER_1_TOKEN);
        Response response = PricingEscalationUtil.getEscalationPricingResponseWithBearerToken(ENGINE_LEAP_1A_QUOTATION_ID, adminToken);
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        EscalationResponseDto escalationResponseDto = utils.convert.jsonToDto(response, EscalationResponseDto.class);

        EscalationInputRequestDto escalationInputDto = new EscalationInputRequestDto();
        PricingEscalationUtil.extractIdsAndYears(escalationResponseDto, escalationInputDto);

        PricingEscalationUtil.modifyValuesInEscalationInput(escalationInputDto, eparPrices, hcMaterialPrices, hcSubcontractPrices, labourPrices, rfpLabour);

        Response responsePutPricingEscalation = PricingEscalationUtil.putEscalationPricingResponseWithBearerToken(ENGINE_LEAP_1A_QUOTATION_ID, adminToken, escalationInputDto);
        Assert.assertEquals(responsePutPricingEscalation.statusCode(), RESPONSE_STATUS_CODE_403, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
        Assert.assertTrue(responsePutPricingEscalation.getBody().jsonPath().get(ERROR_DETAIL).toString().contains(QUOTATION_OWNERSHIP_CHANGED));
    }

    @Test(dataProvider = "validPricingEscalationValues")
    public void verifyPricingEscalation_putEndpoint_viewOnlyUser_cannotEditValues_CFM56_5B(
            Double eparPrices,
            Double hcMaterialPrices,
            Double hcSubcontractPrices,
            Double labourPrices,
            Double rfpLabour
    ) throws JsonProcessingException {
        String adminToken = utils.context.getContextItem(VIEW_ONLY_TOKEN);
        Response response = PricingEscalationUtil.getEscalationPricingResponseWithBearerToken(ENGINE_CFM56_5B_QUOTATION_ID, adminToken);
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        EscalationResponseDto escalationResponseDto = utils.convert.jsonToDto(response, EscalationResponseDto.class);

        EscalationInputRequestDto escalationInputDto = new EscalationInputRequestDto();
        PricingEscalationUtil.extractIdsAndYears(escalationResponseDto, escalationInputDto);

        PricingEscalationUtil.modifyValuesInEscalationInput(escalationInputDto, eparPrices, hcMaterialPrices, hcSubcontractPrices, labourPrices, rfpLabour);

        Response responsePutPricingEscalation = PricingEscalationUtil.putEscalationPricingResponseWithBearerToken(ENGINE_CFM56_5B_QUOTATION_ID, adminToken, escalationInputDto);
        Assert.assertEquals(responsePutPricingEscalation.statusCode(), RESPONSE_STATUS_CODE_400, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
    }

    @Test(dataProvider = "validPricingEscalationValues")
    public void verifyPricingEscalation_putEndpoint_canEditAllValues_LEAP_1A(
            Double eparPrices,
            Double hcMaterialPrices,
            Double hcSubcontractPrices,
            Double labourPrices,
            Double rfpLabour
    ) throws JsonProcessingException {
        String adminToken = utils.context.getContextItem(ADMIN_TOKEN);
        Response response = PricingEscalationUtil.getEscalationPricingResponseWithBearerToken(ENGINE_LEAP_1A_QUOTATION_ID, adminToken);
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        EscalationResponseDto escalationResponseDto = utils.convert.jsonToDto(response, EscalationResponseDto.class);

        EscalationInputRequestDto escalationInputDto = new EscalationInputRequestDto();
        PricingEscalationUtil.extractIdsAndYears(escalationResponseDto, escalationInputDto);

        PricingEscalationUtil.modifyValuesInEscalationInput(escalationInputDto, eparPrices, hcMaterialPrices, hcSubcontractPrices, labourPrices, rfpLabour);

        PricingEscalationUtil.assertExpectedAndActualPricingEscalationValues(ENGINE_LEAP_1A_QUOTATION_ID, adminToken, escalationInputDto, eparPrices, hcMaterialPrices, hcSubcontractPrices, labourPrices, rfpLabour);
    }

    @Test(dataProvider = "validPricingEscalationValues")
    public void verifyPricingEscalation_putEndpoint_canEditAllValues_LEAP_1B(
            Double eparPrices,
            Double hcMaterialPrices,
            Double hcSubcontractPrices,
            Double labourPrices,
            Double rfpLabour
    ) throws JsonProcessingException {
        String adminToken = utils.context.getContextItem(ADMIN_TOKEN);
        Response response = PricingEscalationUtil.getEscalationPricingResponseWithBearerToken(ENGINE_LEAP_1B_QUOTATION_ID, adminToken);
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        EscalationResponseDto escalationResponseDto = utils.convert.jsonToDto(response, EscalationResponseDto.class);

        EscalationInputRequestDto escalationInputDto = new EscalationInputRequestDto();
        PricingEscalationUtil.extractIdsAndYears(escalationResponseDto, escalationInputDto);

        PricingEscalationUtil.modifyValuesInEscalationInput(escalationInputDto, eparPrices, hcMaterialPrices, hcSubcontractPrices, labourPrices, rfpLabour);

        PricingEscalationUtil.assertExpectedAndActualPricingEscalationValues(ENGINE_LEAP_1B_QUOTATION_ID, adminToken, escalationInputDto, eparPrices, hcMaterialPrices, hcSubcontractPrices, labourPrices, rfpLabour);
    }

    @Test(dataProvider = "validPricingEscalationValues")
    public void verifyPricingEscalation_putEndpoint_canEditAllValues_CFM56_5B(
            Double eparPrices,
            Double hcMaterialPrices,
            Double hcSubcontractPrices,
            Double labourPrices,
            Double rfpLabour
    ) throws JsonProcessingException {
        String adminToken = utils.context.getContextItem(ADMIN_TOKEN);
        Response response = PricingEscalationUtil.getEscalationPricingResponseWithBearerToken(ENGINE_CFM56_5B_QUOTATION_ID, adminToken);
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        EscalationResponseDto escalationResponseDto = utils.convert.jsonToDto(response, EscalationResponseDto.class);

        EscalationInputRequestDto escalationInputDto = new EscalationInputRequestDto();
        PricingEscalationUtil.extractIdsAndYears(escalationResponseDto, escalationInputDto);

        PricingEscalationUtil.modifyValuesInEscalationInput(escalationInputDto, eparPrices, hcMaterialPrices, hcSubcontractPrices, labourPrices, rfpLabour);

        PricingEscalationUtil.assertExpectedAndActualPricingEscalationValues(ENGINE_CFM56_5B_QUOTATION_ID, adminToken, escalationInputDto, eparPrices, hcMaterialPrices, hcSubcontractPrices, labourPrices, rfpLabour);
    }

    @Test(groups = "smoke", dataProvider = "validPricingEscalationValues")
    public void verifyPricingEscalation_putEndpoint_canEditAllValues_V2500(
            Double eparPrices,
            Double hcMaterialPrices,
            Double hcSubcontractPrices,
            Double labourPrices,
            Double rfpLabour
    ) throws JsonProcessingException {
        String adminToken = utils.context.getContextItem(ADMIN_TOKEN);
        Response response = PricingEscalationUtil.getEscalationPricingResponseWithBearerToken(ENGINE_V2500_QUOTATION_ID, adminToken);
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        EscalationResponseDto escalationResponseDto = utils.convert.jsonToDto(response, EscalationResponseDto.class);

        EscalationInputRequestDto escalationInputDto = new EscalationInputRequestDto();
        PricingEscalationUtil.extractIdsAndYears(escalationResponseDto, escalationInputDto);

        PricingEscalationUtil.modifyValuesInEscalationInput(escalationInputDto, eparPrices, hcMaterialPrices, hcSubcontractPrices, labourPrices, rfpLabour);

        PricingEscalationUtil.assertExpectedAndActualPricingEscalationValues(ENGINE_V2500_QUOTATION_ID, adminToken, escalationInputDto, eparPrices, hcMaterialPrices, hcSubcontractPrices, labourPrices, rfpLabour);
    }
}
