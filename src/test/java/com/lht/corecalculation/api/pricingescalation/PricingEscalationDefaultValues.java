package com.lht.corecalculation.api.pricingescalation;

import java.util.Arrays;
import java.util.List;

public class PricingEscalationDefaultValues {

    static final List<Double> labourPrices_V2500 = Arrays.asList(0.0, 0.0, 0.0, 0.0, 0.0);
    static final List<Double> eparPrice_V2500 = Arrays.asList(5.0, 5.0, 5.0, 5.0, 5.0);
    static final List<Double> rfpLabour_V2500 = Arrays.asList(0.0, 0.0, 0.0, 0.0, 0.0);
    static final List<Double> hcMaterialPrices_V2500 = Arrays.asList(9.64, 8.9, 6.98, 6.98, 6.98);
    static final List<Double> hcSubcontractPrices_V2500 = Arrays.asList(8.7, 6.0, 6.0, 6.0, 6.0);

    static final List<Double> labourPrices_LEAP_1A = Arrays.asList(0.0, 0.0, 0.0, 0.0, 0.0);
    static final List<Double> eparPrice_LEAP_1A = Arrays.asList(5.0, 5.0, 5.0, 5.0, 5.0);
    static final List<Double> rfpLabour_LEAP_1A = Arrays.asList(0.0, 0.0, 0.0, 0.0, 0.0);
    static final List<Double> hcMaterialPrices_LEAP_1A = Arrays.asList(9.53, 6.73, 6.88, 6.88, 6.88);
    static final List<Double> hcSubcontractPrices_LEAP_1A = Arrays.asList(8.7, 6.0, 6.0, 6.0, 6.0);

    static final List<Double> labourPrices_LEAP_1B = Arrays.asList(0.0, 0.0, 0.0);
    static final List<Double> eparPrice_LEAP_1B = Arrays.asList(5.0, 5.0, 5.0);
    static final List<Double> rfpLabour_LEAP_1B = Arrays.asList(0.0, 0.0, 0.0);
    static final List<Double> hcMaterialPrices_LEAP_1B = Arrays.asList(9.23, 6.20, 6.42);
    static final List<Double> hcSubcontractPrices_LEAP_1B = Arrays.asList(8.7, 6.0, 6.0);

    static final List<Double> labourPrices_CFM56_5B = Arrays.asList(0.0, 0.0);
    static final List<Double> eparPrice_CFM56_5B = Arrays.asList(5.0, 5.0);
    static final List<Double> rfpLabour_CFM56_5B = Arrays.asList(0.0, 0.0);
    static final List<Double> hcMaterialPrices_CFM56_5B = Arrays.asList(6.28, 6.43);
    static final List<Double> hcSubcontractPrices_CFM56_5B = Arrays.asList(6.0, 6.0);
}
