package com.lht.corecalculation.api.pricingescalation;

import com.lht.corecalculation.api.utils.PricingEscalationUtil;
import com.lht.corecalculation.base.BaseCocaApiTest;
import io.restassured.response.Response;
import java.io.IOException;
import java.util.List;
import org.testng.Assert;
import org.testng.annotations.Test;


import static com.lht.corecalculation.api.constants.GlobalConstants.ADMIN_TOKEN;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_CFM56_5B_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_LEAP_1A_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_LEAP_1B_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_V2500_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.EPAR_PRICES_VALUE_MISMATCH;
import static com.lht.corecalculation.api.constants.GlobalConstants.ERROR_DETAILS_MISMATCH_AS_MESSAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.HC_MATERIAL_PRICES_VALUE_MISMATCH;
import static com.lht.corecalculation.api.constants.GlobalConstants.HC_SUBCONTRACT_PRICES_VALUE_MISMATCH;
import static com.lht.corecalculation.api.constants.GlobalConstants.LABOUR_PRICES_VALUE_MISMATCH;
import static com.lht.corecalculation.api.constants.GlobalConstants.NO_ROLE_TOKEN;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_200;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_400;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_401;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_403;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.RFP_LABOUR_VALUE_MISMATCH;
import static com.lht.corecalculation.api.constants.GlobalConstants.USER_DOESNT_HAVE_REQUIRED_PERMISSIONS;
import static com.lht.corecalculation.api.constants.GlobalConstants.VIEW_ONLY_TOKEN;

public class GetPricingEscalationApiTests extends BaseCocaApiTest {

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"})
    public void verifyPricingEscalation_getEndpointReturn_StatusCode_200_AdminUser() {
        String adminToken = utils.context.getContextItem(ADMIN_TOKEN);
        Response response = PricingEscalationUtil.getEscalationPricingResponseWithBearerToken(ENGINE_CFM56_5B_QUOTATION_ID, adminToken);
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"})
    public void verifyPricingEscalation_getEndpointReturn_StatusCode_200_ViewOnlyUser() {
        String adminToken = utils.context.getContextItem(VIEW_ONLY_TOKEN);
        Response response = PricingEscalationUtil.getEscalationPricingResponseWithBearerToken(ENGINE_CFM56_5B_QUOTATION_ID, adminToken);
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"})
    public void verifyPricingEscalation_getEndpointReturn_StatusCode_403_NO_ROLE_TOKEN() {
        String adminToken = utils.context.getContextItem(NO_ROLE_TOKEN);
        Response response = PricingEscalationUtil.getEscalationPricingResponseWithBearerToken(ENGINE_CFM56_5B_QUOTATION_ID, adminToken);
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_401, USER_DOESNT_HAVE_REQUIRED_PERMISSIONS);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"})
    public void verifyPricingEscalation_getEndpointReturn_allYearWithoutTheBaseYear_LEAP_1A() throws IOException {
        String adminToken = utils.context.getContextItem(ADMIN_TOKEN);
        Response response = PricingEscalationUtil.getEscalationPricingResponseWithBearerToken(ENGINE_LEAP_1A_QUOTATION_ID, adminToken);
        List<String> actualYearsInQuotation = PricingEscalationUtil.extractActualYearsWithoutBaseYear(adminToken, ENGINE_LEAP_1A_QUOTATION_ID);

        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
        Assert.assertEquals(actualYearsInQuotation, PricingEscalationUtil.extractYearsFromResponse(response), ERROR_DETAILS_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"})
    public void verifyPricingEscalation_getEndpointReturn_allYearWithoutTheBaseYear_LEAP_1B() throws IOException {
        String adminToken = utils.context.getContextItem(ADMIN_TOKEN);
        Response response = PricingEscalationUtil.getEscalationPricingResponseWithBearerToken(ENGINE_LEAP_1B_QUOTATION_ID, adminToken);
        List<String> actualYearsInQuotation = PricingEscalationUtil.extractActualYearsWithoutBaseYear(adminToken, ENGINE_LEAP_1B_QUOTATION_ID);

        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
        Assert.assertEquals(actualYearsInQuotation, PricingEscalationUtil.extractYearsFromResponse(response), ERROR_DETAILS_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"})
    public void verifyPricingEscalation_getEndpointReturn_allYearWithoutTheBaseYear_CFM56_5B() throws IOException {
        String adminToken = utils.context.getContextItem(ADMIN_TOKEN);
        Response response = PricingEscalationUtil.getEscalationPricingResponseWithBearerToken(ENGINE_CFM56_5B_QUOTATION_ID, adminToken);
        List<String> actualYearsInQuotation = PricingEscalationUtil.extractActualYearsWithoutBaseYear(adminToken, ENGINE_CFM56_5B_QUOTATION_ID);

        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
        Assert.assertEquals(actualYearsInQuotation, PricingEscalationUtil.extractYearsFromResponse(response), ERROR_DETAILS_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = {"smoke", "workscopeFixedPriceAndNte", "modularFixedPriceAndNte"})
    public void verifyPricingEscalation_getEndpointReturn_allYearWithoutTheBaseYear_V2500() throws IOException {
        String adminToken = utils.context.getContextItem(ADMIN_TOKEN);
        Response response = PricingEscalationUtil.getEscalationPricingResponseWithBearerToken(ENGINE_V2500_QUOTATION_ID, adminToken);
        List<String> actualYearsInQuotation = PricingEscalationUtil.extractActualYearsWithoutBaseYear(adminToken, ENGINE_V2500_QUOTATION_ID);

        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
        Assert.assertEquals(actualYearsInQuotation, PricingEscalationUtil.extractYearsFromResponse(response), ERROR_DETAILS_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"})
    public void verifyPricingEscalation_getEndpointReturn_DefaultValues_V2500() throws IOException {
        String adminToken = utils.context.getContextItem(ADMIN_TOKEN);
        Response response = PricingEscalationUtil.getEscalationPricingResponseWithBearerToken(ENGINE_V2500_QUOTATION_ID, adminToken);
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        List<Double> labourPricesActualDefaultValue = PricingEscalationUtil.extractValuesFromResponse(response, dto -> dto.getValues().getLabourPrices().getDefaultValue());
        List<Double> eparPricesActualDefaultValue = PricingEscalationUtil.extractValuesFromResponse(response, dto -> dto.getValues().getEparPrices().getDefaultValue());
        List<Double> rfpLabourActualDefaultValue = PricingEscalationUtil.extractValuesFromResponse(response, dto -> dto.getValues().getRfpLabour().getDefaultValue());
        List<Double> hcMaterialPricesActualDefaultValue = PricingEscalationUtil.extractValuesFromResponse(response, dto -> dto.getValues().getHcMaterialPrices().getDefaultValue());
        List<Double> hcSubcontractPricesActualDefaultValue = PricingEscalationUtil.extractValuesFromResponse(response, dto -> dto.getValues().getHcSubcontractPrices().getDefaultValue());

        Assert.assertEquals(labourPricesActualDefaultValue, PricingEscalationDefaultValues.labourPrices_V2500, LABOUR_PRICES_VALUE_MISMATCH);
        Assert.assertEquals(eparPricesActualDefaultValue, PricingEscalationDefaultValues.eparPrice_V2500, EPAR_PRICES_VALUE_MISMATCH);
        Assert.assertEquals(rfpLabourActualDefaultValue, PricingEscalationDefaultValues.rfpLabour_V2500, RFP_LABOUR_VALUE_MISMATCH);
        Assert.assertEquals(hcMaterialPricesActualDefaultValue, PricingEscalationDefaultValues.hcMaterialPrices_V2500, HC_MATERIAL_PRICES_VALUE_MISMATCH);
        Assert.assertEquals(hcSubcontractPricesActualDefaultValue, PricingEscalationDefaultValues.hcSubcontractPrices_V2500, HC_SUBCONTRACT_PRICES_VALUE_MISMATCH);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"})
    public void verifyPricingEscalation_getEndpointReturn_DefaultValues_LEAP_1A() throws IOException {
        String adminToken = utils.context.getContextItem(ADMIN_TOKEN);
        Response response = PricingEscalationUtil.getEscalationPricingResponseWithBearerToken(ENGINE_LEAP_1A_QUOTATION_ID, adminToken);
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        List<Double> labourPricesActualDefaultValue = PricingEscalationUtil.extractValuesFromResponse(response, dto -> dto.getValues().getLabourPrices().getDefaultValue());
        List<Double> eparPricesActualDefaultValue = PricingEscalationUtil.extractValuesFromResponse(response, dto -> dto.getValues().getEparPrices().getDefaultValue());
        List<Double> rfpLabourActualDefaultValue = PricingEscalationUtil.extractValuesFromResponse(response, dto -> dto.getValues().getRfpLabour().getDefaultValue());
        List<Double> hcMaterialPricesActualDefaultValue = PricingEscalationUtil.extractValuesFromResponse(response, dto -> dto.getValues().getHcMaterialPrices().getDefaultValue());
        List<Double> hcSubcontractPricesActualDefaultValue = PricingEscalationUtil.extractValuesFromResponse(response, dto -> dto.getValues().getHcSubcontractPrices().getDefaultValue());

        Assert.assertEquals(labourPricesActualDefaultValue, PricingEscalationDefaultValues.labourPrices_LEAP_1A, LABOUR_PRICES_VALUE_MISMATCH);
        Assert.assertEquals(eparPricesActualDefaultValue, PricingEscalationDefaultValues.eparPrice_LEAP_1A, EPAR_PRICES_VALUE_MISMATCH);
        Assert.assertEquals(rfpLabourActualDefaultValue, PricingEscalationDefaultValues.rfpLabour_LEAP_1A, RFP_LABOUR_VALUE_MISMATCH);
        Assert.assertEquals(hcMaterialPricesActualDefaultValue, PricingEscalationDefaultValues.hcMaterialPrices_LEAP_1A, HC_MATERIAL_PRICES_VALUE_MISMATCH);
        Assert.assertEquals(hcSubcontractPricesActualDefaultValue, PricingEscalationDefaultValues.hcSubcontractPrices_LEAP_1A, HC_SUBCONTRACT_PRICES_VALUE_MISMATCH);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"})
    public void verifyPricingEscalation_getEndpointReturn_DefaultValues_LEAP_1B() throws IOException {
        String adminToken = utils.context.getContextItem(ADMIN_TOKEN);
        Response response = PricingEscalationUtil.getEscalationPricingResponseWithBearerToken(ENGINE_LEAP_1B_QUOTATION_ID, adminToken);
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        List<Double> labourPricesActualDefaultValue = PricingEscalationUtil.extractValuesFromResponse(response, dto -> dto.getValues().getLabourPrices().getDefaultValue());
        List<Double> eparPricesActualDefaultValue = PricingEscalationUtil.extractValuesFromResponse(response, dto -> dto.getValues().getEparPrices().getDefaultValue());
        List<Double> rfpLabourActualDefaultValue = PricingEscalationUtil.extractValuesFromResponse(response, dto -> dto.getValues().getRfpLabour().getDefaultValue());
        List<Double> hcMaterialPricesActualDefaultValue = PricingEscalationUtil.extractValuesFromResponse(response, dto -> dto.getValues().getHcMaterialPrices().getDefaultValue());
        List<Double> hcSubcontractPricesActualDefaultValue = PricingEscalationUtil.extractValuesFromResponse(response, dto -> dto.getValues().getHcSubcontractPrices().getDefaultValue());

        Assert.assertEquals(labourPricesActualDefaultValue, PricingEscalationDefaultValues.labourPrices_LEAP_1B, LABOUR_PRICES_VALUE_MISMATCH);
        Assert.assertEquals(eparPricesActualDefaultValue, PricingEscalationDefaultValues.eparPrice_LEAP_1B, EPAR_PRICES_VALUE_MISMATCH);
        Assert.assertEquals(rfpLabourActualDefaultValue, PricingEscalationDefaultValues.rfpLabour_LEAP_1B, RFP_LABOUR_VALUE_MISMATCH);
        Assert.assertEquals(hcMaterialPricesActualDefaultValue, PricingEscalationDefaultValues.hcMaterialPrices_LEAP_1B, HC_MATERIAL_PRICES_VALUE_MISMATCH);
        Assert.assertEquals(hcSubcontractPricesActualDefaultValue, PricingEscalationDefaultValues.hcSubcontractPrices_LEAP_1B, HC_SUBCONTRACT_PRICES_VALUE_MISMATCH);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"})
    public void verifyPricingEscalation_getEndpointReturn_DefaultValues_CFM56_5B() throws IOException {
        String adminToken = utils.context.getContextItem(ADMIN_TOKEN);
        Response response = PricingEscalationUtil.getEscalationPricingResponseWithBearerToken(ENGINE_CFM56_5B_QUOTATION_ID, adminToken);
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);

        List<Double> labourPricesActualDefaultValue = PricingEscalationUtil.extractValuesFromResponse(response, dto -> dto.getValues().getLabourPrices().getDefaultValue());
        List<Double> eparPricesActualDefaultValue = PricingEscalationUtil.extractValuesFromResponse(response, dto -> dto.getValues().getEparPrices().getDefaultValue());
        List<Double> rfpLabourActualDefaultValue = PricingEscalationUtil.extractValuesFromResponse(response, dto -> dto.getValues().getRfpLabour().getDefaultValue());
        List<Double> hcMaterialPricesActualDefaultValue = PricingEscalationUtil.extractValuesFromResponse(response, dto -> dto.getValues().getHcMaterialPrices().getDefaultValue());
        List<Double> hcSubcontractPricesActualDefaultValue = PricingEscalationUtil.extractValuesFromResponse(response, dto -> dto.getValues().getHcSubcontractPrices().getDefaultValue());

        Assert.assertEquals(labourPricesActualDefaultValue, PricingEscalationDefaultValues.labourPrices_CFM56_5B, LABOUR_PRICES_VALUE_MISMATCH);
        Assert.assertEquals(eparPricesActualDefaultValue, PricingEscalationDefaultValues.eparPrice_CFM56_5B, EPAR_PRICES_VALUE_MISMATCH);
        Assert.assertEquals(rfpLabourActualDefaultValue, PricingEscalationDefaultValues.rfpLabour_CFM56_5B, RFP_LABOUR_VALUE_MISMATCH);
        Assert.assertEquals(hcMaterialPricesActualDefaultValue, PricingEscalationDefaultValues.hcMaterialPrices_CFM56_5B, HC_MATERIAL_PRICES_VALUE_MISMATCH);
        Assert.assertEquals(hcSubcontractPricesActualDefaultValue, PricingEscalationDefaultValues.hcSubcontractPrices_CFM56_5B, HC_SUBCONTRACT_PRICES_VALUE_MISMATCH);
    }
}
