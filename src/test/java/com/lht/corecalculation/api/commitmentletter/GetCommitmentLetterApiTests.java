package com.lht.corecalculation.api.commitmentletter;

import com.lht.corecalculation.api.request.commitmentletter.GetCommitmentLetterRequest;
import com.lht.corecalculation.base.BaseCocaApiTest;
import io.restassured.response.Response;
import org.testng.Assert;
import org.testng.annotations.Test;


import static com.lht.corecalculation.api.constants.GlobalConstants.ADMIN_TOKEN;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_V2500_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.NO_ROLE_TOKEN;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_200;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_400;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_401;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_403;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.VIEW_ONLY_TOKEN;

public class GetCommitmentLetterApiTests extends BaseCocaApiTest {

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "quotationIdsCommitmentLetterProvider")
    public void verifyCommitmentLetter_getEndpointReturn_StatusCode_200(String quotationId) {
        GetCommitmentLetterRequest getCommitmentLetterRequest =
                new GetCommitmentLetterRequest(quotationId)
                        .withBearerToken(utils.context.getContextItem(ADMIN_TOKEN));

        Response response = getCommitmentLetterRequest.callAPI();
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "quotationIdsCommitmentLetterProvider")
    public void verifyCommitmentLetter_getEndpointReturn_StatusCode_200_ViewOnly(String quotationId) {
        GetCommitmentLetterRequest getCommitmentLetterRequest =
                new GetCommitmentLetterRequest(quotationId)
                        .withBearerToken(utils.context.getContextItem(VIEW_ONLY_TOKEN));

        Response response = getCommitmentLetterRequest.callAPI();
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "adminAccessTokenProvider")
    public void verifyCommitmentLetter_getEndpointReturn_StatusCode_400_EngineType_V2500(String accessToken) {
        GetCommitmentLetterRequest getCommitmentLetterRequest =
                new GetCommitmentLetterRequest(ENGINE_V2500_QUOTATION_ID).withBearerToken(accessToken);

        Response response = getCommitmentLetterRequest.callAPI();
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_400, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "quotationIdsByEngineProvider")
    public void verifyCommitmentLetter_getEndpointReturn_StatusCode_401_(String quotationId) {
        GetCommitmentLetterRequest getCommitmentLetterRequest =
                new GetCommitmentLetterRequest(quotationId)
                        .withBearerToken(utils.context.getContextItem(NO_ROLE_TOKEN));

        Response response = getCommitmentLetterRequest.callAPI();
        Assert.assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_401, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
    }
}
