package com.lht.corecalculation.api.commitmentletter;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.lht.corecalculation.api.enums.PartType;
import com.lht.corecalculation.api.pojo.dto.commitmentletter.CommitmentLetterResponseDto;
import com.lht.corecalculation.api.utils.CommitmentLetterUtil;
import com.lht.corecalculation.base.BaseCocaApiTest;
import io.restassured.response.Response;
import org.testng.annotations.Test;


import static com.lht.corecalculation.api.constants.GlobalConstants.ADMIN_TOKEN;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_CFM56_5B_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_CFM56_7B_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_LEAP_1A_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_LEAP_1B_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_V2500_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ERROR_MESSAGE_NOT_ALL_PARTS_HAS_EXPECTED_COMMITMENT_LETTER;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_200;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_404;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE;
import static com.lht.corecalculation.api.utils.CommitmentLetterUtil.putCommitmentLetterRequest;
import static com.lht.corecalculation.api.utils.CommitmentLetterUtil.resetAllPartsHasCommitmentLetter;
import static com.lht.corecalculation.api.utils.CommitmentLetterUtil.updatePartsToHasCommitmentLetter;
import static com.lht.corecalculation.api.utils.CommitmentLetterUtil.validateAllPartsHasCommitmentLetter;
import static com.lht.corecalculation.api.utils.CommitmentLetterUtil.validatePartsHasCommitmentLetter;
import static org.testng.Assert.assertEquals;
import static org.testng.Assert.assertTrue;

public class PutCommitmentLetterApiTests extends BaseCocaApiTest {

    @Test(groups = "workscopeFixedPriceAndNte", dataProvider = "quotationIdsCommitmentLetterProvider")
    public void verifyPutCommitmentLetterEndpoint_Returns200_WithCaseAndFrameUpdate(String quotationId) throws JsonProcessingException {
        String accessToken = utils.context.getContextItem(ADMIN_TOKEN);
        Response response = CommitmentLetterUtil.getCommitmentLetterResponse(quotationId, accessToken);

        CommitmentLetterResponseDto commitmentLetterResponseDto = utils.convert.jsonToDto(response, CommitmentLetterResponseDto.class);

        resetAllPartsHasCommitmentLetter(commitmentLetterResponseDto);
        updatePartsToHasCommitmentLetter(commitmentLetterResponseDto, PartType.CASE_AND_FRAME);

        Response responsePut = putCommitmentLetterRequest(quotationId, accessToken, commitmentLetterResponseDto);
        assertEquals(responsePut.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
        assertTrue(validatePartsHasCommitmentLetter(commitmentLetterResponseDto, PartType.CASE_AND_FRAME),
                ERROR_MESSAGE_NOT_ALL_PARTS_HAS_EXPECTED_COMMITMENT_LETTER);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "quotationIdsCommitmentLetterProvider")
    public void verifyPutCommitmentLetterEndpoint_Returns200_WithAPartUpdate(String quotationId) throws JsonProcessingException {
        String accessToken = utils.context.getContextItem(ADMIN_TOKEN);
        Response response = CommitmentLetterUtil.getCommitmentLetterResponse(quotationId, accessToken);

        CommitmentLetterResponseDto commitmentLetterResponseDto = utils.convert.jsonToDto(response, CommitmentLetterResponseDto.class);

        resetAllPartsHasCommitmentLetter(commitmentLetterResponseDto);
        updatePartsToHasCommitmentLetter(commitmentLetterResponseDto, PartType.A_PART);

        Response responsePut = putCommitmentLetterRequest(quotationId, accessToken, commitmentLetterResponseDto);
        assertEquals(responsePut.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
        assertTrue(validatePartsHasCommitmentLetter(commitmentLetterResponseDto, PartType.A_PART),
                ERROR_MESSAGE_NOT_ALL_PARTS_HAS_EXPECTED_COMMITMENT_LETTER);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "quotationIdsCommitmentLetterProvider")
    public void verifyPutCommitmentLetterEndpoint_Returns200_WithLlpUpdate(String quotationId) throws JsonProcessingException {
        String accessToken = utils.context.getContextItem(ADMIN_TOKEN);
        Response response = CommitmentLetterUtil.getCommitmentLetterResponse(quotationId, accessToken);

        CommitmentLetterResponseDto commitmentLetterResponseDto = utils.convert.jsonToDto(response, CommitmentLetterResponseDto.class);

        resetAllPartsHasCommitmentLetter(commitmentLetterResponseDto);
        updatePartsToHasCommitmentLetter(commitmentLetterResponseDto, PartType.LLP);

        Response responsePut = putCommitmentLetterRequest(quotationId, accessToken, commitmentLetterResponseDto);
        assertEquals(responsePut.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
        assertTrue(validatePartsHasCommitmentLetter(commitmentLetterResponseDto, PartType.LLP),
                ERROR_MESSAGE_NOT_ALL_PARTS_HAS_EXPECTED_COMMITMENT_LETTER);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "quotationIdsCommitmentLetterProvider")
    public void verifyPutCommitmentLetterEndpoint_Returns200_WithAllPartTypesUpdated(String quotationId) throws JsonProcessingException {
        String accessToken = utils.context.getContextItem(ADMIN_TOKEN);
        Response response = CommitmentLetterUtil.getCommitmentLetterResponse(quotationId, accessToken);

        CommitmentLetterResponseDto commitmentLetterResponseDto = utils.convert.jsonToDto(response, CommitmentLetterResponseDto.class);

        resetAllPartsHasCommitmentLetter(commitmentLetterResponseDto);
        updatePartsToHasCommitmentLetter(commitmentLetterResponseDto, PartType.LLP);
        updatePartsToHasCommitmentLetter(commitmentLetterResponseDto, PartType.CASE_AND_FRAME);
        updatePartsToHasCommitmentLetter(commitmentLetterResponseDto, PartType.A_PART);

        Response responsePut = putCommitmentLetterRequest(quotationId, accessToken, commitmentLetterResponseDto);
        assertEquals(responsePut.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
        assertTrue(validateAllPartsHasCommitmentLetter(commitmentLetterResponseDto, true));
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "quotationIdsCommitmentLetterProvider")
    public void verifyPutCommitmentLetterEndpoint_Returns200_NoPartTypesUpdated(String quotationId) throws JsonProcessingException {
        String accessToken = utils.context.getContextItem(ADMIN_TOKEN);
        Response response = CommitmentLetterUtil.getCommitmentLetterResponse(quotationId, accessToken);

        CommitmentLetterResponseDto commitmentLetterResponseDto = utils.convert.jsonToDto(response, CommitmentLetterResponseDto.class);

        resetAllPartsHasCommitmentLetter(commitmentLetterResponseDto);

        Response responsePut = putCommitmentLetterRequest(quotationId, accessToken, commitmentLetterResponseDto);

        assertEquals(responsePut.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
        assertTrue(validateAllPartsHasCommitmentLetter(commitmentLetterResponseDto, false));
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "adminAccessTokenProvider")
    public void verifyPutCommitmentLetter_Returns404_ForEngineV2500(String accessToken) throws JsonProcessingException {
        Response response = CommitmentLetterUtil.getCommitmentLetterResponse(ENGINE_LEAP_1A_QUOTATION_ID, accessToken);

        CommitmentLetterResponseDto commitmentLetterResponseDto = utils.convert.jsonToDto(response, CommitmentLetterResponseDto.class);

        resetAllPartsHasCommitmentLetter(commitmentLetterResponseDto);
        updatePartsToHasCommitmentLetter(commitmentLetterResponseDto, PartType.CASE_AND_FRAME);

        Response responsePut = putCommitmentLetterRequest(ENGINE_V2500_QUOTATION_ID, accessToken, commitmentLetterResponseDto);
        assertEquals(responsePut.statusCode(), RESPONSE_STATUS_CODE_404, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = "workscopeFixedPriceAndNte", dataProvider = "adminAccessTokenProvider")
    public void verifyPutEndpoint_Status404_ForCommitmentLetterWithDifferentLeapEngines(String accessToken) throws JsonProcessingException {
        Response response = CommitmentLetterUtil.getCommitmentLetterResponse(ENGINE_LEAP_1A_QUOTATION_ID, accessToken);

        CommitmentLetterResponseDto commitmentLetterResponseDto = utils.convert.jsonToDto(response, CommitmentLetterResponseDto.class);

        resetAllPartsHasCommitmentLetter(commitmentLetterResponseDto);
        updatePartsToHasCommitmentLetter(commitmentLetterResponseDto, PartType.CASE_AND_FRAME);

        Response responsePut = putCommitmentLetterRequest(ENGINE_LEAP_1B_QUOTATION_ID, accessToken, commitmentLetterResponseDto);
        assertEquals(responsePut.statusCode(), RESPONSE_STATUS_CODE_404, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = "workscopeFixedPriceAndNte", dataProvider = "adminAccessTokenProvider")
    public void verifyPutEndpoint_Status404_ForCommitmentLetterWithDifferentCfmEngines(String accessToken) throws JsonProcessingException {
        Response response = CommitmentLetterUtil.getCommitmentLetterResponse(ENGINE_CFM56_7B_QUOTATION_ID, accessToken);

        CommitmentLetterResponseDto commitmentLetterResponseDto = utils.convert.jsonToDto(response, CommitmentLetterResponseDto.class);

        resetAllPartsHasCommitmentLetter(commitmentLetterResponseDto);
        updatePartsToHasCommitmentLetter(commitmentLetterResponseDto, PartType.CASE_AND_FRAME);

        Response responsePut = putCommitmentLetterRequest(ENGINE_CFM56_5B_QUOTATION_ID, accessToken, commitmentLetterResponseDto);
        assertEquals(responsePut.statusCode(), RESPONSE_STATUS_CODE_404, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
    }
}
