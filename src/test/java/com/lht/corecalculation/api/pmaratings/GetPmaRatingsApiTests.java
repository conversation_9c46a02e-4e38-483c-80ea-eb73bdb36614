package com.lht.corecalculation.api.pmaratings;

import com.lht.corecalculation.api.handlingcharges.ClusterOrderAssertion;
import com.lht.corecalculation.api.handlingcharges.ClusterPartsNameOrderAssertion;
import com.lht.corecalculation.api.pojo.dto.pmarating.ClusterDto;
import com.lht.corecalculation.api.pojo.dto.pmarating.PartDto;
import com.lht.corecalculation.api.pojo.dto.pmarating.PmaResponseDto;
import com.lht.corecalculation.api.request.pmarating.GetPmaRatingsRequest;
import com.lht.corecalculation.base.BaseCocaApiTest;
import io.restassured.response.Response;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import org.testng.annotations.Test;


import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_CFM56_5B_PMA_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_CFM56_5B_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_CFM56_7B_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_LEAP_1A_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_LEAP_1B_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.ERROR_DETAILS_MISMATCH_AS_MESSAGE;
import static com.lht.corecalculation.api.constants.GlobalConstants.NO_ROLE_TOKEN;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_200;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_401;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_403;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_404;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE;
import static com.lht.corecalculation.api.utils.PmaRatingsUtil.extractFromClusters;
import static com.lht.corecalculation.api.utils.PmaRatingsUtil.extractFromParts;
import static com.lht.corecalculation.api.utils.PmaRatingsUtil.fetchPmaRatingsResponseDto;
import static com.lht.corecalculation.api.z2ratings.GetAllZ2RatingsApiTests.VALID_CURRENCY_VALUES;
import static org.testng.Assert.assertEquals;

public class GetPmaRatingsApiTests extends BaseCocaApiTest {

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "adminAccessTokenProvider")
    public void verifyPmaRating_ReturnsStatusCode200_Engine_CFM56_5B_PMA_Admin(String accessToken) {
        GetPmaRatingsRequest getPmaRatingsRequest = new GetPmaRatingsRequest(ENGINE_CFM56_5B_PMA_QUOTATION_ID).withBearerToken(accessToken);
        Response response = getPmaRatingsRequest.callAPI();
        assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "viewOnlyAccessTokenProvider")
    public void verifyPmaRating_ReturnsStatusCode200_Engine_CFM56_5B_PMA_ViewOnly(String accessToken) {
        GetPmaRatingsRequest getPmaRatingsRequest = new GetPmaRatingsRequest(ENGINE_CFM56_5B_PMA_QUOTATION_ID).withBearerToken(accessToken);
        Response response = getPmaRatingsRequest.callAPI();
        assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"})
    public void verifyPmaRating_ReturnsStatusCode200_Engine_CFM56_5B_PMA_NoRole() {
        String accessToken = utils.context.getContextItem(NO_ROLE_TOKEN);
        GetPmaRatingsRequest getPmaRatingsRequest = new GetPmaRatingsRequest(ENGINE_CFM56_5B_PMA_QUOTATION_ID).withBearerToken(accessToken);
        Response response = getPmaRatingsRequest.callAPI();
        assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_401, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "adminAccessTokenProvider")
    public void verifyPmaRating_ReturnsStatusCode200_Engine_CFM56_5B_PMA(String accessToken) {
        GetPmaRatingsRequest getPmaRatingsRequest = new GetPmaRatingsRequest(ENGINE_CFM56_5B_PMA_QUOTATION_ID).withBearerToken(accessToken);
        Response response = getPmaRatingsRequest.callAPI();
        assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_200, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "adminAccessTokenProvider")
    public void verifyPmaRating_ClusterOrder_Engine_CFM56_5B_PMA(String accessToken) throws IOException {
        PmaResponseDto responseDto = fetchPmaRatingsResponseDto(accessToken, ENGINE_CFM56_5B_PMA_QUOTATION_ID);

        List<String> actualClustersNames = extractFromClusters(responseDto, ClusterDto::getName);
        List<String> expectedClusterOrderList = Arrays.asList(ClusterOrderAssertion.getz2ClusterOrderCfm565B());

        assertEquals(actualClustersNames, expectedClusterOrderList, ERROR_DETAILS_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "adminAccessTokenProvider")
    public void verifyPamRating_PartsOrder_Engine_CFM56_5B_PMA(String accessToken) throws IOException {
        PmaResponseDto responseDto = fetchPmaRatingsResponseDto(accessToken, ENGINE_CFM56_5B_PMA_QUOTATION_ID);

        List<String> actualClustersNames = extractFromParts(responseDto, PartDto::getName);
        List<String> expectedClusterOrderList = Arrays.asList(ClusterPartsNameOrderAssertion.getPmaClusterOrder_CFM56_5B());

        assertEquals(actualClustersNames, expectedClusterOrderList, ERROR_DETAILS_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "adminAccessTokenProvider")
    public void verifyPmaRating_CurrencyOnlyUsdAndNull_Engine_CFM56_5B_PMA(String accessToken) throws IOException {
        PmaResponseDto responseDto = fetchPmaRatingsResponseDto(accessToken, ENGINE_CFM56_5B_PMA_QUOTATION_ID);

        List<String> actualCurrency = extractFromParts(responseDto, PartDto::getCurrency);

        assertEquals(actualCurrency, VALID_CURRENCY_VALUES, ERROR_DETAILS_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "adminAccessTokenProvider")
    public void verifyPmaRating_ReturnsStatusCode404_Engine_CFM56_5B_withoutPMA_Admin(String accessToken) {
        GetPmaRatingsRequest getPmaRatingsRequest = new GetPmaRatingsRequest(ENGINE_CFM56_5B_QUOTATION_ID).withBearerToken(accessToken);
        Response response = getPmaRatingsRequest.callAPI();
        assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_404, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "adminAccessTokenProvider")
    public void verifyPmaRating_ReturnsStatusCode404_Engine_LEAP_1A_withoutPMA_Admin(String accessToken) {
        GetPmaRatingsRequest getPmaRatingsRequest = new GetPmaRatingsRequest(ENGINE_LEAP_1A_QUOTATION_ID).withBearerToken(accessToken);
        Response response = getPmaRatingsRequest.callAPI();
        assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_404, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "adminAccessTokenProvider")
    public void verifyPmaRating_ReturnsStatusCode404_Engine_LEAP_1B_withoutPMA_Admin(String accessToken) {
        GetPmaRatingsRequest getPmaRatingsRequest = new GetPmaRatingsRequest(ENGINE_LEAP_1B_QUOTATION_ID).withBearerToken(accessToken);
        Response response = getPmaRatingsRequest.callAPI();
        assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_404, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
    }

    @Test(groups = {"workscopeFixedPriceAndNte", "modularFixedPriceAndNte"}, dataProvider = "adminAccessTokenProvider")
    public void verifyPmaRating_ReturnsStatusCode404_Engine_ENGINE_CFM56_7B_withoutPMA_Admin(String accessToken) {
        GetPmaRatingsRequest getPmaRatingsRequest = new GetPmaRatingsRequest(ENGINE_CFM56_7B_QUOTATION_ID).withBearerToken(accessToken);
        Response response = getPmaRatingsRequest.callAPI();
        assertEquals(response.statusCode(), RESPONSE_STATUS_CODE_404, RESPONSE_STATUS_CODE_MISMATCH_AS_MESSAGE);
    }
}