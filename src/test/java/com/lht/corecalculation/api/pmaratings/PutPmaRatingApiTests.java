package com.lht.corecalculation.api.pmaratings;

import com.lht.corecalculation.api.pojo.dto.pmarating.PartDto;
import com.lht.corecalculation.api.pojo.dto.pmarating.PmaRatingInputDto;
import com.lht.corecalculation.api.pojo.dto.pmarating.PmaResponseDto;
import com.lht.corecalculation.base.BaseCocaApiTest;
import java.io.IOException;
import java.util.List;
import java.util.function.Function;
import org.testng.Assert;
import org.testng.annotations.Test;


import static com.lht.corecalculation.api.constants.GlobalConstants.ADMIN_TOKEN;
import static com.lht.corecalculation.api.constants.GlobalConstants.ENGINE_CFM56_5B_PMA_QUOTATION_ID;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_200;
import static com.lht.corecalculation.api.constants.GlobalConstants.RESPONSE_STATUS_CODE_400;
import static com.lht.corecalculation.api.utils.PmaRatingsUtil.assertResponseStatusCode;
import static com.lht.corecalculation.api.utils.PmaRatingsUtil.buildPmaRatingInputs;
import static com.lht.corecalculation.api.utils.PmaRatingsUtil.buildPmaRatingInputsSpecificYearAndValue;
import static com.lht.corecalculation.api.utils.PmaRatingsUtil.createPmaRatingsUpdate;
import static com.lht.corecalculation.api.utils.PmaRatingsUtil.extractFromParts;
import static com.lht.corecalculation.api.utils.PmaRatingsUtil.fetchPmaRatingsResponseDto;
import static com.lht.corecalculation.api.utils.PmaRatingsUtil.getAttributeForSpecificYear;
import static com.lht.corecalculation.api.utils.PmaRatingsUtil.updatePmaRatings;
import static com.lht.corecalculation.api.utils.PmaRatingsUtil.validateUpdatedRatings;

public class PutPmaRatingApiTests extends BaseCocaApiTest {

    @Test(groups = {"smoke", "workscopeFixedPriceAndNte"}, dataProvider = "doubleValues")
    public void verifyGlobalPmaRating_CanUpdateAllPartsValueInAllYears_Engine_CFM56_5B_PMA(
            String accessToken,
            Double value
    ) throws IOException {
        String adminToken = utils.context.getContextItem(ADMIN_TOKEN);

        PmaResponseDto initialRatingsResponse = fetchPmaRatingsResponseDto(adminToken, ENGINE_CFM56_5B_PMA_QUOTATION_ID);

        List<PmaRatingInputDto> pmaRatingInputs = buildPmaRatingInputs(extractFromParts(initialRatingsResponse, Function.identity()), value);
        String updateRequestBody = utils.convert.dtoToJsonString(createPmaRatingsUpdate(pmaRatingInputs));
        assertResponseStatusCode(updatePmaRatings(adminToken, updateRequestBody, ENGINE_CFM56_5B_PMA_QUOTATION_ID), RESPONSE_STATUS_CODE_200);

        validateUpdatedRatings(fetchPmaRatingsResponseDto(adminToken, ENGINE_CFM56_5B_PMA_QUOTATION_ID), value);
    }

    @Test(groups = "workscopeFixedPriceAndNte", dataProvider = "pmaValuesProvider")
    public void verifyPmaRating_CanUpdateAllPartValueInYear_Engine_CFM56_5B_PMA(
            Double initialValue,
            Double newValue,
            String year
    ) throws IOException {
        String adminToken = utils.context.getContextItem(ADMIN_TOKEN);

        PmaResponseDto initialRatingsResponse = fetchPmaRatingsResponseDto(adminToken, ENGINE_CFM56_5B_PMA_QUOTATION_ID);

        List<PmaRatingInputDto> pmaRatingInputs = buildPmaRatingInputsSpecificYearAndValue(
                extractFromParts(initialRatingsResponse, Function.identity()),
                initialValue,
                initialRatingsResponse,
                year,
                newValue
        );

        String updateRequestBody = utils.convert.dtoToJsonString(createPmaRatingsUpdate(pmaRatingInputs));
        assertResponseStatusCode(updatePmaRatings(adminToken, updateRequestBody, ENGINE_CFM56_5B_PMA_QUOTATION_ID), RESPONSE_STATUS_CODE_200);

        PmaResponseDto updatedRatingsResponse = fetchPmaRatingsResponseDto(adminToken, ENGINE_CFM56_5B_PMA_QUOTATION_ID);
        List<Double> listActualPmaRatingInput = getAttributeForSpecificYear(updatedRatingsResponse, year, PartDto::getPmaRatingInput);

        listActualPmaRatingInput.forEach(actualValue -> Assert.assertEquals(actualValue, newValue));
    }

    @Test(groups = "workscopeFixedPriceAndNte", dataProvider = "pmaInvalidValuesProvider")
    public void verifyGlobalPmaRating_CannotUpdateWithInvalidValues_Engine_CFM56_5B_PMA(Double value) throws IOException {
        String adminToken = utils.context.getContextItem(ADMIN_TOKEN);

        PmaResponseDto initialRatingsResponse = fetchPmaRatingsResponseDto(adminToken, ENGINE_CFM56_5B_PMA_QUOTATION_ID);

        List<PmaRatingInputDto> pmaRatingInputs = buildPmaRatingInputs(extractFromParts(initialRatingsResponse, Function.identity()), value);
        String updateRequestBody = utils.convert.dtoToJsonString(createPmaRatingsUpdate(pmaRatingInputs));
        assertResponseStatusCode(updatePmaRatings(adminToken, updateRequestBody, ENGINE_CFM56_5B_PMA_QUOTATION_ID), RESPONSE_STATUS_CODE_400);
    }

    @Test(groups = "workscopeFixedPriceAndNte")
    public void verifyGlobalPmaRatingCanBeUpdatedToNullForAllParts_EngineCFM56_5B_PMA() throws IOException {
        String adminToken = utils.context.getContextItem(ADMIN_TOKEN);

        PmaResponseDto initialRatingsResponse = fetchPmaRatingsResponseDto(adminToken, ENGINE_CFM56_5B_PMA_QUOTATION_ID);

        List<PmaRatingInputDto> pmaRatingInputs = buildPmaRatingInputs(extractFromParts(initialRatingsResponse, Function.identity()), null);
        String updateRequestBody = utils.convert.dtoToJsonString(createPmaRatingsUpdate(pmaRatingInputs));
        assertResponseStatusCode(updatePmaRatings(adminToken, updateRequestBody, ENGINE_CFM56_5B_PMA_QUOTATION_ID), RESPONSE_STATUS_CODE_200);
    }
}
