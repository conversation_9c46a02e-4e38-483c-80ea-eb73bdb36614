# Core Calculation - Carina API Testing Project

## Overview

This project is an API automation testing framework based on **Carina Framework**. It provides a structured approach to test APIs
efficiently using
Java, Rest-Assured, TestNG, and other tools.

## Prerequisites

- **JDK 17** (Ensure it's set as the default Java version)
- **Maven** (Used for dependency management and test execution)
- **IntelliJ IDEA** (Recommended IDE)
- **Lombok Plugin** (Ensure it is installed and enabled in IntelliJ IDE)

## IntelliJ IDEA Setup

### Code Formatting and Style

To set up code formatting and style, follow the steps provided on the **Miro board** used by the backend team.

### Actions on Save

1. Go to **Settings** → **Tools** → **Actions on Save**.
2. Check the following options:
    - **Reformat code**.
    - **Optimize imports**.
    - **Rearrange code**.
    - **Run code cleanup**.

## Installation

1. Clone the repository:
   ```sh
   <NAME_EMAIL>:lht-general/the-core-calculation-qa-automation.git
   cd coca-qa-automation
   ```
2. Import the project into **IntelliJ IDEA** as a Maven project.
3. Ensure all dependencies are downloaded by running:
   ```sh
   mvn clean install
   ```

## IntelliJ IDEA Configuration

### Download Sources and Documentation Automatically

To ensure that sources and documentation are downloaded automatically in IntelliJ IDEA:

1. Go to **Settings** → **Build, Execution, Deployment** → **Build Tools** → **Maven** → **Importing**.
2. Check **Sources and Documentation**.
3. Click **Apply** and then **OK**.

### Check Java Home and Maven Home Installation

Ensure that **Java Home** and **Maven Home** are correctly configured:

- Open **Terminal** and run:
  ```sh
  java -version
  mvn -version
  ```
- If any of these commands fail, update the environment variables accordingly.

## Before Building the Project

Before building the project, you need to add the following files:

- **`src/main/resources/META-INF/persistence.xml`**
- **`src/main/resources/_testdata.properties`**

Ensure these files are correctly configured as per project requirements.

## Project Structure

```
📂 core-calculation
 ├── 📂 src/main/java       # Main application source code
 ├── 📂 src/main/resources  # Configurations and properties
 │   ├── 📂 META-INF
 │   │   ├── persistence.xml  # Persistence configuration file
 │   ├── _testdata.properties  # Test data properties file
 ├── 📂 src/test/java       # Test scripts
 ├── 📂 src/test/resources  # Test data and configurations
 ├── 📜 pom.xml             # Maven dependencies and configurations
```

## Running Tests

Before executing a test suite, the database must be reset using the reset-input script from the coca-tooling repository.

To reset the database, run:

```sh
./run.sh -u manual -e test -m reset-input
```

After resetting the database, run the desired test suite:

```sh
mvn clean test -Dsuite=api
```

## CI/CD Integration

This project supports integration with Jenkins for continuous testing and reporting.

The reports are in Dashboards in Kibana, where we can see the results from previous executions.

The Jenkins pipeline is running every morning.

---

Happy Testing! 🚀
