# DataProvider Update Helper Utility

This utility automatically extracts actual values from API responses and updates DataProvider expected values in test classes across three specific packages: `wssummarymodularntefixprice`, `wssummary`, and `wssummarynteandfixprice`.

## Problem Solved

API values change daily, causing tests to fail because the expected values in DataProviders become outdated. This utility automates the process of extracting current actual values from the `sharedWorkscopeSummaryResponse` variable and updating all relevant DataProviders automatically.

## Components

### 1. DataProviderUpdater.java
Core utility that handles the actual updating of DataProvider files with new values from API responses.

**Key Features:**
- Automatically detects DataProvider structure (component-based vs workscope-based)
- Handles different metrics (ProductionCost, Revenue, Discount, etc.)
- Supports different item types (INCLUDED_ITEMS, EXCLUDED_ITEMS, TOTAL_ITEMS)
- Preserves existing Object[][] format

### 2. DataProviderUpdateRunner.java
Runner utility that executes the update process for test classes.

**Key Features:**
- Runs `fetchWorkscopeSummary()` method of test classes
- Extracts `sharedWorkscopeSummaryResponse` from test instances
- Coordinates the update process across multiple test classes

### 3. DataProviderUpdateExample.java
Example class showing different ways to use the utilities.

## Usage Examples

### 1. Update DataProvider for a Specific Test Class

```java
// Update DataProvider for a specific test
DataProviderUpdateRunner.updateDataProviderForTest("GetWorkscopeSummaryEngineLeap1ATests");
```

### 2. Update All DataProviders in a Package

```java
// Update all DataProviders in the wssummary package
DataProviderUpdateRunner.updateDataProvidersForPackage("wssummary");

// Update modular pricing DataProviders
DataProviderUpdateRunner.updateDataProvidersForPackage("wssummarymodularntefixprice");

// Update NTE and fixed price DataProviders
DataProviderUpdateRunner.updateDataProvidersForPackage("wssummarynteandfixprice");
```

### 3. Update All DataProviders Across All Packages

```java
// Update all DataProviders in all target packages
DataProviderUpdateRunner.updateAllDataProviders();
```

### 4. Use from Within a Test Class

```java
@Test
public void updateMyDataProvider() {
    // This will automatically detect the DataProvider class and update it
    DataProviderUpdater.updateMyDataProvider(this, sharedWorkscopeSummaryResponse);
}
```

### 5. Manual DataProvider Update

```java
@Test
public void updateDataProviderManually() {
    String dataProviderClassName = "DataProvidersWorkscopeSummaryLeap1A";
    String packageName = "wssummary";
    DataProviderUpdater.updateDataProvider(sharedWorkscopeSummaryResponse, dataProviderClassName, packageName);
}
```

## Command Line Usage

### Update All DataProviders
```bash
java DataProviderUpdateRunner
```

### Update Specific Test Class
```bash
java DataProviderUpdateRunner --test GetWorkscopeSummaryEngineLeap1ATests
```

### Update Specific Package
```bash
java DataProviderUpdateRunner --package wssummary
```

## Supported Test Classes

### wssummarymodularntefixprice Package:
- GetWorkscopeModularSummaryNteCfm567bApiTests
- GetModularWorkscopeSummaryNteLeap1bApiTests
- GetWorkscopeSummaryModularNteCfm565bPmaApiTests
- GetWsModularFixedAndNteLeap1aApiTests
- GetWSModularFixedPriceV2500ApiTest

### wssummary Package:
- GetWorkscopeSummaryEngineLeap1ATests
- GetWorkscopeSummaryEngineLeap1BTests
- GetWorkscopeSummaryEngineV2500Tests
- GetWorkscopeSummaryEngineCfm567BTests
- GetWorkscopeSummaryEngineCfm565BTests

### wssummarynteandfixprice Package:
- GetWorkscopeSummaryWsFixedAndNteLeap1aApiTests
- GetWorkscopeSummaryNteCfm567bApiTests
- GetWorkscopeSummaryNteLeap1bApiTests
- GetWorkscopeSummaryNteCfm565bPmaApiTests
- GetWorkscopeSummaryWsFixedPriceV2500ApiTests

## Supported Metrics

The utility can extract and update values for the following metrics:
- ProductionCost
- Revenue
- Discount
- SurchargesCost
- ProdCostInclDiscountsAndSurcharges
- Db2
- Ebit
- EbitPercentage
- EatPercentage
- NetMargin

## DataProvider Structure Support

### Component-Based DataProviders
For DataProviders that use component types (CPR, HPC, HPT_S1B, HPT_LLP, NSV):
```java
{CPR, Arrays.asList(8004343.0, 8454563.0, 8946349.0, 9250083.0, 9509838.0, 9724336.0)},
{HPC, Arrays.asList(241751.0, 225872.0, 230754.0, 232770.0, 232961.0, 242138.0)},
```

### Workscope-Based DataProviders
For DataProviders that use workscope names (WS1, WS2, etc.):
```java
{
    WS1,
    Arrays.asList(2335719.0, 2450265.0, 2560613.0),
    Arrays.asList(1069330.0, 1135615.0, 1204832.0),
    Arrays.asList(3405049.0, 3585881.0, 3765446.0)
},
```

## Workflow for Test Failures

When tests fail due to changed API values:

1. **Run the utility to update DataProviders:**
   ```java
   DataProviderUpdateRunner.updateAllDataProviders();
   ```

2. **Or update specific failing tests:**
   ```java
   DataProviderUpdateRunner.updateDataProviderForTest("GetWorkscopeSummaryEngineLeap1ATests");
   ```

3. **Re-run the tests** to verify they now pass with updated expected values.

## Error Handling

The utility includes comprehensive error handling:
- Logs warnings for missing test classes or DataProviders
- Continues processing other classes if one fails
- Preserves original DataProvider content if update fails
- Provides detailed error messages for troubleshooting

## Testing the Utility

Use the test method to verify the utility works without actually updating files:
```java
DataProviderUpdateRunner.testUpdateProcess("GetWorkscopeSummaryEngineLeap1ATests");
```

This will:
- Run the fetchWorkscopeSummary method
- Extract the API response
- Log information about the response and DataProvider
- Not modify any files

## Integration with Existing Tests

The utility is designed to work seamlessly with existing test infrastructure:
- Uses existing `fetchWorkscopeSummary()` methods
- Accesses existing `sharedWorkscopeSummaryResponse` variables
- Preserves existing DataProvider method signatures
- Maintains existing test class inheritance structure

## Best Practices

1. **Run after test failures** to sync expected values with current API responses
2. **Use package-level updates** when multiple tests in a package are failing
3. **Test the process first** using `testUpdateProcess()` before actual updates
4. **Review changes** in version control before committing updated DataProviders
5. **Run tests again** after updating to ensure they pass

## Troubleshooting

### Common Issues:

1. **Test class not found**: Ensure the test class name is correct and exists in target packages
2. **DataProvider not found**: Check that the test class extends the correct DataProvider class
3. **Null response**: Ensure `fetchWorkscopeSummary()` runs successfully before updating
4. **Permission errors**: Ensure write permissions to DataProvider files
5. **Format errors**: Check that DataProvider methods follow expected patterns

### Debug Mode:
Enable debug logging to see detailed information about the update process:
```java
// The utility uses SLF4J logging - configure your logging framework for debug level
```
