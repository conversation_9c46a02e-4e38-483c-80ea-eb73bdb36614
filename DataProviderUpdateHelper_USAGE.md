# DataProvider Update Helper - Usage Guide

## Issue Resolution

The compilation errors you encountered were caused by the utility creating duplicate entries and malformed DataProvider methods. I've fixed the `DataProvidersWorkscopeSummaryLeap1A.java` file and improved the utility to prevent this issue.

## Fixed Files

✅ **DataProvidersWorkscopeSummaryLeap1A.java** - Restored to correct format, compilation errors resolved
✅ **DataProviderUpdater.java** - Improved regex pattern to prevent duplication
✅ **SafeDataProviderUpdater.java** - New safe utility with backup/rollback functionality

## Recommended Usage

### 1. Use the Safe Updater (Recommended)

The `SafeDataProviderUpdater` creates backups and provides rollback functionality:

```java
// Safe update with automatic backup
boolean success = SafeDataProviderUpdater.safeUpdateDataProvider(
    sharedWorkscopeSummaryResponse, 
    "DataProvidersWorkscopeSummaryLeap1A", 
    "wssummary"
);

if (!success) {
    // Automatic rollback already performed
    log.error("Update failed, original content restored");
}
```

### 2. Manual Backup Before Using Original Utility

If you prefer the original utility, create manual backups:

```bash
# Create backup before running utility
cp src/test/java/com/lht/corecalculation/api/wssummary/DataProvidersWorkscopeSummaryLeap1A.java \
   src/test/java/com/lht/corecalculation/api/wssummary/DataProvidersWorkscopeSummaryLeap1A.java.backup
```

### 3. Test Mode First

Always test the process before actual updates:

```java
// Test the process without updating files
DataProviderUpdateRunner.testUpdateProcess("GetWorkscopeSummaryEngineLeap1ATests");
```

## Step-by-Step Safe Update Process

### Step 1: Test the Process
```java
@Test
public void testDataProviderUpdate() {
    // This will run fetchWorkscopeSummary and log information without updating files
    DataProviderUpdateRunner.testUpdateProcess("GetWorkscopeSummaryEngineLeap1ATests");
}
```

### Step 2: Safe Update with Backup
```java
@Test
public void safeUpdateDataProvider() {
    // Run the test to get fresh API response
    String testClassName = "GetWorkscopeSummaryEngineLeap1ATests";
    
    try {
        // Get test class and run fetchWorkscopeSummary
        Class<?> testClass = Class.forName("com.lht.corecalculation.api.wssummary." + testClassName);
        Object testInstance = testClass.getDeclaredConstructor().newInstance();
        
        Method fetchMethod = testClass.getMethod("fetchWorkscopeSummary");
        fetchMethod.invoke(testInstance);
        
        Field responseField = testClass.getDeclaredField("sharedWorkscopeSummaryResponse");
        responseField.setAccessible(true);
        Response response = (Response) responseField.get(testInstance);
        
        // Safe update with backup
        boolean success = SafeDataProviderUpdater.safeUpdateDataProvider(
            response, 
            "DataProvidersWorkscopeSummaryLeap1A", 
            "wssummary"
        );
        
        if (success) {
            log.info("DataProvider updated successfully!");
        } else {
            log.error("Update failed, check logs for details");
        }
        
    } catch (Exception e) {
        log.error("Failed to update DataProvider: {}", e.getMessage(), e);
    }
}
```

### Step 3: Verify and Clean Up
```java
@Test
public void verifyAndCleanup() {
    // Run your tests to verify the updates work
    // If everything is good, clean up old backups
    SafeDataProviderUpdater.cleanupBackups("DataProvidersWorkscopeSummaryLeap1A", "wssummary");
}
```

## Rollback if Needed

If something goes wrong, you can rollback:

```java
@Test
public void rollbackDataProvider() {
    boolean success = SafeDataProviderUpdater.rollbackDataProvider(
        "DataProvidersWorkscopeSummaryLeap1A", 
        "wssummary"
    );
    
    if (success) {
        log.info("Successfully rolled back to previous version");
    }
}
```

## Batch Update All DataProviders Safely

```java
@Test
public void safeUpdateAllDataProviders() {
    String[] packages = {"wssummary", "wssummarymodularntefixprice", "wssummarynteandfixprice"};
    
    for (String packageName : packages) {
        try {
            log.info("Updating DataProviders for package: {}", packageName);
            
            // Get test classes for this package
            List<String> testClasses = getTestClassesForPackage(packageName);
            
            for (String testClassName : testClasses) {
                try {
                    // Run test and get response
                    Response response = runTestAndGetResponse(testClassName, packageName);
                    
                    // Get DataProvider class name
                    String dataProviderClassName = getDataProviderClassName(testClassName, packageName);
                    
                    // Safe update
                    boolean success = SafeDataProviderUpdater.safeUpdateDataProvider(
                        response, dataProviderClassName, packageName);
                    
                    if (success) {
                        log.info("Updated DataProvider: {}", dataProviderClassName);
                    } else {
                        log.error("Failed to update DataProvider: {}", dataProviderClassName);
                    }
                    
                } catch (Exception e) {
                    log.error("Failed to process test class {}: {}", testClassName, e.getMessage());
                }
            }
            
        } catch (Exception e) {
            log.error("Failed to process package {}: {}", packageName, e.getMessage());
        }
    }
}
```

## Best Practices

### 1. Always Create Backups
- Use `SafeDataProviderUpdater` for automatic backups
- Or create manual backups before using other utilities

### 2. Test First
- Use `testUpdateProcess()` to verify the process works
- Check that `sharedWorkscopeSummaryResponse` is not null
- Verify API response contains expected data

### 3. Verify After Updates
- Run your tests after updating DataProviders
- Check that compilation errors are resolved
- Verify test results are as expected

### 4. Clean Up
- Remove backup files after verification
- Use `cleanupBackups()` to keep only recent backups

## Troubleshooting

### Compilation Errors After Update
```java
// Rollback to previous version
SafeDataProviderUpdater.rollbackDataProvider("DataProviderClassName", "packageName");
```

### Null Response Error
```java
// Check if fetchWorkscopeSummary ran successfully
@Test
public void debugFetchWorkscopeSummary() {
    // Add debug logging to see what's happening
    DataProviderUpdateRunner.testUpdateProcess("YourTestClassName");
}
```

### Wrong Values in DataProvider
```java
// Check the API response content
@Test
public void debugApiResponse() {
    // Run fetchWorkscopeSummary and examine the response
    // Log response.getBody().asString() to see actual values
}
```

## File Locations

- **Test Classes**: `src/test/java/com/lht/corecalculation/api/{package}/`
- **DataProvider Classes**: Same location as test classes
- **Backup Files**: Created in same directory with `.backup.{timestamp}` suffix
- **Utilities**: `src/test/java/com/lht/corecalculation/api/utils/`

## Example Complete Workflow

```java
@Test
public void completeDataProviderUpdateWorkflow() {
    String testClassName = "GetWorkscopeSummaryEngineLeap1ATests";
    String dataProviderClassName = "DataProvidersWorkscopeSummaryLeap1A";
    String packageName = "wssummary";
    
    try {
        // Step 1: Test the process
        log.info("Step 1: Testing update process...");
        DataProviderUpdateRunner.testUpdateProcess(testClassName);
        
        // Step 2: Run test and get response
        log.info("Step 2: Running test and getting API response...");
        Response response = runTestAndGetResponse(testClassName, packageName);
        
        // Step 3: Safe update with backup
        log.info("Step 3: Performing safe update...");
        boolean success = SafeDataProviderUpdater.safeUpdateDataProvider(
            response, dataProviderClassName, packageName);
        
        if (success) {
            log.info("Step 4: Update successful! Verify by running your tests.");
            log.info("Step 5: If tests pass, clean up backups with cleanupBackups()");
        } else {
            log.error("Update failed. Original content has been restored.");
        }
        
    } catch (Exception e) {
        log.error("Workflow failed: {}", e.getMessage(), e);
    }
}
```

This approach ensures safe updates with the ability to rollback if anything goes wrong.
