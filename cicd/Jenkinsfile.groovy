#!groovy

pipeline {
    agent {
        label 'java17'
    }

    triggers {
        cron("0 5 * * *")
    }

    options {
        buildDiscarder(logRotator(artifactDaysToKeepStr: '', artifactNumToKeepStr: '', daysToKeepStr: '', numToKeepStr: '10'))
        disableConcurrentBuilds()
    }

    parameters {
        string(name: 'RELEASE_BRANCH', defaultValue: 'master')
        string(name: 'TOOLING_BRANCH', defaultValue: 'master')
    }

    environment {
        LHT_LOGGING_ENABLED = "true"
        LHT_LOGGING_REMOTE_HOST = "localhost"
        LHT_LOGGING_REMOTE_PORT = "8181"
        APP_ID = "2938"
        LHT_LOGGING_NAMESPACE = "t-core-calc-nonprod"
        LHT_LOGGING_STAGE = "qa"
        APP_NAME = "coca-qa-automation"
    }

    stages {
        stage('Load Environment Variables') {
            steps {
                withCredentials([file(credentialsId: 'backend-db-test-connection-env', variable: 'SECRET_FILE')]) {
                    script {
                        // Load the secrets from the file
                        def props = readFile(env.SECRET_FILE).split("\n")
                        for (line in props) {
                            line = line.trim()
                            if (line && !line.startsWith("#")) { // Skip empty lines and comments
                                def (key, value) = line.tokenize('=')
                                env."${key.trim()}" = value?.trim()
                            }
                        }
                    }
                }
            }
        }
        stage("Increase DB Resources") {
            steps {
                withCredentials([usernamePassword(credentialsId: 'backend-db-test-username-password', usernameVariable: 'DB_USERNAME', passwordVariable: 'DB_PASSWORD')]) {
                    sh 'az login --service-principal --username ${DB_USERNAME} --password ${DB_PASSWORD} --tenant ${TENANT_ID}'
                    sh 'az sql db show --server ${DB_SERVER} --name ${DB_NAME} --resource-group ${RESOURCE_GROUP} --query "sku.capacity" -o table'
                    sh 'az sql db update --server ${DB_SERVER} --name ${DB_NAME} --resource-group ${RESOURCE_GROUP} --service-objective S4'
                    sh 'az sql db show --server ${DB_SERVER} --name ${DB_NAME} --resource-group ${RESOURCE_GROUP} --query "sku.capacity" -o table'
                }
            }
        }
        stage("Sonar scan") {
            steps {
                script {
                    sh 'mvn clean install -DskipTests'
                    scanSonar('appID': '2938')
                }
            }
        }

        stage("Install dependencies") {
            steps {
                script {
                    dir('dependencies') {
                        sh """
                wget https://github.com/microsoft/go-sqlcmd/releases/download/v1.3.0/sqlcmd-v1.3.0-linux-x64.tar.bz2
                tar -xf sqlcmd-v1.3.0-linux-x64.tar.bz2

                ./sqlcmd --version
            """
                    }
                }
            }
        }

        stage("DB setup Time and Material") {
            steps {
                script {
                    withCredentials([sshUserPrivateKey(credentialsId: 'github-the-core-calculation-tooling-access-key', keyFileVariable: 'SSH_KEY')]) {
                        withCredentials([file(credentialsId: 'sqlcmd-test.properties', variable: 'SQLCMD_PROPERTIES_FILE')]) {
                            sh """
                export GIT_SSH_COMMAND="ssh -i $SSH_KEY"
                <NAME_EMAIL>:lht-general/the-core-calculation-tooling.git
                
                cd the-core-calculation-tooling
                git checkout ${params.TOOLING_BRANCH}

                mkdir -p ./config/pricing-tool/sqlcmd
                mv ${SQLCMD_PROPERTIES_FILE} ./config/pricing-tool/sqlcmd/test.properties
              
                export PATH=../dependencies:\$PATH
                ./run.sh -e test -m reset-input -s pricing-tool -b ${params.TOOLING_BRANCH} -u pipeline
              """
                        }
                    }
                }
            }
        }

        stage("Run Time and Material API tests") {
            steps {
                catchError(buildResult: 'UNSTABLE', stageResult: 'FAILURE') {
                    loginToOCP(cluster: 'azureTI', namespace: 't-core-calc-nonprod', token: 'OpenShift4Token')
                    script {
                        withCredentials([file(credentialsId: 'test-users', variable: 'TEST_USERS_FILE')]) {
                            withCredentials([file(credentialsId: 'persistence.xml', variable: 'PERSISTENCE_XML_FILE')]) {
                                sh """
                                    oc port-forward svc/coca-logstash-haproxy 8181:8181 &
                                    sleep 10

                                    export TRACKING_ID="\$BUILD_NUMBER/api-tests"

                                    mv ${TEST_USERS_FILE} ./src/main/resources/_testdata.properties
                                    mv ${PERSISTENCE_XML_FILE} ./src/main/resources/META-INF/persistence.xml

                                    mvn clean -Dsuite=api test
                                """
                            }
                        }
                    }
                }
            }
        }

        stage("DB setup Workscope Fixed Price and NTE") {
            steps {
                script {
                    withCredentials([sshUserPrivateKey(credentialsId: 'github-the-core-calculation-tooling-access-key', keyFileVariable: 'SSH_KEY')]) {
                        withCredentials([file(credentialsId: 'sqlcmd-test.properties', variable: 'SQLCMD_PROPERTIES_FILE')]) {
                            sh """
                cd the-core-calculation-tooling
                git checkout ${params.TOOLING_BRANCH}

                mkdir -p ./config/pricing-tool/sqlcmd
                mv ${SQLCMD_PROPERTIES_FILE} ./config/pricing-tool/sqlcmd/test.properties
              
                export PATH=../dependencies:\$PATH
                ./run.sh -e test -m reset-input -s pricing-tool -b ${params.TOOLING_BRANCH} -u pipeline
              """
                        }
                    }
                }
            }
        }

        stage("Run API tests Workscope Fixed Price and NTE") {
            steps {
                catchError(buildResult: 'UNSTABLE', stageResult: 'FAILURE') {
                    loginToOCP(cluster: 'azureTI', namespace: 't-core-calc-nonprod', token: 'OpenShift4Token')
                    script {
                        withCredentials([file(credentialsId: 'test-users', variable: 'TEST_USERS_FILE')]) {
                            withCredentials([file(credentialsId: 'persistence.xml', variable: 'PERSISTENCE_XML_FILE')]) {
                                sh """
                                oc port-forward svc/coca-logstash-haproxy 8181:8181 &
                                sleep 10

                                export TRACKING_ID="\$BUILD_NUMBER/workscope-fix-price-and-nte-tests"

                                mv ${TEST_USERS_FILE} ./src/main/resources/_testdata.properties
                                mv ${PERSISTENCE_XML_FILE} ./src/main/resources/META-INF/persistence.xml

                                mvn clean -Dsuite=workscope-fix-price-and-nte test
                            """
                            }
                        }
                    }
                }
            }
        }

        stage("DB setup Modular Fixed Price and NTE") {
            steps {
                script {
                    withCredentials([sshUserPrivateKey(credentialsId: 'github-the-core-calculation-tooling-access-key', keyFileVariable: 'SSH_KEY')]) {
                        withCredentials([file(credentialsId: 'sqlcmd-test.properties', variable: 'SQLCMD_PROPERTIES_FILE')]) {
                            sh """
                cd the-core-calculation-tooling
                git checkout ${params.TOOLING_BRANCH}

                mkdir -p ./config/pricing-tool/sqlcmd
                mv ${SQLCMD_PROPERTIES_FILE} ./config/pricing-tool/sqlcmd/test.properties
              
                export PATH=../dependencies:\$PATH
                ./run.sh -e test -m reset-input -s pricing-tool -b ${params.TOOLING_BRANCH} -u pipeline
              """
                        }
                    }
                }
            }
        }

        stage("Run API tests Modular Fixed Price and NTE") {
            steps {
                catchError(buildResult: 'UNSTABLE', stageResult: 'FAILURE') {
                    loginToOCP(cluster: 'azureTI', namespace: 't-core-calc-nonprod', token: 'OpenShift4Token')
                    script {
                        withCredentials([file(credentialsId: 'test-users', variable: 'TEST_USERS_FILE')]) {
                            withCredentials([file(credentialsId: 'persistence.xml', variable: 'PERSISTENCE_XML_FILE')]) {
                                sh """
                                oc port-forward svc/coca-logstash-haproxy 8181:8181 &
                                sleep 10

                                export TRACKING_ID="\$BUILD_NUMBER/modular-fix-price-and-nte-tests"

                                mv ${TEST_USERS_FILE} ./src/main/resources/_testdata.properties
                                mv ${PERSISTENCE_XML_FILE} ./src/main/resources/META-INF/persistence.xml

                                mvn clean -Dsuite=modular-fix-price-and-nte test
                            """
                            }
                        }
                    }
                }
            }
        }
    }

    post {
        always {
            script {
                withCredentials([usernamePassword(credentialsId: 'backend-db-test-username-password', usernameVariable: 'DB_USERNAME', passwordVariable: 'DB_PASSWORD')]) {
                    sh 'az login --service-principal --username ${DB_USERNAME} --password ${DB_PASSWORD} --tenant ${TENANT_ID}'
                    sh 'az sql db show --server ${DB_SERVER} --name ${DB_NAME} --resource-group ${RESOURCE_GROUP} --query "sku.capacity" -o table'
                    sh 'az sql db update --server ${DB_SERVER} --name ${DB_NAME} --resource-group ${RESOURCE_GROUP} --service-objective S3'
                    sh 'az sql db show --server ${DB_SERVER} --name ${DB_NAME} --resource-group ${RESOURCE_GROUP} --query "sku.capacity" -o table'
                }
            }
            script {
                currentBuild.result = currentBuild.result ?: 'SUCCESS'
                notifyGitHub()
            }
        }
        cleanup {
            cleanWs()
        }
    }
}